import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { generateCertificatePDF } from '@/lib/certificate-generator'

const generateCertificateSchema = z.object({
  studentId: z.string().min(1, 'Student ID is required'),
  templateId: z.string().optional(), // If not provided, use active template
  overrideRequirements: z.boolean().default(false) // Instructor can override requirements
})

const bulkGenerateSchema = z.object({
  studentIds: z.array(z.string()).min(1, 'At least one student ID is required'),
  templateId: z.string().optional(),
  overrideRequirements: z.boolean().default(false)
})

// POST /api/courses/[courseId]/certificates/generate - Generate certificate for student
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: generateCertificateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-3, -2)[0]
      const { studentId, templateId, overrideRequirements } = validatedBody

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get student enrollment
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: studentId,
            courseId
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          }
        }
      })

      if (!enrollment || enrollment.status !== 'ACTIVE') {
        return APIResponse.error('Student not enrolled in course', 404)
      }

      // Get certificate template
      let template
      if (templateId) {
        template = await prisma.certificateTemplate.findUnique({
          where: { 
            id: templateId,
            courseId
          }
        })
      } else {
        template = await prisma.certificateTemplate.findFirst({
          where: { 
            courseId,
            isActive: true
          }
        })
      }

      if (!template) {
        return APIResponse.error('No certificate template found', 404)
      }

      // Check if certificate already exists
      const existingCertificate = await prisma.courseCertificate.findUnique({
        where: {
          studentId_courseId_templateId: {
            studentId,
            courseId,
            templateId: template.id
          }
        }
      })

      if (existingCertificate) {
        return APIResponse.error('Certificate already issued for this student', 400)
      }

      // Check completion requirements (unless overridden)
      if (!overrideRequirements) {
        const progress = await calculateStudentProgress(studentId, courseId)
        
        if (!meetsRequirements(progress, template.requirements)) {
          return APIResponse.error(
            'Student does not meet certificate requirements',
            400,
            { requirements: template.requirements, progress }
          )
        }
      }

      // Generate certificate ID
      const certificateId = `CERT-${courseId.slice(-8).toUpperCase()}-${studentId.slice(-8).toUpperCase()}-${Date.now()}`

      // Create certificate record
      const certificate = await prisma.courseCertificate.create({
        data: {
          id: certificateId,
          studentId,
          courseId,
          templateId: template.id,
          issuedAt: new Date(),
          certificateData: {
            studentName: enrollment.user.name,
            courseName: course.title,
            instructorName: course.instructor.name,
            completionDate: new Date().toISOString(),
            certificateId,
            template: template
          }
        },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          template: true
        }
      })

      // Generate PDF (this would be implemented separately)
      try {
        const pdfBuffer = await generateCertificatePDF(certificate)
        
        // Store PDF URL (would upload to cloud storage in production)
        await prisma.courseCertificate.update({
          where: { id: certificateId },
          data: { 
            pdfUrl: `/certificates/${certificateId}.pdf` // Placeholder URL
          }
        })
      } catch (pdfError) {
        console.error('Error generating PDF:', pdfError)
        // Continue without PDF for now
      }

      return APIResponse.success({
        message: 'Certificate generated successfully',
        certificate: {
          ...certificate,
          pdfUrl: `/certificates/${certificateId}.pdf`
        }
      })

    } catch (error) {
      console.error('Error generating certificate:', error)
      return APIResponse.error(
        'Failed to generate certificate: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/courses/[courseId]/certificates/generate - Bulk generate certificates
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: bulkGenerateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-3, -2)[0]
      const { studentIds, templateId, overrideRequirements } = validatedBody

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get certificate template
      let template
      if (templateId) {
        template = await prisma.certificateTemplate.findUnique({
          where: { 
            id: templateId,
            courseId
          }
        })
      } else {
        template = await prisma.certificateTemplate.findFirst({
          where: { 
            courseId,
            isActive: true
          }
        })
      }

      if (!template) {
        return APIResponse.error('No certificate template found', 404)
      }

      const results = {
        successful: [] as string[],
        failed: [] as { studentId: string; reason: string }[],
        skipped: [] as { studentId: string; reason: string }[]
      }

      // Process each student
      for (const studentId of studentIds) {
        try {
          // Check enrollment
          const enrollment = await prisma.courseEnrollment.findUnique({
            where: {
              userId_courseId: {
                userId: studentId,
                courseId
              }
            },
            include: {
              user: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          })

          if (!enrollment || enrollment.status !== 'ACTIVE') {
            results.failed.push({
              studentId,
              reason: 'Student not enrolled in course'
            })
            continue
          }

          // Check if certificate already exists
          const existingCertificate = await prisma.courseCertificate.findUnique({
            where: {
              studentId_courseId_templateId: {
                studentId,
                courseId,
                templateId: template.id
              }
            }
          })

          if (existingCertificate) {
            results.skipped.push({
              studentId,
              reason: 'Certificate already issued'
            })
            continue
          }

          // Check requirements
          if (!overrideRequirements) {
            const progress = await calculateStudentProgress(studentId, courseId)
            
            if (!meetsRequirements(progress, template.requirements)) {
              results.failed.push({
                studentId,
                reason: 'Does not meet certificate requirements'
              })
              continue
            }
          }

          // Generate certificate
          const certificateId = `CERT-${courseId.slice(-8).toUpperCase()}-${studentId.slice(-8).toUpperCase()}-${Date.now()}`

          await prisma.courseCertificate.create({
            data: {
              id: certificateId,
              studentId,
              courseId,
              templateId: template.id,
              issuedAt: new Date(),
              certificateData: {
                studentName: enrollment.user.name,
                courseName: course.title,
                completionDate: new Date().toISOString(),
                certificateId
              }
            }
          })

          results.successful.push(studentId)

        } catch (error) {
          results.failed.push({
            studentId,
            reason: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      return APIResponse.success({
        message: 'Bulk certificate generation completed',
        results: {
          total: studentIds.length,
          successful: results.successful.length,
          failed: results.failed.length,
          skipped: results.skipped.length,
          details: results
        }
      })

    } catch (error) {
      console.error('Error bulk generating certificates:', error)
      return APIResponse.error(
        'Failed to bulk generate certificates: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// Helper functions
async function calculateStudentProgress(studentId: string, courseId: string) {
  // Get course structure
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    include: {
      sections: {
        include: {
          topics: true
        }
      },
      quizzes: {
        where: { isPublished: true }
      }
    }
  })

  if (!course) return null

  const totalTopics = course.sections.reduce((sum, section) => sum + section.topics.length, 0)
  
  // Get completed topics
  const completedTopics = await prisma.topicProgress.count({
    where: {
      userId: studentId,
      topic: {
        section: {
          courseId
        }
      },
      isCompleted: true
    }
  })

  // Get quiz attempts
  const quizAttempts = await prisma.courseQuizAttempt.findMany({
    where: {
      userId: studentId,
      quiz: {
        courseId
      },
      completedAt: { not: null }
    },
    include: {
      quiz: true
    }
  })

  const completionPercentage = totalTopics > 0 ? (completedTopics / totalTopics) * 100 : 0
  const passedQuizzes = quizAttempts.filter(attempt => attempt.isPassed)
  const averageQuizScore = quizAttempts.length > 0 
    ? quizAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / quizAttempts.length 
    : 0

  return {
    completionPercentage,
    totalQuizzes: course.quizzes.length,
    passedQuizzes: passedQuizzes.length,
    averageQuizScore,
    allQuizzesPassed: course.quizzes.length > 0 && passedQuizzes.length === course.quizzes.length
  }
}

function meetsRequirements(progress: any, requirements: any) {
  if (!progress) return false

  // Check completion percentage
  if (progress.completionPercentage < requirements.minCompletionPercentage) {
    return false
  }

  // Check quiz requirements
  if (requirements.requireAllQuizzesPassed && !progress.allQuizzesPassed) {
    return false
  }

  if (progress.averageQuizScore < requirements.minQuizScore) {
    return false
  }

  return true
}
