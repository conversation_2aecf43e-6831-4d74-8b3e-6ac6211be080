/**
 * Video Upload Utility
 * 
 * This module provides utilities for handling video uploads to <PERSON> CDN
 * with progress tracking and validation.
 */

import { uploadVideoToBunny, uploadDocumentToBunny, BunnyApiError } from './bunny-api'
import { isValidVideoFile, formatFileSize, formatDuration } from './bunny-config'

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
  speed?: number // bytes per second
  timeRemaining?: number // seconds
}

export interface VideoUploadResult {
  success: boolean
  videoUrl?: string
  thumbnailUrl?: string
  duration?: number
  fileSize: number
  error?: string
}

export interface DocumentUploadResult {
  success: boolean
  fileUrl?: string
  fileName: string
  fileSize: number
  mimeType: string
  error?: string
}

export class VideoUploadManager {
  private onProgress?: (progress: UploadProgress) => void
  private onComplete?: (result: VideoUploadResult) => void
  private onError?: (error: string) => void
  private abortController?: AbortController

  constructor(callbacks?: {
    onProgress?: (progress: UploadProgress) => void
    onComplete?: (result: VideoUploadResult) => void
    onError?: (error: string) => void
  }) {
    this.onProgress = callbacks?.onProgress
    this.onComplete = callbacks?.onComplete
    this.onError = callbacks?.onError
  }

  /**
   * Upload video file with progress tracking
   */
  async uploadVideo(
    file: File,
    courseId: string,
    sectionId: string,
    topicId: string
  ): Promise<VideoUploadResult> {
    try {
      // Validate file
      if (!isValidVideoFile(file.name)) {
        const error = 'Invalid video file format. Please upload MP4, MOV, AVI, MKV, WebM, or M4V files.'
        this.onError?.(error)
        return { success: false, fileSize: file.size, error }
      }

      // Check file size (limit to 2GB)
      const maxSize = 2 * 1024 * 1024 * 1024 // 2GB
      if (file.size > maxSize) {
        const error = `File size too large. Maximum allowed size is ${formatFileSize(maxSize)}.`
        this.onError?.(error)
        return { success: false, fileSize: file.size, error }
      }

      this.abortController = new AbortController()

      // Start upload with progress tracking
      const startTime = Date.now()
      let lastLoaded = 0
      let lastTime = startTime

      const result = await this.uploadWithProgress(
        file,
        courseId,
        sectionId,
        topicId,
        (loaded, total) => {
          const now = Date.now()
          const timeDiff = (now - lastTime) / 1000 // seconds
          const loadedDiff = loaded - lastLoaded

          const speed = timeDiff > 0 ? loadedDiff / timeDiff : 0
          const percentage = Math.round((loaded / total) * 100)
          const timeRemaining = speed > 0 ? (total - loaded) / speed : 0

          this.onProgress?.({
            loaded,
            total,
            percentage,
            speed,
            timeRemaining
          })

          lastLoaded = loaded
          lastTime = now
        }
      )

      this.onComplete?.(result)
      return result

    } catch (error) {
      const errorMessage = error instanceof BunnyApiError 
        ? error.message 
        : 'Failed to upload video'
      
      this.onError?.(errorMessage)
      return { 
        success: false, 
        fileSize: file.size, 
        error: errorMessage 
      }
    }
  }

  /**
   * Upload document/resource file
   */
  async uploadDocument(
    file: File,
    courseId: string
  ): Promise<DocumentUploadResult> {
    try {
      // Check file size (limit to 100MB for documents)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        const error = `File size too large. Maximum allowed size is ${formatFileSize(maxSize)}.`
        return { 
          success: false, 
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          error 
        }
      }

      const result = await uploadDocumentToBunny(file, courseId, file.name)

      return {
        success: result.success,
        fileUrl: result.fileUrl,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type
      }

    } catch (error) {
      const errorMessage = error instanceof BunnyApiError 
        ? error.message 
        : 'Failed to upload document'
      
      return { 
        success: false, 
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        error: errorMessage 
      }
    }
  }

  /**
   * Cancel ongoing upload
   */
  cancelUpload(): void {
    if (this.abortController) {
      this.abortController.abort()
      this.onError?.('Upload cancelled by user')
    }
  }

  /**
   * Upload with progress tracking (simplified implementation)
   */
  private async uploadWithProgress(
    file: File,
    courseId: string,
    sectionId: string,
    topicId: string,
    onProgress: (loaded: number, total: number) => void
  ): Promise<VideoUploadResult> {
    // Simulate progress for now - in production, implement actual progress tracking
    const total = file.size
    let loaded = 0

    const progressInterval = setInterval(() => {
      loaded = Math.min(loaded + total * 0.1, total * 0.9)
      onProgress(loaded, total)
    }, 500)

    try {
      const result = await uploadVideoToBunny(file, courseId, sectionId, topicId, file.name)
      
      clearInterval(progressInterval)
      onProgress(total, total) // Complete

      return {
        success: result.success,
        videoUrl: result.videoUrl,
        thumbnailUrl: result.thumbnailUrl,
        duration: result.duration,
        fileSize: file.size
      }

    } catch (error) {
      clearInterval(progressInterval)
      throw error
    }
  }
}

/**
 * Validate video file before upload
 */
export function validateVideoFile(file: File): { valid: boolean; error?: string } {
  if (!isValidVideoFile(file.name)) {
    return {
      valid: false,
      error: 'Invalid video file format. Please upload MP4, MOV, AVI, MKV, WebM, or M4V files.'
    }
  }

  const maxSize = 2 * 1024 * 1024 * 1024 // 2GB
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size too large. Maximum allowed size is ${formatFileSize(maxSize)}.`
    }
  }

  return { valid: true }
}

/**
 * Get video metadata from file
 */
export function getVideoMetadata(file: File): Promise<{
  duration: number
  width: number
  height: number
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    video.preload = 'metadata'

    video.onloadedmetadata = () => {
      resolve({
        duration: Math.round(video.duration),
        width: video.videoWidth,
        height: video.videoHeight
      })
    }

    video.onerror = () => {
      reject(new Error('Failed to load video metadata'))
    }

    video.src = URL.createObjectURL(file)
  })
}

/**
 * Create video thumbnail from file
 */
export function createVideoThumbnail(file: File, timeOffset = 10): Promise<string> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.onloadedmetadata = () => {
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      video.currentTime = Math.min(timeOffset, video.duration / 2)
    }

    video.onseeked = () => {
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
      const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)
      resolve(thumbnailDataUrl)
    }

    video.onerror = () => {
      reject(new Error('Failed to create video thumbnail'))
    }

    video.src = URL.createObjectURL(file)
  })
}
