'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  BookOpen, 
  Play, 
  Clock, 
  Users, 
  Award, 
  MessageSquare,
  CheckCircle,
  BarChart3,
  Calendar,
  Target,
  TrendingUp,
  Star,
  Download,
  Share2,
  Eye,
  ArrowRight,
  Trophy,
  Zap
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface CourseProgress {
  courseId: string
  title: string
  description: string
  thumbnailImage?: string
  instructor: {
    id: string
    name: string
    image?: string
  }
  progress: {
    completedTopics: number
    totalTopics: number
    completionPercentage: number
    timeSpent: number // minutes
    lastAccessedAt: string
    currentTopic?: {
      id: string
      title: string
      sectionTitle: string
    }
  }
  stats: {
    totalQuizzes: number
    completedQuizzes: number
    averageQuizScore: number
    discussionPosts: number
    certificates: number
  }
  nextMilestone?: {
    type: 'quiz' | 'assignment' | 'certificate' | 'completion'
    title: string
    description: string
    progress: number
  }
}

interface EnhancedCourseDashboardProps {
  courses: CourseProgress[]
  onCourseSelect: (courseId: string) => void
  className?: string
}

export function EnhancedCourseDashboard({ 
  courses, 
  onCourseSelect, 
  className = '' 
}: EnhancedCourseDashboardProps) {
  const [selectedView, setSelectedView] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'recent' | 'progress' | 'name'>('recent')
  const [filterStatus, setFilterStatus] = useState<'all' | 'in-progress' | 'completed'>('all')

  // Calculate overall stats
  const overallStats = courses.reduce((acc, course) => {
    acc.totalCourses += 1
    acc.completedTopics += course.progress.completedTopics
    acc.totalTopics += course.progress.totalTopics
    acc.totalTimeSpent += course.progress.timeSpent
    acc.totalCertificates += course.stats.certificates
    
    if (course.progress.completionPercentage === 100) {
      acc.completedCourses += 1
    } else if (course.progress.completionPercentage > 0) {
      acc.inProgressCourses += 1
    }
    
    return acc
  }, {
    totalCourses: 0,
    completedCourses: 0,
    inProgressCourses: 0,
    completedTopics: 0,
    totalTopics: 0,
    totalTimeSpent: 0,
    totalCertificates: 0
  })

  const overallProgress = overallStats.totalTopics > 0 
    ? (overallStats.completedTopics / overallStats.totalTopics) * 100 
    : 0

  // Filter and sort courses
  const filteredCourses = courses
    .filter(course => {
      switch (filterStatus) {
        case 'completed':
          return course.progress.completionPercentage === 100
        case 'in-progress':
          return course.progress.completionPercentage > 0 && course.progress.completionPercentage < 100
        default:
          return true
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'progress':
          return b.progress.completionPercentage - a.progress.completionPercentage
        case 'name':
          return a.title.localeCompare(b.title)
        default: // recent
          return new Date(b.progress.lastAccessedAt).getTime() - new Date(a.progress.lastAccessedAt).getTime()
      }
    })

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Overall Stats */}
      <div className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">My Learning Dashboard</h1>
            <p className="text-violet-100 mt-1">
              Track your progress and continue your learning journey
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{Math.round(overallProgress)}%</div>
            <div className="text-violet-100 text-sm">Overall Progress</div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <BookOpen className="w-5 h-5" />
              <span className="font-medium">Courses</span>
            </div>
            <div className="text-2xl font-bold">{overallStats.totalCourses}</div>
            <div className="text-violet-100 text-sm">
              {overallStats.completedCourses} completed
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="w-5 h-5" />
              <span className="font-medium">Topics</span>
            </div>
            <div className="text-2xl font-bold">{overallStats.completedTopics}</div>
            <div className="text-violet-100 text-sm">
              of {overallStats.totalTopics} total
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Clock className="w-5 h-5" />
              <span className="font-medium">Time Spent</span>
            </div>
            <div className="text-2xl font-bold">{Math.round(overallStats.totalTimeSpent / 60)}h</div>
            <div className="text-violet-100 text-sm">
              {overallStats.totalTimeSpent % 60}m this week
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Award className="w-5 h-5" />
              <span className="font-medium">Certificates</span>
            </div>
            <div className="text-2xl font-bold">{overallStats.totalCertificates}</div>
            <div className="text-violet-100 text-sm">earned</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4 text-left hover:shadow-lg transition-all duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="font-semibold text-gray-900 dark:text-white">Continue Learning</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Resume where you left off
              </div>
            </div>
          </div>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4 text-left hover:shadow-lg transition-all duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="font-semibold text-gray-900 dark:text-white">Join Discussions</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Connect with classmates
              </div>
            </div>
          </div>
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4 text-left hover:shadow-lg transition-all duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="font-semibold text-gray-900 dark:text-white">View Certificates</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {overallStats.totalCertificates} earned
              </div>
            </div>
          </div>
        </motion.button>
      </div>

      {/* Filters and View Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Filter:
            </label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">All Courses</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Sort by:
            </label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="recent">Recently Accessed</option>
              <option value="progress">Progress</option>
              <option value="name">Name</option>
            </select>
          </div>
        </div>

        <div className="flex items-center space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setSelectedView('grid')}
            className={`p-2 rounded-md transition-colors ${
              selectedView === 'grid'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
              <div className="bg-current rounded-sm"></div>
              <div className="bg-current rounded-sm"></div>
              <div className="bg-current rounded-sm"></div>
              <div className="bg-current rounded-sm"></div>
            </div>
          </button>
          <button
            onClick={() => setSelectedView('list')}
            className={`p-2 rounded-md transition-colors ${
              selectedView === 'list'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400'
            }`}
          >
            <div className="w-4 h-4 flex flex-col space-y-1">
              <div className="h-0.5 bg-current rounded"></div>
              <div className="h-0.5 bg-current rounded"></div>
              <div className="h-0.5 bg-current rounded"></div>
            </div>
          </button>
        </div>
      </div>

      {/* Courses Display */}
      <AnimatePresence mode="wait">
        {selectedView === 'grid' ? (
          <motion.div
            key="grid"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.courseId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <CourseCard
                  course={course}
                  onSelect={() => onCourseSelect(course.courseId)}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            key="list"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            {filteredCourses.map((course, index) => (
              <motion.div
                key={course.courseId}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <CourseListItem
                  course={course}
                  onSelect={() => onCourseSelect(course.courseId)}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {filteredCourses.length === 0 && (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No Courses Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {filterStatus === 'all' 
              ? "You haven't enrolled in any courses yet"
              : `No ${filterStatus.replace('-', ' ')} courses found`
            }
          </p>
        </div>
      )}
    </div>
  )
}

// Course Card Component
interface CourseCardProps {
  course: CourseProgress
  onSelect: () => void
}

function CourseCard({ course, onSelect }: CourseCardProps) {
  return (
    <motion.div
      whileHover={{ y: -4 }}
      className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer"
      onClick={onSelect}
    >
      {/* Course Thumbnail */}
      <div className="relative h-48 bg-gradient-to-br from-violet-500 to-purple-600">
        {course.thumbnailImage ? (
          <img
            src={course.thumbnailImage}
            alt={course.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <BookOpen className="w-16 h-16 text-white/50" />
          </div>
        )}
        
        {/* Progress Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm p-3">
          <div className="flex items-center justify-between text-white text-sm mb-2">
            <span>{Math.round(course.progress.completionPercentage)}% Complete</span>
            <span>{course.progress.completedTopics}/{course.progress.totalTopics} topics</span>
          </div>
          <div className="w-full bg-white/20 rounded-full h-2">
            <motion.div
              className="bg-white h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${course.progress.completionPercentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>

        {/* Certificate Badge */}
        {course.stats.certificates > 0 && (
          <div className="absolute top-3 right-3">
            <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
              <Award className="w-4 h-4 text-white" />
            </div>
          </div>
        )}
      </div>

      {/* Course Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
          {course.title}
        </h3>

        <div className="flex items-center space-x-2 mb-3">
          {course.instructor.image ? (
            <img
              src={course.instructor.image}
              alt={course.instructor.name}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <Users className="w-3 h-3 text-gray-600 dark:text-gray-400" />
            </div>
          )}
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {course.instructor.name}
          </span>
        </div>

        {/* Current Topic */}
        {course.progress.currentTopic && (
          <div className="mb-3 p-2 bg-violet-50 dark:bg-violet-900/20 rounded-lg">
            <div className="text-xs text-violet-600 dark:text-violet-400 font-medium">
              Continue with:
            </div>
            <div className="text-sm text-gray-900 dark:text-white">
              {course.progress.currentTopic.title}
            </div>
          </div>
        )}

        {/* Next Milestone */}
        {course.nextMilestone && (
          <div className="mb-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                  Next Milestone:
                </div>
                <div className="text-sm text-gray-900 dark:text-white">
                  {course.nextMilestone.title}
                </div>
              </div>
              <div className="text-xs text-green-600 dark:text-green-400 font-bold">
                {course.nextMilestone.progress}%
              </div>
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>{Math.round(course.progress.timeSpent / 60)}h spent</span>
          </div>
          <div className="flex items-center space-x-1">
            <MessageSquare className="w-3 h-3" />
            <span>{course.stats.discussionPosts} posts</span>
          </div>
        </div>

        {/* Last Accessed */}
        <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
          Last accessed {formatDistanceToNow(new Date(course.progress.lastAccessedAt), { addSuffix: true })}
        </div>

        {/* Action Button */}
        <button className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200">
          <Play className="w-4 h-4" />
          <span>Continue Learning</span>
          <ArrowRight className="w-4 h-4" />
        </button>
      </div>
    </motion.div>
  )
}

// Course List Item Component (placeholder)
function CourseListItem({ course, onSelect }: CourseCardProps) {
  return (
    <div
      className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4 hover:shadow-lg transition-all duration-200 cursor-pointer"
      onClick={onSelect}
    >
      <div className="flex items-center space-x-4">
        {/* Thumbnail */}
        <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <BookOpen className="w-8 h-8 text-white" />
          )}
        </div>

        {/* Course Info */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 dark:text-white truncate">
            {course.title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {course.instructor.name}
          </p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
            <span>{Math.round(course.progress.completionPercentage)}% complete</span>
            <span>{course.progress.completedTopics}/{course.progress.totalTopics} topics</span>
            <span>{Math.round(course.progress.timeSpent / 60)}h spent</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-32 flex-shrink-0">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${course.progress.completionPercentage}%` }}
            />
          </div>
        </div>

        {/* Action */}
        <button className="flex items-center space-x-2 px-4 py-2 bg-violet-100 text-violet-700 hover:bg-violet-200 dark:bg-violet-900/20 dark:text-violet-300 dark:hover:bg-violet-900/30 rounded-lg transition-colors flex-shrink-0">
          <Play className="w-4 h-4" />
          <span>Continue</span>
        </button>
      </div>
    </div>
  )
}
