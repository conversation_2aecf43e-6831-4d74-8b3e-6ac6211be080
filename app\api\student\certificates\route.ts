import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  courseId: z.string().optional()
})

// GET /api/student/certificates - Get student's certificates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, courseId } = validatedQuery

      // Build where clause
      const where: any = {
        studentId: user.id
      }

      if (courseId) {
        where.courseId = courseId
      }

      // Get total count
      const total = await prisma.courseCertificate.count({ where })

      // Get certificates
      const certificates = await prisma.courseCertificate.findMany({
        where,
        orderBy: { issuedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          course: {
            select: {
              id: true,
              title: true,
              thumbnailImage: true,
              instructor: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            }
          },
          template: {
            select: {
              id: true,
              name: true,
              design: true,
              content: true
            }
          }
        }
      })

      // Format certificates
      const formattedCertificates = certificates.map(cert => ({
        id: cert.id,
        certificateId: cert.id,
        issuedAt: cert.issuedAt,
        pdfUrl: cert.pdfUrl,
        verificationUrl: `/verify-certificate/${cert.id}`,
        course: cert.course,
        template: cert.template,
        certificateData: cert.certificateData
      }))

      return APIResponse.success({
        certificates: formattedCertificates,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching student certificates:', error)
      return APIResponse.error(
        'Failed to fetch certificates: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
