import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const trendingSchema = commonSchemas.pagination.extend({
  period: z.enum(['day', 'week', 'month', 'year']).default('week'),
  category: z.string().optional()
})

// GET /api/courses/trending - Get trending courses
export const GET = createAPIHandler(
  {
    requireAuth: false,
    validateQuery: trendingSchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    try {
      const { period, category, page = 1, limit = 20 } = validatedQuery

      // Calculate date range based on period
      const now = new Date()
      let startDate: Date

      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      }

      // Build where conditions
      const whereConditions: any = {
        isPublished: true,
        isDeleted: false
      }

      if (category) {
        whereConditions.category = category
      }

      // Get trending courses based on recent enrollments and engagement
      const trendingCourses = await prisma.course.findMany({
        where: whereConditions,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true
            }
          },
          enrollments: {
            where: {
              enrolledAt: {
                gte: startDate
              },
              status: 'ACTIVE'
            },
            select: {
              id: true,
              enrolledAt: true
            }
          },
          reviews: {
            where: {
              createdAt: {
                gte: startDate
              }
            },
            select: {
              id: true,
              rating: true,
              createdAt: true
            }
          },
          _count: {
            select: {
              enrollments: {
                where: { status: 'ACTIVE' }
              },
              reviews: true
            }
          }
        }
      })

      // Calculate trending score
      const coursesWithTrendingScore = trendingCourses.map(course => {
        const recentEnrollments = course.enrollments.length
        const recentReviews = course.reviews.length
        const avgRecentRating = course.reviews.length > 0 
          ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length
          : 0

        // Trending score calculation
        // Weight: 60% recent enrollments, 25% recent reviews, 15% recent rating
        const trendingScore = (recentEnrollments * 0.6) + (recentReviews * 0.25) + (avgRecentRating * 0.15)

        return {
          ...course,
          trendingScore,
          recentEnrollments,
          recentReviews,
          avgRecentRating
        }
      })

      // Sort by trending score and apply pagination
      const sortedCourses = coursesWithTrendingScore
        .sort((a, b) => b.trendingScore - a.trendingScore)
        .slice((page - 1) * limit, page * limit)

      // Format response
      const formattedCourses = sortedCourses.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        shortDescription: course.shortDescription,
        thumbnailImage: course.thumbnailImage,
        price: course.price,
        originalPrice: course.originalPrice,
        currency: course.currency,
        level: course.level,
        category: course.category,
        tags: course.tags,
        language: course.language,
        estimatedDuration: course.estimatedDuration,
        averageRating: course.averageRating,
        reviewCount: course._count.reviews,
        enrollmentCount: course._count.enrollments,
        isFeatured: course.isFeatured,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        instructor: course.instructor,
        trending: {
          score: course.trendingScore,
          recentEnrollments: course.recentEnrollments,
          recentReviews: course.recentReviews,
          avgRecentRating: course.avgRecentRating,
          period
        }
      }))

      const total = coursesWithTrendingScore.length

      return APIResponse.success({
        courses: formattedCourses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        period,
        category
      })

    } catch (error) {
      console.error('Error fetching trending courses:', error)
      return APIResponse.error(
        'Failed to fetch trending courses: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
