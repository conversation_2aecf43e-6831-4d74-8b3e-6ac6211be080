import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const recommendationsSchema = commonSchemas.pagination.extend({
  userId: z.string().optional(),
  courseId: z.string().optional(), // For "similar courses" recommendations
  type: z.enum(['personalized', 'similar', 'popular', 'new']).default('personalized')
})

// GET /api/courses/recommendations - Get course recommendations
export const GET = createAPIHandler(
  {
    requireAuth: false,
    validateQuery: recommendationsSchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const { userId, courseId, type, page = 1, limit = 20 } = validatedQuery
      const targetUserId = userId || user?.id

      let recommendedCourses: any[] = []

      if (type === 'personalized' && targetUserId) {
        recommendedCourses = await getPersonalizedRecommendations(targetUserId, limit, (page - 1) * limit)
      } else if (type === 'similar' && courseId) {
        recommendedCourses = await getSimilarCourses(courseId, limit, (page - 1) * limit)
      } else if (type === 'popular') {
        recommendedCourses = await getPopularCourses(limit, (page - 1) * limit)
      } else if (type === 'new') {
        recommendedCourses = await getNewCourses(limit, (page - 1) * limit)
      } else {
        // Default to popular courses if no user context
        recommendedCourses = await getPopularCourses(limit, (page - 1) * limit)
      }

      // Format response
      const formattedCourses = recommendedCourses.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        shortDescription: course.shortDescription,
        thumbnailImage: course.thumbnailImage,
        price: course.price,
        originalPrice: course.originalPrice,
        currency: course.currency,
        level: course.level,
        category: course.category,
        tags: course.tags,
        language: course.language,
        estimatedDuration: course.estimatedDuration,
        averageRating: course.averageRating,
        reviewCount: course.reviewCount || 0,
        enrollmentCount: course.enrollmentCount || 0,
        isFeatured: course.isFeatured,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        instructor: course.instructor,
        recommendationScore: course.recommendationScore || 0,
        recommendationReason: course.recommendationReason || 'Popular course'
      }))

      return APIResponse.success({
        courses: formattedCourses,
        pagination: {
          page,
          limit,
          total: formattedCourses.length,
          totalPages: Math.ceil(formattedCourses.length / limit),
          hasNext: formattedCourses.length === limit,
          hasPrev: page > 1
        },
        type,
        userId: targetUserId,
        courseId
      })

    } catch (error) {
      console.error('Error fetching recommendations:', error)
      return APIResponse.error(
        'Failed to fetch recommendations: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// Helper function for personalized recommendations
async function getPersonalizedRecommendations(userId: string, limit: number, skip: number) {
  // Get user's enrolled courses and preferences
  const userEnrollments = await prisma.courseEnrollment.findMany({
    where: {
      userId,
      status: 'ACTIVE'
    },
    include: {
      course: {
        select: {
          category: true,
          tags: true,
          level: true,
          instructor: {
            select: { id: true }
          }
        }
      }
    }
  })

  // Extract user preferences
  const enrolledCourseIds = userEnrollments.map(e => e.courseId)
  const preferredCategories = [...new Set(userEnrollments.map(e => e.course.category))]
  const preferredTags = [...new Set(userEnrollments.flatMap(e => e.course.tags))]
  const preferredInstructors = [...new Set(userEnrollments.map(e => e.course.instructor.id))]

  // Build recommendation query
  const recommendations = await prisma.course.findMany({
    where: {
      isPublished: true,
      isDeleted: false,
      id: { notIn: enrolledCourseIds }, // Exclude already enrolled courses
      OR: [
        { category: { in: preferredCategories } },
        { tags: { hasSome: preferredTags } },
        { instructorId: { in: preferredInstructors } }
      ]
    },
    include: {
      instructor: {
        select: {
          id: true,
          name: true,
          image: true,
          bio: true
        }
      },
      _count: {
        select: {
          enrollments: { where: { status: 'ACTIVE' } },
          reviews: true
        }
      }
    },
    take: limit * 2, // Get more to calculate scores
    orderBy: [
      { averageRating: 'desc' },
      { enrollmentCount: 'desc' }
    ]
  })

  // Calculate recommendation scores
  const scoredRecommendations = recommendations.map(course => {
    let score = 0
    let reasons: string[] = []

    // Category match
    if (preferredCategories.includes(course.category)) {
      score += 3
      reasons.push(`Similar to your ${course.category} courses`)
    }

    // Tag matches
    const tagMatches = course.tags.filter(tag => preferredTags.includes(tag)).length
    score += tagMatches * 2
    if (tagMatches > 0) {
      reasons.push(`Matches your interests`)
    }

    // Instructor match
    if (preferredInstructors.includes(course.instructorId)) {
      score += 4
      reasons.push(`From instructor you've learned from`)
    }

    // Quality factors
    score += course.averageRating * 0.5
    score += Math.min(course.enrollmentCount / 100, 2) // Cap enrollment bonus

    return {
      ...course,
      recommendationScore: score,
      recommendationReason: reasons[0] || 'Recommended for you',
      reviewCount: course._count.reviews,
      enrollmentCount: course._count.enrollments
    }
  })

  // Sort by score and apply pagination
  return scoredRecommendations
    .sort((a, b) => b.recommendationScore - a.recommendationScore)
    .slice(skip, skip + limit)
}

// Helper function for similar courses
async function getSimilarCourses(courseId: string, limit: number, skip: number) {
  // Get the reference course
  const referenceCourse = await prisma.course.findUnique({
    where: { id: courseId },
    select: {
      category: true,
      tags: true,
      level: true,
      instructorId: true
    }
  })

  if (!referenceCourse) {
    return []
  }

  // Find similar courses
  const similarCourses = await prisma.course.findMany({
    where: {
      isPublished: true,
      isDeleted: false,
      id: { not: courseId },
      OR: [
        { category: referenceCourse.category },
        { tags: { hasSome: referenceCourse.tags } },
        { level: referenceCourse.level },
        { instructorId: referenceCourse.instructorId }
      ]
    },
    include: {
      instructor: {
        select: {
          id: true,
          name: true,
          image: true,
          bio: true
        }
      },
      _count: {
        select: {
          enrollments: { where: { status: 'ACTIVE' } },
          reviews: true
        }
      }
    },
    take: limit * 2
  })

  // Calculate similarity scores
  const scoredSimilar = similarCourses.map(course => {
    let score = 0
    let reasons: string[] = []

    if (course.category === referenceCourse.category) {
      score += 3
      reasons.push('Same category')
    }

    const tagMatches = course.tags.filter(tag => referenceCourse.tags.includes(tag)).length
    score += tagMatches * 2
    if (tagMatches > 0) {
      reasons.push('Similar topics')
    }

    if (course.level === referenceCourse.level) {
      score += 1
      reasons.push('Same difficulty level')
    }

    if (course.instructorId === referenceCourse.instructorId) {
      score += 2
      reasons.push('Same instructor')
    }

    return {
      ...course,
      recommendationScore: score,
      recommendationReason: reasons[0] || 'Similar course',
      reviewCount: course._count.reviews,
      enrollmentCount: course._count.enrollments
    }
  })

  return scoredSimilar
    .sort((a, b) => b.recommendationScore - a.recommendationScore)
    .slice(skip, skip + limit)
}

// Helper function for popular courses
async function getPopularCourses(limit: number, skip: number) {
  const popularCourses = await prisma.course.findMany({
    where: {
      isPublished: true,
      isDeleted: false
    },
    include: {
      instructor: {
        select: {
          id: true,
          name: true,
          image: true,
          bio: true
        }
      },
      _count: {
        select: {
          enrollments: { where: { status: 'ACTIVE' } },
          reviews: true
        }
      }
    },
    orderBy: [
      { enrollmentCount: 'desc' },
      { averageRating: 'desc' }
    ],
    skip,
    take: limit
  })

  return popularCourses.map(course => ({
    ...course,
    recommendationScore: course.enrollmentCount + (course.averageRating * 10),
    recommendationReason: 'Popular course',
    reviewCount: course._count.reviews,
    enrollmentCount: course._count.enrollments
  }))
}

// Helper function for new courses
async function getNewCourses(limit: number, skip: number) {
  const newCourses = await prisma.course.findMany({
    where: {
      isPublished: true,
      isDeleted: false,
      createdAt: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      }
    },
    include: {
      instructor: {
        select: {
          id: true,
          name: true,
          image: true,
          bio: true
        }
      },
      _count: {
        select: {
          enrollments: { where: { status: 'ACTIVE' } },
          reviews: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    skip,
    take: limit
  })

  return newCourses.map(course => ({
    ...course,
    recommendationScore: 5, // Base score for new courses
    recommendationReason: 'New course',
    reviewCount: course._count.reviews,
    enrollmentCount: course._count.enrollments
  }))
}
