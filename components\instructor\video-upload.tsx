'use client'

import React, { useState, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, X, Play, FileVideo, AlertCircle, CheckCircle, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { validateVideoFile, getVideoMetadata, createVideoThumbnail } from '@/lib/video-upload'
import { formatFileSize, formatDuration } from '@/lib/bunny-config'

interface VideoUploadProps {
  courseId: string
  sectionId: string
  topicId: string
  onUploadComplete?: (content: any) => void
  onUploadError?: (error: string) => void
  className?: string
}

interface UploadProgress {
  loaded: number
  total: number
  percentage: number
  speed?: number
  timeRemaining?: number
}

interface VideoMetadata {
  duration: number
  width: number
  height: number
  thumbnail?: string
}

export function VideoUpload({
  courseId,
  sectionId,
  topicId,
  onUploadComplete,
  onUploadError,
  className = ''
}: VideoUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [videoMetadata, setVideoMetadata] = useState<VideoMetadata | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null)
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      await handleFileSelect(files[0])
    }
  }, [])

  const handleFileInputChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      await handleFileSelect(files[0])
    }
  }, [])

  const handleFileSelect = async (file: File) => {
    // Validate file
    const validation = validateVideoFile(file)
    if (!validation.valid) {
      toast.error(validation.error)
      onUploadError?.(validation.error || 'Invalid file')
      return
    }

    setSelectedFile(file)
    setTitle(file.name.replace(/\.[^/.]+$/, '')) // Remove extension

    try {
      // Get video metadata
      const metadata = await getVideoMetadata(file)
      
      // Generate thumbnail
      const thumbnail = await createVideoThumbnail(file, 10)
      
      setVideoMetadata({
        ...metadata,
        thumbnail
      })
    } catch (error) {
      console.warn('Failed to get video metadata:', error)
      setVideoMetadata({
        duration: 0,
        width: 0,
        height: 0
      })
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    abortControllerRef.current = new AbortController()

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('courseId', courseId)
      formData.append('sectionId', sectionId)
      formData.append('topicId', topicId)
      formData.append('title', title)
      formData.append('description', description)

      // Simulate progress tracking (in production, use actual progress)
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (!prev) {
            return {
              loaded: selectedFile.size * 0.1,
              total: selectedFile.size,
              percentage: 10
            }
          }
          
          const newLoaded = Math.min(prev.loaded + selectedFile.size * 0.1, selectedFile.size * 0.9)
          return {
            ...prev,
            loaded: newLoaded,
            percentage: Math.round((newLoaded / prev.total) * 100)
          }
        })
      }, 1000)

      const response = await fetch('/api/instructor/upload/video', {
        method: 'POST',
        body: formData,
        signal: abortControllerRef.current.signal
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Upload failed')
      }

      const result = await response.json()

      // Complete progress
      setUploadProgress({
        loaded: selectedFile.size,
        total: selectedFile.size,
        percentage: 100
      })

      toast.success('Video uploaded successfully!')
      onUploadComplete?.(result.content)

      // Reset form
      setTimeout(() => {
        setSelectedFile(null)
        setVideoMetadata(null)
        setTitle('')
        setDescription('')
        setUploadProgress(null)
        setIsUploading(false)
      }, 2000)

    } catch (error: any) {
      if (error.name === 'AbortError') {
        toast.info('Upload cancelled')
      } else {
        const errorMessage = error.message || 'Upload failed'
        toast.error(errorMessage)
        onUploadError?.(errorMessage)
      }
      
      setUploadProgress(null)
      setIsUploading(false)
    }
  }

  const handleCancel = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setSelectedFile(null)
    setVideoMetadata(null)
    setTitle('')
    setDescription('')
    setUploadProgress(null)
    setIsUploading(false)
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* File Drop Zone */}
      {!selectedFile && (
        <motion.div
          className={`
            relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
            ${isDragOver 
              ? 'border-violet-400 bg-violet-50 dark:bg-violet-950/20' 
              : 'border-gray-300 dark:border-gray-600 hover:border-violet-300 dark:hover:border-violet-500'
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileInputChange}
            className="hidden"
          />

          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
              <Upload className="w-8 h-8 text-white" />
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Upload Video Content
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Drag and drop your video file here, or click to browse
              </p>
              
              <button
                onClick={openFileDialog}
                className="px-6 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
              >
                Choose File
              </button>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>Supported formats: MP4, MOV, AVI, MKV, WebM, M4V</p>
              <p>Maximum file size: 2GB</p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Selected File Preview */}
      <AnimatePresence>
        {selectedFile && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-start space-x-4">
              {/* Video Thumbnail */}
              <div className="flex-shrink-0">
                {videoMetadata?.thumbnail ? (
                  <img
                    src={videoMetadata.thumbnail}
                    alt="Video thumbnail"
                    className="w-24 h-16 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-24 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <FileVideo className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* File Info */}
              <div className="flex-1 space-y-3">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {selectedFile.name}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
                    <span>{formatFileSize(selectedFile.size)}</span>
                    {videoMetadata?.duration && (
                      <span>{formatDuration(videoMetadata.duration)}</span>
                    )}
                    {videoMetadata?.width && videoMetadata?.height && (
                      <span>{videoMetadata.width}×{videoMetadata.height}</span>
                    )}
                  </div>
                </div>

                {/* Title Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Video Title
                  </label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter video title..."
                  />
                </div>

                {/* Description Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                    placeholder="Enter video description..."
                  />
                </div>
              </div>

              {/* Remove Button */}
              {!isUploading && (
                <button
                  onClick={handleCancel}
                  className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>

            {/* Upload Progress */}
            <AnimatePresence>
              {uploadProgress && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Uploading... {uploadProgress.percentage}%
                    </span>
                    {uploadProgress.percentage === 100 ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <Loader2 className="w-5 h-5 text-violet-500 animate-spin" />
                    )}
                  </div>
                  
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${uploadProgress.percentage}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action Buttons */}
            {!uploadProgress && (
              <div className="flex justify-end space-x-3 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpload}
                  disabled={!title.trim() || isUploading}
                  className="px-6 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  Upload Video
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
