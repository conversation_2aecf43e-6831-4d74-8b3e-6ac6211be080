'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Eye, 
  Clock, 
  Star,
  BookOpen,
  Play,
  MessageCircle,
  Award,
  Calendar,
  BarChart3,
  Loader2
} from 'lucide-react'

interface CourseAnalyticsProps {
  courseId: string
}

interface AnalyticsData {
  overview: {
    totalEnrollments: number
    activeStudents: number
    completionRate: number
    averageRating: number
    totalRevenue: number
    viewCount: number
  }
  engagement: {
    averageWatchTime: number
    mostWatchedLesson: string
    discussionPosts: number
    questionsAsked: number
  }
  performance: {
    enrollmentTrend: Array<{ date: string; count: number }>
    completionTrend: Array<{ date: string; rate: number }>
    revenueTrend: Array<{ date: string; amount: number }>
  }
}

export function CourseAnalytics({ courseId }: CourseAnalyticsProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  // Mock data for now - in production, fetch from API
  useEffect(() => {
    const fetchAnalytics = async () => {
      setIsLoading(true)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock analytics data
      const mockData: AnalyticsData = {
        overview: {
          totalEnrollments: 1247,
          activeStudents: 892,
          completionRate: 68.5,
          averageRating: 4.7,
          totalRevenue: 24850,
          viewCount: 15420
        },
        engagement: {
          averageWatchTime: 42.5,
          mostWatchedLesson: 'Introduction to React Hooks',
          discussionPosts: 156,
          questionsAsked: 89
        },
        performance: {
          enrollmentTrend: [
            { date: '2024-01-01', count: 45 },
            { date: '2024-01-02', count: 52 },
            { date: '2024-01-03', count: 38 },
            { date: '2024-01-04', count: 67 },
            { date: '2024-01-05', count: 71 },
            { date: '2024-01-06', count: 59 },
            { date: '2024-01-07', count: 84 }
          ],
          completionTrend: [
            { date: '2024-01-01', rate: 65.2 },
            { date: '2024-01-02', rate: 67.1 },
            { date: '2024-01-03', rate: 68.5 },
            { date: '2024-01-04', rate: 69.8 },
            { date: '2024-01-05', rate: 68.2 },
            { date: '2024-01-06', rate: 70.1 },
            { date: '2024-01-07', rate: 68.5 }
          ],
          revenueTrend: [
            { date: '2024-01-01', amount: 2450 },
            { date: '2024-01-02', amount: 2890 },
            { date: '2024-01-03', amount: 2120 },
            { date: '2024-01-04', amount: 3670 },
            { date: '2024-01-05', amount: 3920 },
            { date: '2024-01-06', amount: 3240 },
            { date: '2024-01-07', amount: 4560 }
          ]
        }
      }
      
      setAnalyticsData(mockData)
      setIsLoading(false)
    }

    fetchAnalytics()
  }, [courseId, timeRange])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-violet-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading analytics...</p>
        </div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Analytics Data
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Analytics data will appear here once students start enrolling
        </p>
      </div>
    )
  }

  const { overview, engagement } = analyticsData

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Course Analytics
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track your course performance and student engagement
          </p>
        </div>

        {/* Time Range Selector */}
        <div className="flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg p-1 border border-gray-200 dark:border-gray-700">
          {[
            { value: '7d', label: '7 Days' },
            { value: '30d', label: '30 Days' },
            { value: '90d', label: '90 Days' },
            { value: '1y', label: '1 Year' }
          ].map((range) => (
            <button
              key={range.value}
              onClick={() => setTimeRange(range.value as any)}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                timeRange === range.value
                  ? 'bg-violet-600 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Total Enrollments */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Enrollments
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                {overview.totalEnrollments.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="flex items-center mt-4 text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 dark:text-green-400 font-medium">+12.5%</span>
            <span className="text-gray-500 dark:text-gray-400 ml-1">vs last month</span>
          </div>
        </motion.div>

        {/* Active Students */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Students
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                {overview.activeStudents.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <Play className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="flex items-center mt-4 text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              {((overview.activeStudents / overview.totalEnrollments) * 100).toFixed(1)}% of total enrollments
            </span>
          </div>
        </motion.div>

        {/* Completion Rate */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Completion Rate
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                {overview.completionRate}%
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-4">
            <motion.div
              className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${overview.completionRate}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </motion.div>

        {/* Average Rating */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Average Rating
              </p>
              <div className="flex items-center mt-1">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {overview.averageRating}
                </p>
                <div className="flex items-center ml-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(overview.averageRating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-white" />
            </div>
          </div>
        </motion.div>

        {/* Total Revenue */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Revenue
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                ${overview.totalRevenue.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="flex items-center mt-4 text-sm">
            <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
            <span className="text-green-600 dark:text-green-400 font-medium">+8.2%</span>
            <span className="text-gray-500 dark:text-gray-400 ml-1">vs last month</span>
          </div>
        </motion.div>

        {/* View Count */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Course Views
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                {overview.viewCount.toLocaleString()}
              </p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6 text-white" />
            </div>
          </div>
          <div className="flex items-center mt-4 text-sm">
            <span className="text-gray-600 dark:text-gray-400">
              {((overview.totalEnrollments / overview.viewCount) * 100).toFixed(1)}% conversion rate
            </span>
          </div>
        </motion.div>
      </div>

      {/* Engagement Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Student Engagement
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Clock className="w-5 h-5 text-violet-500" />
                <span className="text-gray-700 dark:text-gray-300">Average Watch Time</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {engagement.averageWatchTime} min
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BookOpen className="w-5 h-5 text-blue-500" />
                <span className="text-gray-700 dark:text-gray-300">Most Watched</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white text-right max-w-xs truncate">
                {engagement.mostWatchedLesson}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageCircle className="w-5 h-5 text-green-500" />
                <span className="text-gray-700 dark:text-gray-300">Discussion Posts</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {engagement.discussionPosts}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-orange-500" />
                <span className="text-gray-700 dark:text-gray-300">Questions Asked</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">
                {engagement.questionsAsked}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg bg-violet-50 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300 hover:bg-violet-100 dark:hover:bg-violet-900/30 transition-colors">
              View Student Feedback
            </button>
            <button className="w-full text-left p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
              Export Analytics Report
            </button>
            <button className="w-full text-left p-3 rounded-lg bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
              Send Course Update
            </button>
            <button className="w-full text-left p-3 rounded-lg bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors">
              Manage Discussions
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
