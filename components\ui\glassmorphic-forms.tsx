'use client'

import React, { forwardRef, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Eye, EyeOff, ChevronDown, Check, X, Search, Calendar } from 'lucide-react'
import { GlassmorphicCard } from './glassmorphic-card'

// Base Input Component
interface GlassmorphicInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  hint?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  variant?: 'default' | 'filled' | 'outlined'
  glassEffect?: boolean
}

export const GlassmorphicInput = forwardRef<HTMLInputElement, GlassmorphicInputProps>(
  ({ 
    label, 
    error, 
    hint, 
    leftIcon, 
    rightIcon, 
    variant = 'default',
    glassEffect = true,
    className, 
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false)

    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
              {leftIcon}
            </div>
          )}
          
          <motion.input
            ref={ref}
            className={cn(
              'w-full px-4 py-3 rounded-xl transition-all duration-200',
              'text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-0',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              
              // Glass effect
              glassEffect && 'bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10',
              
              // Variant styles
              variant === 'filled' && 'bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
              variant === 'outlined' && 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
              
              // Icon padding
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              
              // Error state
              error && 'border-red-500 focus:ring-red-500',
              
              className
            )}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            whileFocus={{ scale: 1.02 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400">
              {rightIcon}
            </div>
          )}
          
          {/* Focus ring effect */}
          <AnimatePresence>
            {isFocused && (
              <motion.div
                className="absolute inset-0 rounded-xl border-2 border-violet-500/50 pointer-events-none"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </AnimatePresence>
        </div>
        
        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.p
              className="text-sm text-red-500 flex items-center space-x-1"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <X className="w-4 h-4" />
              <span>{error}</span>
            </motion.p>
          )}
        </AnimatePresence>
        
        {/* Hint */}
        {hint && !error && (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {hint}
          </p>
        )}
      </div>
    )
  }
)

GlassmorphicInput.displayName = 'GlassmorphicInput'

// Password Input
export const GlassmorphicPasswordInput = forwardRef<HTMLInputElement, Omit<GlassmorphicInputProps, 'type' | 'rightIcon'>>(
  (props, ref) => {
    const [showPassword, setShowPassword] = useState(false)

    return (
      <GlassmorphicInput
        ref={ref}
        type={showPassword ? 'text' : 'password'}
        rightIcon={
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
        }
        {...props}
      />
    )
  }
)

GlassmorphicPasswordInput.displayName = 'GlassmorphicPasswordInput'

// Search Input
export const GlassmorphicSearchInput = forwardRef<HTMLInputElement, Omit<GlassmorphicInputProps, 'leftIcon'>>(
  (props, ref) => {
    return (
      <GlassmorphicInput
        ref={ref}
        type="search"
        leftIcon={<Search className="w-4 h-4" />}
        placeholder="Search..."
        {...props}
      />
    )
  }
)

GlassmorphicSearchInput.displayName = 'GlassmorphicSearchInput'

// Textarea Component
interface GlassmorphicTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  hint?: string
  variant?: 'default' | 'filled' | 'outlined'
  glassEffect?: boolean
  autoResize?: boolean
}

export const GlassmorphicTextarea = forwardRef<HTMLTextAreaElement, GlassmorphicTextareaProps>(
  ({ 
    label, 
    error, 
    hint, 
    variant = 'default',
    glassEffect = true,
    autoResize = false,
    className, 
    ...props 
  }, ref) => {
    const [isFocused, setIsFocused] = useState(false)

    return (
      <div className="space-y-2">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          <motion.textarea
            ref={ref}
            className={cn(
              'w-full px-4 py-3 rounded-xl transition-all duration-200 resize-none',
              'text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-0',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              
              // Glass effect
              glassEffect && 'bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10',
              
              // Variant styles
              variant === 'filled' && 'bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
              variant === 'outlined' && 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
              
              // Error state
              error && 'border-red-500 focus:ring-red-500',
              
              // Auto resize
              autoResize && 'resize-none overflow-hidden',
              
              className
            )}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            whileFocus={{ scale: 1.02 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            {...props}
          />
          
          {/* Focus ring effect */}
          <AnimatePresence>
            {isFocused && (
              <motion.div
                className="absolute inset-0 rounded-xl border-2 border-violet-500/50 pointer-events-none"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              />
            )}
          </AnimatePresence>
        </div>
        
        {/* Error message */}
        <AnimatePresence>
          {error && (
            <motion.p
              className="text-sm text-red-500 flex items-center space-x-1"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <X className="w-4 h-4" />
              <span>{error}</span>
            </motion.p>
          )}
        </AnimatePresence>
        
        {/* Hint */}
        {hint && !error && (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {hint}
          </p>
        )}
      </div>
    )
  }
)

GlassmorphicTextarea.displayName = 'GlassmorphicTextarea'

// Select Component
interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

interface GlassmorphicSelectProps {
  label?: string
  error?: string
  hint?: string
  options: SelectOption[]
  value?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  variant?: 'default' | 'filled' | 'outlined'
  glassEffect?: boolean
  onChange?: (value: string) => void
  className?: string
}

export function GlassmorphicSelect({
  label,
  error,
  hint,
  options,
  value,
  placeholder = 'Select an option',
  disabled = false,
  required = false,
  variant = 'default',
  glassEffect = true,
  onChange,
  className
}: GlassmorphicSelectProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedOption, setSelectedOption] = useState<SelectOption | null>(
    options.find(opt => opt.value === value) || null
  )

  const handleSelect = (option: SelectOption) => {
    if (option.disabled) return
    
    setSelectedOption(option)
    onChange?.(option.value)
    setIsOpen(false)
  }

  return (
    <div className="space-y-2 relative">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <motion.button
          type="button"
          className={cn(
            'w-full px-4 py-3 rounded-xl transition-all duration-200 text-left',
            'text-gray-900 dark:text-white',
            'focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-0',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'flex items-center justify-between',
            
            // Glass effect
            glassEffect && 'bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10',
            
            // Variant styles
            variant === 'filled' && 'bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
            variant === 'outlined' && 'bg-transparent border-2 border-gray-300 dark:border-gray-600',
            
            // Error state
            error && 'border-red-500 focus:ring-red-500',
            
            className
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          whileHover={!disabled ? { scale: 1.02 } : undefined}
          whileTap={!disabled ? { scale: 0.98 } : undefined}
        >
          <span className={selectedOption ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </motion.div>
        </motion.button>
        
        {/* Dropdown */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute top-full left-0 right-0 mt-2 z-50"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <GlassmorphicCard variant="heavy" className="py-2 max-h-60 overflow-y-auto">
                {options.map((option) => (
                  <motion.button
                    key={option.value}
                    type="button"
                    className={cn(
                      'w-full px-4 py-3 text-left transition-colors',
                      'hover:bg-white/10 dark:hover:bg-white/5',
                      'disabled:opacity-50 disabled:cursor-not-allowed',
                      'flex items-center justify-between',
                      selectedOption?.value === option.value && 'bg-violet-500/20 text-violet-600 dark:text-violet-400'
                    )}
                    onClick={() => handleSelect(option)}
                    disabled={option.disabled}
                    whileHover={!option.disabled ? { x: 4 } : undefined}
                  >
                    <span>{option.label}</span>
                    {selectedOption?.value === option.value && (
                      <Check className="w-4 h-4" />
                    )}
                  </motion.button>
                ))}
              </GlassmorphicCard>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Error message */}
      <AnimatePresence>
        {error && (
          <motion.p
            className="text-sm text-red-500 flex items-center space-x-1"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <X className="w-4 h-4" />
            <span>{error}</span>
          </motion.p>
        )}
      </AnimatePresence>
      
      {/* Hint */}
      {hint && !error && (
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {hint}
        </p>
      )}
    </div>
  )
}
