'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  BellRing, 
  Check, 
  <PERSON><PERSON>heck, 
  X, 
  <PERSON>tings, 
  Filter,
  MoreHorizontal,
  Trash2,
  Archive,
  Star,
  Clock,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Zap,
  MessageSquare,
  BookOpen,
  Award,
  User,
  Calendar
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

import { GlassmorphicCard, InteractiveGlassCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicButton, IconButton } from '@/components/ui/glassmorphic-button'
import { GlassmorphicSelect } from '@/components/ui/glassmorphic-forms'
import { getSocketClient } from '@/lib/socket-client'

interface Notification {
  id: string
  type: 'COURSE' | 'ASSIGNMENT' | 'DISCUSSION' | 'SYSTEM' | 'ACHIEVEMENT' | 'QUIZ_AVAILABLE' | 'QUIZ_REMINDER'
  title: string
  message: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  category?: string
  isRead: boolean
  actionUrl?: string
  imageUrl?: string
  data?: any
  createdAt: string
  readAt?: string
  expiresAt?: string
}

interface EnhancedNotificationPanelProps {
  className?: string
  maxHeight?: string
  showSettings?: boolean
}

export function EnhancedNotificationPanel({ 
  className = '', 
  maxHeight = '500px',
  showSettings = true 
}: EnhancedNotificationPanelProps) {
  const { data: session } = useSession()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [isConnected, setIsConnected] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set())

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        limit: '50',
        read: filter === 'all' ? 'all' : filter === 'unread' ? 'false' : 'true'
      })

      if (typeFilter !== 'all') {
        params.append('type', typeFilter)
      }

      const response = await fetch(`/api/notifications?${params}`)
      if (response.ok) {
        const data = await response.json()
        setNotifications(data.notifications)
        setUnreadCount(data.unreadCount)
      }
    } catch (error) {
      console.error('Error fetching notifications:', error)
      toast.error('Failed to load notifications')
    } finally {
      setLoading(false)
    }
  }, [filter, typeFilter])

  // Initialize real-time connection
  useEffect(() => {
    if (!session?.user?.id) return

    const socketClient = getSocketClient()
    
    // Connection handlers
    const handleConnect = () => {
      setIsConnected(true)
      console.log('🔔 Notification panel connected')
    }

    const handleDisconnect = () => {
      setIsConnected(false)
      console.log('🔔 Notification panel disconnected')
    }

    // Notification handlers
    const handleNewNotification = (notification: any) => {
      setNotifications(prev => [notification, ...prev])
      setUnreadCount(prev => prev + 1)
      
      // Show toast for high priority notifications
      if (notification.priority === 'high' || notification.priority === 'urgent') {
        toast(notification.title, {
          description: notification.message,
          action: notification.actionUrl ? {
            label: 'View',
            onClick: () => window.location.href = notification.actionUrl
          } : undefined
        })
      }
    }

    // Set up socket listeners
    socketClient.on('connect', handleConnect)
    socketClient.on('disconnect', handleDisconnect)
    socketClient.on('notification:received', handleNewNotification)

    // Initial fetch
    fetchNotifications()

    return () => {
      socketClient.off('connect', handleConnect)
      socketClient.off('disconnect', handleDisconnect)
      socketClient.off('notification:received', handleNewNotification)
    }
  }, [session?.user?.id, fetchNotifications])

  // Mark notifications as read
  const markAsRead = async (notificationIds?: string[]) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          notificationIds,
          markAll: !notificationIds
        })
      })

      if (response.ok) {
        if (notificationIds) {
          setNotifications(prev => 
            prev.map(n => 
              notificationIds.includes(n.id) 
                ? { ...n, isRead: true, readAt: new Date().toISOString() }
                : n
            )
          )
          setUnreadCount(prev => Math.max(0, prev - notificationIds.length))
        } else {
          setNotifications(prev => 
            prev.map(n => ({ ...n, isRead: true, readAt: new Date().toISOString() }))
          )
          setUnreadCount(0)
        }
        toast.success('Notifications marked as read')
      }
    } catch (error) {
      console.error('Error marking notifications as read:', error)
      toast.error('Failed to mark notifications as read')
    }
  }

  // Delete notifications
  const deleteNotifications = async (notificationIds: string[]) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationIds })
      })

      if (response.ok) {
        setNotifications(prev => prev.filter(n => !notificationIds.includes(n.id)))
        setUnreadCount(prev => {
          const deletedUnread = notifications.filter(n => 
            notificationIds.includes(n.id) && !n.isRead
          ).length
          return Math.max(0, prev - deletedUnread)
        })
        setSelectedNotifications(new Set())
        toast.success('Notifications deleted')
      }
    } catch (error) {
      console.error('Error deleting notifications:', error)
      toast.error('Failed to delete notifications')
    }
  }

  // Handle notification click
  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.isRead) {
      await markAsRead([notification.id])
    }

    if (notification.actionUrl) {
      window.location.href = notification.actionUrl
    }
  }

  // Toggle notification selection
  const toggleSelection = (notificationId: string) => {
    setSelectedNotifications(prev => {
      const newSet = new Set(prev)
      if (newSet.has(notificationId)) {
        newSet.delete(notificationId)
      } else {
        newSet.add(notificationId)
      }
      return newSet
    })
  }

  // Get notification icon
  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = `w-5 h-5 ${
      priority === 'urgent' ? 'text-red-500' :
      priority === 'high' ? 'text-orange-500' :
      priority === 'normal' ? 'text-blue-500' :
      'text-gray-500'
    }`

    switch (type) {
      case 'COURSE':
        return <BookOpen className={iconClass} />
      case 'ASSIGNMENT':
        return <Calendar className={iconClass} />
      case 'DISCUSSION':
        return <MessageSquare className={iconClass} />
      case 'ACHIEVEMENT':
        return <Award className={iconClass} />
      case 'QUIZ_AVAILABLE':
      case 'QUIZ_REMINDER':
        return <Zap className={iconClass} />
      case 'SYSTEM':
        return priority === 'urgent' ? <AlertTriangle className={iconClass} /> : <Info className={iconClass} />
      default:
        return <Bell className={iconClass} />
    }
  }

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300 text-xs rounded-full">Urgent</span>
      case 'high':
        return <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300 text-xs rounded-full">High</span>
      default:
        return null
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.isRead) return false
    if (filter === 'read' && !notification.isRead) return false
    if (typeFilter !== 'all' && notification.type !== typeFilter) return false
    return true
  })

  return (
    <GlassmorphicCard className={`${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              {isConnected ? (
                <BellRing className="w-6 h-6 text-violet-500" />
              ) : (
                <Bell className="w-6 h-6 text-gray-400" />
              )}
              {unreadCount > 0 && (
                <span className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Notifications
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {unreadCount} unread
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {unreadCount > 0 && (
              <GlassmorphicButton
                variant="ghost"
                size="sm"
                onClick={() => markAsRead()}
              >
                <CheckCheck className="w-4 h-4 mr-2" />
                Mark all read
              </GlassmorphicButton>
            )}

            {showSettings && (
              <IconButton variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </IconButton>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-3 mt-4">
          <GlassmorphicSelect
            options={[
              { value: 'all', label: 'All' },
              { value: 'unread', label: 'Unread' },
              { value: 'read', label: 'Read' }
            ]}
            value={filter}
            onChange={(value) => setFilter(value as any)}
            className="w-32"
            variant="filled"
          />

          <GlassmorphicSelect
            options={[
              { value: 'all', label: 'All Types' },
              { value: 'COURSE', label: 'Courses' },
              { value: 'ASSIGNMENT', label: 'Assignments' },
              { value: 'DISCUSSION', label: 'Discussions' },
              { value: 'ACHIEVEMENT', label: 'Achievements' },
              { value: 'SYSTEM', label: 'System' }
            ]}
            value={typeFilter}
            onChange={setTypeFilter}
            className="w-40"
            variant="filled"
          />
        </div>

        {/* Bulk Actions */}
        {selectedNotifications.size > 0 && (
          <div className="flex items-center space-x-2 mt-3 p-2 bg-violet-100 dark:bg-violet-900/20 rounded-lg">
            <span className="text-sm text-violet-700 dark:text-violet-300">
              {selectedNotifications.size} selected
            </span>
            <GlassmorphicButton
              variant="ghost"
              size="sm"
              onClick={() => markAsRead(Array.from(selectedNotifications))}
            >
              <Check className="w-4 h-4 mr-1" />
              Mark read
            </GlassmorphicButton>
            <GlassmorphicButton
              variant="ghost"
              size="sm"
              onClick={() => deleteNotifications(Array.from(selectedNotifications))}
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Delete
            </GlassmorphicButton>
          </div>
        )}
      </div>

      {/* Notifications List */}
      <div className="overflow-y-auto" style={{ maxHeight }}>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="w-6 h-6 border-2 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">
              {filter === 'unread' ? 'No unread notifications' : 'No notifications'}
            </p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            <AnimatePresence>
              {filteredNotifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <NotificationItem
                    notification={notification}
                    isSelected={selectedNotifications.has(notification.id)}
                    onToggleSelection={() => toggleSelection(notification.id)}
                    onClick={() => handleNotificationClick(notification)}
                    onMarkRead={() => markAsRead([notification.id])}
                    onDelete={() => deleteNotifications([notification.id])}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>
    </GlassmorphicCard>
  )
}

// Individual Notification Item Component
interface NotificationItemProps {
  notification: Notification
  isSelected: boolean
  onToggleSelection: () => void
  onClick: () => void
  onMarkRead: () => void
  onDelete: () => void
}

function NotificationItem({ 
  notification, 
  isSelected, 
  onToggleSelection, 
  onClick, 
  onMarkRead, 
  onDelete 
}: NotificationItemProps) {
  const [showActions, setShowActions] = useState(false)

  return (
    <InteractiveGlassCard
      className={`p-3 cursor-pointer transition-all duration-200 ${
        !notification.isRead ? 'border-l-4 border-l-violet-500' : ''
      } ${isSelected ? 'ring-2 ring-violet-500' : ''}`}
      onClick={onClick}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="flex items-start space-x-3">
        {/* Selection Checkbox */}
        <input
          type="checkbox"
          checked={isSelected}
          onChange={onToggleSelection}
          onClick={(e) => e.stopPropagation()}
          className="mt-1 w-4 h-4 text-violet-600 bg-white/10 border-white/20 rounded focus:ring-violet-500"
        />

        {/* Icon */}
        <div className="flex-shrink-0 mt-1">
          {getNotificationIcon(notification.type, notification.priority)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className={`text-sm font-medium truncate ${
                  notification.isRead 
                    ? 'text-gray-700 dark:text-gray-300' 
                    : 'text-gray-900 dark:text-white'
                }`}>
                  {notification.title}
                </h4>
                {getPriorityBadge(notification.priority)}
              </div>
              
              <p className={`text-sm line-clamp-2 ${
                notification.isRead 
                  ? 'text-gray-600 dark:text-gray-400' 
                  : 'text-gray-700 dark:text-gray-300'
              }`}>
                {notification.message}
              </p>
              
              <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500 dark:text-gray-400">
                <span>{formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}</span>
                {notification.category && (
                  <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                    {notification.category}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <AnimatePresence>
              {showActions && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center space-x-1 ml-2"
                >
                  {!notification.isRead && (
                    <IconButton
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        onMarkRead()
                      }}
                      title="Mark as read"
                    >
                      <Check className="w-3 h-3" />
                    </IconButton>
                  )}
                  
                  <IconButton
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      onDelete()
                    }}
                    title="Delete"
                  >
                    <Trash2 className="w-3 h-3" />
                  </IconButton>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </InteractiveGlassCard>
  )
}
