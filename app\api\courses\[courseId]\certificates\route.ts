import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createCertificateTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required'),
  description: z.string().optional(),
  design: z.object({
    backgroundColor: z.string().default('#ffffff'),
    primaryColor: z.string().default('#6366f1'),
    secondaryColor: z.string().default('#8b5cf6'),
    fontFamily: z.string().default('Inter'),
    layout: z.enum(['modern', 'classic', 'elegant', 'minimal']).default('modern'),
    showLogo: z.boolean().default(true),
    showBorder: z.boolean().default(true),
    showSignature: z.boolean().default(true)
  }),
  content: z.object({
    title: z.string().default('Certificate of Completion'),
    subtitle: z.string().optional(),
    bodyText: z.string().default('This is to certify that {studentName} has successfully completed the course {courseName} on {completionDate}.'),
    footerText: z.string().optional(),
    instructorTitle: z.string().default('Course Instructor'),
    organizationName: z.string().optional()
  }),
  requirements: z.object({
    minCompletionPercentage: z.number().min(0).max(100).default(80),
    requireAllQuizzesPassed: z.boolean().default(false),
    minQuizScore: z.number().min(0).max(100).default(70),
    requireAllAssignmentsCompleted: z.boolean().default(false)
  }),
  isActive: z.boolean().default(true)
})

const querySchema = commonSchemas.pagination.extend({
  active: z.enum(['true', 'false']).optional()
})

// GET /api/courses/[courseId]/certificates - Get certificate templates
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]
      const { page = 1, limit = 20, active } = validatedQuery

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Build where clause
      const where: any = {
        courseId
      }

      if (active !== undefined) {
        where.isActive = active === 'true'
      }

      // Get total count
      const total = await prisma.certificateTemplate.count({ where })

      // Get templates
      const templates = await prisma.certificateTemplate.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          _count: {
            select: {
              certificates: true
            }
          }
        }
      })

      // Format templates
      const formattedTemplates = templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        design: template.design,
        content: template.content,
        requirements: template.requirements,
        isActive: template.isActive,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt,
        certificateCount: template._count.certificates
      }))

      return APIResponse.success({
        templates: formattedTemplates,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching certificate templates:', error)
      return APIResponse.error(
        'Failed to fetch templates: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/courses/[courseId]/certificates - Create certificate template
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: createCertificateTemplateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Deactivate other templates if this one is being set as active
      if (validatedBody.isActive) {
        await prisma.certificateTemplate.updateMany({
          where: { courseId },
          data: { isActive: false }
        })
      }

      // Create template
      const template = await prisma.certificateTemplate.create({
        data: {
          ...validatedBody,
          courseId
        },
        include: {
          _count: {
            select: {
              certificates: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Certificate template created successfully',
        template: {
          ...template,
          certificateCount: template._count.certificates
        }
      })

    } catch (error) {
      console.error('Error creating certificate template:', error)
      return APIResponse.error(
        'Failed to create template: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
