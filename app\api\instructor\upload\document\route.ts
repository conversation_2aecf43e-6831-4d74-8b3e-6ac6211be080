import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { uploadDocumentToBunny } from '@/lib/bunny-api'
import { formatFileSize } from '@/lib/bunny-config'

// Allowed document types
const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
  'text/csv',
  'application/zip',
  'application/x-zip-compressed',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp'
]

const ALLOWED_EXTENSIONS = [
  '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
  '.txt', '.csv', '.zip', '.jpg', '.jpeg', '.png', '.gif', '.webp'
]

function isValidDocumentFile(fileName: string, mimeType: string): boolean {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return ALLOWED_EXTENSIONS.includes(extension) || ALLOWED_DOCUMENT_TYPES.includes(mimeType)
}

// POST /api/instructor/upload/document - Upload document/resource
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const courseId = formData.get('courseId') as string
      const topicId = formData.get('topicId') as string | null
      const title = formData.get('title') as string || file?.name
      const description = formData.get('description') as string || ''
      const isDownloadable = formData.get('isDownloadable') === 'true'

      // Validate required fields
      if (!file || !courseId) {
        return APIResponse.error('File and courseId are required', 400)
      }

      // Validate file type
      if (!isValidDocumentFile(file.name, file.type)) {
        return APIResponse.error(
          'Invalid file format. Supported formats: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, JPG, PNG, GIF, WebP',
          400
        )
      }

      // Check file size (100MB limit for documents)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (file.size > maxSize) {
        return APIResponse.error(
          `File size too large. Maximum allowed size is ${formatFileSize(maxSize)}.`,
          400
        )
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found or access denied', 404)
      }

      // If topicId provided, verify it belongs to the course
      let topic = null
      if (topicId) {
        topic = await prisma.courseTopic.findFirst({
          where: { 
            id: topicId,
            section: {
              courseId
            }
          }
        })

        if (!topic) {
          return APIResponse.error('Topic not found in this course', 404)
        }
      }

      console.log(`📄 Starting document upload: ${file.name}`)
      console.log(`📁 File: ${file.name} (${formatFileSize(file.size)})`)

      // Upload document to Bunny CDN
      const uploadResult = await uploadDocumentToBunny(file, courseId, file.name)

      if (!uploadResult.success) {
        return APIResponse.error(
          uploadResult.message || 'Failed to upload document',
          500
        )
      }

      // Create course content record
      const courseContent = await prisma.courseContent.create({
        data: {
          ...(topicId && { topicId }),
          type: 'DOCUMENT',
          title: title || file.name,
          description,
          fileUrl: uploadResult.fileUrl,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          isDownloadable,
          order: 1 // Default order
        }
      })

      console.log('✅ Document upload completed successfully')

      return APIResponse.success({
        message: 'Document uploaded successfully',
        content: {
          id: courseContent.id,
          type: courseContent.type,
          title: courseContent.title,
          description: courseContent.description,
          fileUrl: courseContent.fileUrl,
          fileName: courseContent.fileName,
          fileSize: courseContent.fileSize,
          mimeType: courseContent.mimeType,
          isDownloadable: courseContent.isDownloadable,
          createdAt: courseContent.createdAt
        },
        uploadInfo: {
          originalFileName: file.name,
          fileSize: formatFileSize(file.size),
          mimeType: file.type,
          fileUrl: uploadResult.fileUrl
        }
      })

    } catch (error) {
      console.error('❌ Error uploading document:', error)
      return APIResponse.error(
        'Failed to upload document: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// GET /api/instructor/upload/document - Get recent document uploads
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (_request: NextRequest, { user }) => {
    try {
      // Get recent document uploads for this instructor
      const recentUploads = await prisma.courseContent.findMany({
        where: {
          type: 'DOCUMENT',
          OR: [
            // Documents attached to topics
            {
              topic: {
                section: {
                  course: {
                    instructorId: user.id
                  }
                }
              }
            },
            // Course-level documents (no topic)
            {
              topicId: null,
              // We need to find a way to link course-level documents
              // For now, we'll use a different approach
            }
          ]
        },
        orderBy: { createdAt: 'desc' },
        take: 20,
        include: {
          topic: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      title: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      const formattedUploads = recentUploads.map(upload => ({
        id: upload.id,
        title: upload.title,
        fileName: upload.fileName,
        fileSize: upload.fileSize ? formatFileSize(upload.fileSize) : 'Unknown',
        mimeType: upload.mimeType,
        fileUrl: upload.fileUrl,
        isDownloadable: upload.isDownloadable,
        createdAt: upload.createdAt,
        course: upload.topic ? {
          id: upload.topic.section.course.id,
          title: upload.topic.section.course.title
        } : null,
        section: upload.topic ? {
          id: upload.topic.section.id,
          title: upload.topic.section.title
        } : null,
        topic: upload.topic ? {
          id: upload.topic.id,
          title: upload.topic.title
        } : null
      }))

      return APIResponse.success({
        recentUploads: formattedUploads,
        uploadLimits: {
          maxFileSize: formatFileSize(100 * 1024 * 1024), // 100MB
          supportedFormats: ALLOWED_EXTENSIONS,
          allowedTypes: [
            'PDF Documents',
            'Microsoft Office (Word, Excel, PowerPoint)',
            'Text Files (TXT, CSV)',
            'Images (JPG, PNG, GIF, WebP)',
            'Archives (ZIP)'
          ]
        }
      })

    } catch (error) {
      console.error('❌ Error fetching document uploads:', error)
      return APIResponse.error(
        'Failed to fetch uploads: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
