import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createCourseSchema = z.object({
  title: z.string().min(1, 'Course title is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  language: z.string().default('en'),
  price: z.number().min(0).default(0),
  originalPrice: z.number().optional(),
  currency: z.string().default('INR'),
  thumbnailImage: z.string().url().optional(),
  previewVideo: z.string().url().optional(),
  tags: z.array(z.string()).default([]),
  keywords: z.array(z.string()).default([]),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  allowComments: z.boolean().default(true),
  allowDownloads: z.boolean().default(false)
})

const querySchema = commonSchemas.pagination.extend({
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED', 'PRIVATE']).optional(),
  search: z.string().optional()
})

// GET /api/instructor/courses - Get instructor's courses
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateQuery: querySchema
  },
  async (_request: NextRequest, { validatedQuery, user }) => {
    try {
      const { page = 1, limit = 20, status, search } = validatedQuery

      // Build where clause
      const where: any = {
        instructorId: user.id
      }

      if (status) {
        where.status = status
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { shortDescription: { contains: search, mode: 'insensitive' } }
        ]
      }

      // Get total count
      const total = await prisma.course.count({ where })

      // Get courses
      const courses = await prisma.course.findMany({
        where,
        orderBy: { updatedAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
        include: {
          sections: {
            include: {
              topics: {
                select: { id: true, duration: true }
              }
            }
          },
          _count: {
            select: {
              enrollments: true,
              reviews: true
            }
          }
        }
      })

      // Format courses with calculated fields
      const formattedCourses = courses.map(course => {
        const totalTopics = course.sections.reduce(
          (sum, section) => sum + section.topics.length, 
          0
        )
        const totalDuration = course.sections.reduce(
          (sum, section) => sum + section.topics.reduce(
            (topicSum, topic) => topicSum + (topic.duration || 0), 
            0
          ), 
          0
        )

        return {
          id: course.id,
          title: course.title,
          description: course.description,
          shortDescription: course.shortDescription,
          slug: course.slug,
          thumbnailImage: course.thumbnailImage,
          category: course.category,
          subcategory: course.subcategory,
          level: course.level,
          price: course.price,
          originalPrice: course.originalPrice,
          currency: course.currency,
          status: course.status,
          isPublished: course.isPublished,
          isFeatured: course.isFeatured,
          rating: course.rating,
          reviewCount: course.reviewCount,
          enrollmentCount: course.enrollmentCount,
          viewCount: course.viewCount,
          totalLessons: totalTopics,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          sectionsCount: course.sections.length,
          tags: course.tags,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt,
          publishedAt: course.publishedAt
        }
      })

      return APIResponse.success({
        courses: formattedCourses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching instructor courses:', error)
      return APIResponse.error(
        'Failed to fetch courses: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/instructor/courses - Create a new course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: createCourseSchema
  },
  async (_request: NextRequest, { validatedBody, user }) => {
    try {
      const courseData = validatedBody

      // Generate unique slug
      const baseSlug = courseData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')

      let slug = baseSlug
      let counter = 1
      
      while (await prisma.course.findUnique({ where: { slug } })) {
        slug = `${baseSlug}-${counter}`
        counter++
      }

      // Create course
      const course = await prisma.course.create({
        data: {
          ...courseData,
          slug,
          instructorId: user.id,
          status: 'DRAFT',
          isPublished: false
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Course created successfully',
        course: {
          id: course.id,
          title: course.title,
          description: course.description,
          slug: course.slug,
          status: course.status,
          isPublished: course.isPublished,
          instructor: course.instructor,
          createdAt: course.createdAt
        }
      })

    } catch (error) {
      console.error('Error creating course:', error)
      return APIResponse.error(
        'Failed to create course: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
