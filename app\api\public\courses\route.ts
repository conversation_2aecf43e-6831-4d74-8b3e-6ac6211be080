import { NextRequest } from 'next/server'
import { APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/public/courses - Get featured courses for public display (no auth required)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '6')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured') === 'true'

    // Build where clause for filtering
    const where: any = {
      status: 'PUBLISHED',
      isPublished: true
    }

    if (featured) {
      where.isFeatured = true
    }

    if (category && category !== 'all') {
      where.category = { equals: category, mode: 'insensitive' }
    }

    // Get courses from database
    const courses = await prisma.course.findMany({
      where,
      orderBy: [
        { rating: 'desc' },
        { enrollmentCount: 'desc' },
        { publishedAt: 'desc' }
      ],
      take: limit,
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true,
            isVerified: true
          }
        },
        _count: {
          select: {
            enrollments: true
          }
        }
      }
    })

    // Format courses for public display
    const formattedCourses = courses.map(course => ({
      id: course.id,
      title: course.title,
      description: course.description,
      shortDescription: course.shortDescription,
      slug: course.slug,
      thumbnailImage: course.thumbnailImage,
      category: course.category,
      subcategory: course.subcategory,
      level: course.level,
      price: course.price,
      originalPrice: course.originalPrice,
      currency: course.currency,
      rating: course.rating,
      reviewCount: course.reviewCount,
      enrollmentCount: course.enrollmentCount,
      estimatedDuration: course.estimatedDuration,
      totalLessons: course.totalLessons,
      isFeatured: course.isFeatured,
      tags: course.tags,
      instructor: course.instructor,
      publishedAt: course.publishedAt
    }))

    return APIResponse.success({
      courses: formattedCourses,
      total: formattedCourses.length
    })

  } catch (error) {
    console.error('Error fetching public courses:', error)

    return APIResponse.error(
      'Failed to fetch courses: ' + (error instanceof Error ? error.message : 'Unknown error'),
      500
    )
  }
}
