import { renderWithProviders, mockFetch, mockApiResponse, mockCourse, mockNotification } from '@/lib/test-utils'
import { screen, waitFor, fireEvent } from '@testing-library/react'
import { AdvancedSearch } from '@/components/search/advanced-search'
import { SearchResults } from '@/components/search/search-results'
import { EnhancedNotificationPanel } from '@/components/notifications/enhanced-notification-panel'
import { NotificationSettings } from '@/components/notifications/notification-settings'
import { PerformanceMonitor } from '@/components/admin/performance-monitor'

// Mock the socket client
jest.mock('@/lib/socket-client', () => ({
  getSocketClient: () => ({
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn()
  })
}))

describe('System Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Search System Integration', () => {
    it('should integrate search components correctly', async () => {
      const mockResults = {
        courses: [mockCourse],
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }

      const restoreFetch = mockFetch({
        'GET /api/search/courses': mockApiResponse.success(mockResults),
        'POST /api/search/courses/suggestions': mockApiResponse.success({
          courses: [{ type: 'course', id: '1', title: 'Test Course' }],
          instructors: [],
          tags: []
        })
      })

      const mockOnResults = jest.fn()
      const mockOnLoading = jest.fn()

      renderWithProviders(
        <AdvancedSearch
          onResults={mockOnResults}
          onLoading={mockOnLoading}
          initialQuery="test"
        />
      )

      // Check if search input is rendered
      expect(screen.getByPlaceholderText(/search for courses/i)).toBeInTheDocument()

      // Check if filters are available
      expect(screen.getByText(/filters/i)).toBeInTheDocument()

      restoreFetch()
    })

    it('should handle search results display', async () => {
      const mockOnCourseSelect = jest.fn()
      const mockOnEnroll = jest.fn()

      renderWithProviders(
        <SearchResults
          courses={[mockCourse]}
          loading={false}
          query="test"
          totalResults={1}
          onCourseSelect={mockOnCourseSelect}
          onEnroll={mockOnEnroll}
        />
      )

      // Check if course is displayed
      expect(screen.getByText(mockCourse.title)).toBeInTheDocument()
      expect(screen.getByText(mockCourse.instructor.name)).toBeInTheDocument()

      // Check if enroll button works
      const enrollButton = screen.getByText(/enroll/i)
      fireEvent.click(enrollButton)
      expect(mockOnEnroll).toHaveBeenCalledWith(mockCourse.id)
    })
  })

  describe('Notification System Integration', () => {
    it('should render notification panel correctly', async () => {
      const restoreFetch = mockFetch({
        'GET /api/notifications': mockApiResponse.success({
          notifications: [mockNotification],
          unreadCount: 1
        })
      })

      renderWithProviders(<EnhancedNotificationPanel />)

      await waitFor(() => {
        expect(screen.getByText(/notifications/i)).toBeInTheDocument()
      })

      restoreFetch()
    })

    it('should render notification settings correctly', async () => {
      const restoreFetch = mockFetch({
        'GET /api/user/notification-preferences': mockApiResponse.success({
          preferences: {
            email: { courseUpdates: true },
            push: { courseUpdates: true },
            inApp: { courseUpdates: true }
          }
        })
      })

      renderWithProviders(<NotificationSettings />)

      await waitFor(() => {
        expect(screen.getByText(/notification settings/i)).toBeInTheDocument()
      })

      restoreFetch()
    })
  })

  describe('Performance Monitoring Integration', () => {
    it('should render performance monitor correctly', async () => {
      const mockMetrics = {
        server: {
          uptime: 3600,
          cpuUsage: 45,
          memoryUsage: 60,
          diskUsage: 50,
          responseTime: 200
        },
        database: {
          connectionCount: 10,
          queryTime: 50,
          slowQueries: 2,
          cacheHitRate: 85
        },
        application: {
          activeUsers: 150,
          requestsPerMinute: 300,
          errorRate: 0.5,
          averageLoadTime: 800
        },
        cache: {
          hitRate: 90,
          missRate: 10,
          keyCount: 1000,
          memoryUsage: '50MB'
        }
      }

      const restoreFetch = mockFetch({
        'GET /api/admin/performance-metrics': mockApiResponse.success({
          metrics: mockMetrics,
          alerts: []
        })
      })

      renderWithProviders(<PerformanceMonitor />)

      await waitFor(() => {
        expect(screen.getByText(/performance monitor/i)).toBeInTheDocument()
      })

      restoreFetch()
    })
  })

  describe('Component Integration', () => {
    it('should handle glassmorphic components integration', () => {
      // Test that glassmorphic components can be imported and used together
      const { GlassmorphicCard } = require('@/components/ui/glassmorphic-card')
      const { GlassmorphicButton } = require('@/components/ui/glassmorphic-button')
      const { GlassmorphicInput } = require('@/components/ui/glassmorphic-forms')

      expect(GlassmorphicCard).toBeDefined()
      expect(GlassmorphicButton).toBeDefined()
      expect(GlassmorphicInput).toBeDefined()
    })

    it('should handle responsive hooks integration', () => {
      const { useResponsive, useMediaQuery } = require('@/hooks/use-responsive')

      expect(useResponsive).toBeDefined()
      expect(useMediaQuery).toBeDefined()
    })

    it('should handle cache system integration', async () => {
      const { cache } = require('@/lib/cache')

      expect(cache).toBeDefined()
      expect(typeof cache.get).toBe('function')
      expect(typeof cache.set).toBe('function')
      expect(typeof cache.del).toBe('function')
    })

    it('should handle database optimization integration', () => {
      const { DatabaseOptimizer } = require('@/lib/db-optimization')

      expect(DatabaseOptimizer).toBeDefined()
      expect(typeof DatabaseOptimizer.getCourseById).toBe('function')
      expect(typeof DatabaseOptimizer.getCourses).toBe('function')
    })
  })

  describe('API Integration', () => {
    it('should handle search API integration', async () => {
      const restoreFetch = mockFetch({
        'GET /api/search/courses?q=test': mockApiResponse.success({
          courses: [mockCourse],
          pagination: { page: 1, limit: 20, total: 1 }
        })
      })

      const response = await fetch('/api/search/courses?q=test')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.courses).toHaveLength(1)

      restoreFetch()
    })

    it('should handle notification API integration', async () => {
      const restoreFetch = mockFetch({
        'GET /api/notifications': mockApiResponse.success({
          notifications: [mockNotification],
          unreadCount: 1
        })
      })

      const response = await fetch('/api/notifications')
      const data = await response.json()

      expect(data.success).toBe(true)
      expect(data.data.notifications).toHaveLength(1)

      restoreFetch()
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle API errors gracefully', async () => {
      const restoreFetch = mockFetch({
        'GET /api/search/courses': mockApiResponse.error('Server error', 500)
      })

      const response = await fetch('/api/search/courses')
      expect(response.status).toBe(500)

      restoreFetch()
    })

    it('should handle component errors gracefully', () => {
      // Test error boundaries and fallbacks
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

      try {
        renderWithProviders(
          <SearchResults
            courses={[]}
            loading={false}
            query=""
            onCourseSelect={() => {}}
            onEnroll={() => {}}
          />
        )

        expect(screen.getByText(/no courses found/i)).toBeInTheDocument()
      } finally {
        consoleSpy.mockRestore()
      }
    })
  })

  describe('Performance Integration', () => {
    it('should handle performance monitoring', async () => {
      const { PerformanceTester } = require('@/lib/test-utils')
      const tester = new PerformanceTester()

      const result = await tester.measureAsync('test-operation', async () => {
        await new Promise(resolve => setTimeout(resolve, 100))
        return 'test-result'
      })

      expect(result).toBe('test-result')

      const stats = tester.getStats('test-operation')
      expect(stats).toBeDefined()
      expect(stats?.count).toBe(1)
      expect(stats?.average).toBeGreaterThan(90)
    })
  })
})
