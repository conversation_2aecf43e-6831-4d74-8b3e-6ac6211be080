/**
 * Bunny CDN API Service
 * 
 * This module provides functions to interact with Bunny CDN API endpoints
 * for video upload, streaming, and content management.
 */

import {
  getBunnyConfig,
  getBunnyHeaders,
  getBunnyStorageHeaders,
  getVideoStoragePath,
  getDocumentStoragePath,
  isBunnyApiError,
  getBunnyErrorMessage,
  type BunnyVideoUploadResponse,
  type BunnyVideoInfo
} from './bunny-config'

export class BunnyApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message)
    this.name = 'BunnyApiError'
  }
}

/**
 * Upload video file to Bunny CDN storage
 */
export async function uploadVideoToBunny(
  file: File | Buffer,
  courseId: string,
  sectionId: string,
  topicId: string,
  fileName: string
): Promise<BunnyVideoUploadResponse> {
  try {
    const config = getBunnyConfig()
    const storagePath = getVideoStoragePath(courseId, sectionId, topicId, fileName)
    const uploadUrl = `${config.storageUrl}/${storagePath}`

    console.log('🎥 Uploading video to Bunny CDN:', storagePath)

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: getBunnyStorageHeaders(),
      body: file
    })

    if (!response.ok) {
      throw new BunnyApiError(
        `Failed to upload video: ${response.statusText}`,
        response.status
      )
    }

    // Generate video URL
    const videoUrl = `${config.pullZoneUrl}/${storagePath}`
    
    return {
      success: true,
      message: 'Video uploaded successfully',
      videoUrl,
      thumbnailUrl: `${config.pullZoneUrl}/thumbnails/${topicId}.jpg`
    }

  } catch (error) {
    console.error('❌ Error uploading video to Bunny CDN:', error)
    throw new BunnyApiError(
      'Failed to upload video to Bunny CDN',
      500,
      error
    )
  }
}

/**
 * Upload document/resource file to Bunny CDN storage
 */
export async function uploadDocumentToBunny(
  file: File | Buffer,
  courseId: string,
  fileName: string
): Promise<{ success: boolean; fileUrl: string; message: string }> {
  try {
    const config = getBunnyConfig()
    const storagePath = getDocumentStoragePath(courseId, fileName)
    const uploadUrl = `${config.storageUrl}/${storagePath}`

    console.log('📄 Uploading document to Bunny CDN:', storagePath)

    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: getBunnyStorageHeaders(),
      body: file
    })

    if (!response.ok) {
      throw new BunnyApiError(
        `Failed to upload document: ${response.statusText}`,
        response.status
      )
    }

    const fileUrl = `${config.pullZoneUrl}/${storagePath}`
    
    return {
      success: true,
      fileUrl,
      message: 'Document uploaded successfully'
    }

  } catch (error) {
    console.error('❌ Error uploading document to Bunny CDN:', error)
    throw new BunnyApiError(
      'Failed to upload document to Bunny CDN',
      500,
      error
    )
  }
}

/**
 * Delete file from Bunny CDN storage
 */
export async function deleteFileFromBunny(filePath: string): Promise<boolean> {
  try {
    const config = getBunnyConfig()
    const deleteUrl = `${config.storageUrl}/${filePath}`

    console.log('🗑️ Deleting file from Bunny CDN:', filePath)

    const response = await fetch(deleteUrl, {
      method: 'DELETE',
      headers: {
        'AccessKey': config.storagePassword
      }
    })

    if (!response.ok && response.status !== 404) {
      throw new BunnyApiError(
        `Failed to delete file: ${response.statusText}`,
        response.status
      )
    }

    return true

  } catch (error) {
    console.error('❌ Error deleting file from Bunny CDN:', error)
    return false
  }
}

/**
 * Get video information from Bunny Stream (if using Stream API)
 */
export async function getBunnyVideoInfo(videoId: string): Promise<BunnyVideoInfo | null> {
  try {
    const config = getBunnyConfig()
    
    if (!config.streamApiKey || !config.libraryId) {
      console.warn('⚠️ Bunny Stream API not configured, skipping video info fetch')
      return null
    }

    const response = await fetch(
      `https://video.bunnycdn.com/library/${config.libraryId}/videos/${videoId}`,
      {
        method: 'GET',
        headers: getBunnyHeaders(true)
      }
    )

    if (!response.ok) {
      throw new BunnyApiError(
        `Failed to get video info: ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()

    return {
      videoId: data.guid,
      title: data.title,
      duration: data.length,
      status: data.status === 4 ? 'ready' : 'processing',
      thumbnailUrl: data.thumbnailUrl,
      videoUrl: data.playlistUrl,
      qualities: data.videoLibrary?.map((quality: any) => ({
        quality: `${quality.height}p`,
        url: quality.url,
        fileSize: quality.size,
        bitrate: quality.bitrate
      })) || [],
      fileSize: data.storageSize,
      createdAt: data.dateUploaded
    }

  } catch (error) {
    console.error('❌ Error getting video info from Bunny CDN:', error)
    return null
  }
}

/**
 * Generate video thumbnail
 */
export async function generateVideoThumbnail(
  videoPath: string,
  timestamp = 10
): Promise<string | null> {
  try {
    const config = getBunnyConfig()
    
    // For basic implementation, return a placeholder thumbnail URL
    // In production, implement actual thumbnail generation
    const thumbnailPath = videoPath.replace(/\.[^/.]+$/, '_thumbnail.jpg')
    return `${config.pullZoneUrl}/${thumbnailPath}`

  } catch (error) {
    console.error('❌ Error generating video thumbnail:', error)
    return null
  }
}

/**
 * Get storage zone statistics
 */
export async function getBunnyStorageStats(): Promise<{
  totalFiles: number
  totalSize: number
  bandwidth: number
} | null> {
  try {
    const config = getBunnyConfig()

    const response = await fetch(
      `https://api.bunny.net/storagezone/${config.storageZoneName}/statistics`,
      {
        method: 'GET',
        headers: getBunnyHeaders()
      }
    )

    if (!response.ok) {
      throw new BunnyApiError(
        `Failed to get storage stats: ${response.statusText}`,
        response.status
      )
    }

    const data = await response.json()

    return {
      totalFiles: data.FilesStored || 0,
      totalSize: data.StorageUsed || 0,
      bandwidth: data.BandwidthUsed || 0
    }

  } catch (error) {
    console.error('❌ Error getting Bunny CDN storage stats:', error)
    return null
  }
}

/**
 * Purge CDN cache for a specific file
 */
export async function purgeBunnyCDNCache(fileUrl: string): Promise<boolean> {
  try {
    const config = getBunnyConfig()

    const response = await fetch('https://api.bunny.net/purge', {
      method: 'POST',
      headers: {
        ...getBunnyHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: fileUrl,
        async: false
      })
    })

    if (!response.ok) {
      throw new BunnyApiError(
        `Failed to purge cache: ${response.statusText}`,
        response.status
      )
    }

    return true

  } catch (error) {
    console.error('❌ Error purging Bunny CDN cache:', error)
    return false
  }
}
