import { Redis } from 'ioredis'

// Redis client configuration
let redis: Redis | null = null

try {
  redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
    lazyConnect: true,
    connectTimeout: 5000,
    commandTimeout: 5000
  })
} catch (error) {
  console.warn('Redis client initialization failed:', error)
  redis = null
}

// Cache key prefixes
export const CACHE_KEYS = {
  COURSE: 'course:',
  COURSE_LIST: 'courses:list:',
  USER: 'user:',
  QUIZ: 'quiz:',
  SEARCH: 'search:',
  TRENDING: 'trending:',
  RECOMMENDATIONS: 'recommendations:',
  NOTIFICATIONS: 'notifications:',
  ANALYTICS: 'analytics:',
  SESSION: 'session:'
} as const

// Cache TTL (Time To Live) in seconds
export const CACHE_TTL = {
  SHORT: 300,      // 5 minutes
  MEDIUM: 1800,    // 30 minutes
  LONG: 3600,      // 1 hour
  VERY_LONG: 86400 // 24 hours
} as const

interface CacheOptions {
  ttl?: number
  tags?: string[]
  compress?: boolean
}

class CacheManager {
  private isConnected = false

  constructor() {
    this.initializeConnection()
  }

  private async initializeConnection() {
    try {
      if (redis) {
        await redis.ping()
        this.isConnected = true
        console.log('✅ Redis cache connected')
      } else {
        this.isConnected = false
      }
    } catch (error) {
      console.warn('⚠️ Redis cache not available, falling back to memory cache')
      this.isConnected = false
    }
  }

  // Get cached data
  async get<T>(key: string): Promise<T | null> {
    try {
      if (!this.isConnected || !redis) {
        return this.memoryGet(key)
      }

      const cached = await redis.get(key)
      if (!cached) return null

      const data = JSON.parse(cached)

      // Check if data has compression flag
      if (data._compressed) {
        // Decompress if needed (implement compression library)
        return data.data
      }

      return data
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  // Set cached data
  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    try {
      const { ttl = CACHE_TTL.MEDIUM, tags = [], compress = false } = options

      let dataToCache = value

      // Compress large objects if needed
      if (compress && JSON.stringify(value).length > 10000) {
        dataToCache = {
          _compressed: true,
          data: value
        } as any
      }

      if (!this.isConnected || !redis) {
        this.memorySet(key, dataToCache, ttl)
        return
      }

      await redis.setex(key, ttl, JSON.stringify(dataToCache))

      // Store tags for cache invalidation
      if (tags.length > 0) {
        for (const tag of tags) {
          await redis.sadd(`tag:${tag}`, key)
          await redis.expire(`tag:${tag}`, ttl)
        }
      }
    } catch (error) {
      console.error('Cache set error:', error)
    }
  }

  // Delete cached data
  async del(key: string): Promise<void> {
    try {
      if (!this.isConnected || !redis) {
        this.memoryDelete(key)
        return
      }

      await redis.del(key)
    } catch (error) {
      console.error('Cache delete error:', error)
    }
  }

  // Invalidate cache by tags
  async invalidateByTags(tags: string[]): Promise<void> {
    try {
      if (!this.isConnected || !redis) {
        // For memory cache, we'd need to track tags separately
        return
      }

      for (const tag of tags) {
        const keys = await redis.smembers(`tag:${tag}`)
        if (keys.length > 0) {
          await redis.del(...keys)
          await redis.del(`tag:${tag}`)
        }
      }
    } catch (error) {
      console.error('Cache invalidation error:', error)
    }
  }

  // Clear all cache
  async clear(): Promise<void> {
    try {
      if (!this.isConnected || !redis) {
        this.memoryClear()
        return
      }

      await redis.flushdb()
    } catch (error) {
      console.error('Cache clear error:', error)
    }
  }

  // Get cache statistics
  async getStats(): Promise<{
    connected: boolean
    keyCount: number
    memoryUsage?: string
  }> {
    try {
      if (!this.isConnected || !redis) {
        return {
          connected: false,
          keyCount: this.memoryCache.size
        }
      }

      const info = await redis.info('memory')
      const keyCount = await redis.dbsize()

      const memoryMatch = info.match(/used_memory_human:(.+)/)
      const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'Unknown'

      return {
        connected: true,
        keyCount,
        memoryUsage
      }
    } catch (error) {
      console.error('Cache stats error:', error)
      return {
        connected: false,
        keyCount: 0
      }
    }
  }

  // Memory cache fallback
  private memoryCache = new Map<string, { data: any; expires: number }>()

  private memoryGet(key: string) {
    const item = this.memoryCache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expires) {
      this.memoryCache.delete(key)
      return null
    }
    
    return item.data
  }

  private memorySet(key: string, value: any, ttl: number) {
    this.memoryCache.set(key, {
      data: value,
      expires: Date.now() + (ttl * 1000)
    })
  }

  private memoryDelete(key: string) {
    this.memoryCache.delete(key)
  }

  private memoryClear() {
    this.memoryCache.clear()
  }

  // Cleanup expired memory cache entries
  private cleanupMemoryCache() {
    const now = Date.now()
    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expires) {
        this.memoryCache.delete(key)
      }
    }
  }
}

// Singleton cache manager
export const cache = new CacheManager()

// Cache decorators and utilities
export function withCache<T extends any[], R>(
  keyGenerator: (...args: T) => string,
  options: CacheOptions = {}
) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const method = descriptor.value

    descriptor.value = async function (...args: T): Promise<R> {
      const cacheKey = keyGenerator(...args)
      
      // Try to get from cache first
      const cached = await cache.get<R>(cacheKey)
      if (cached !== null) {
        return cached
      }

      // Execute original method
      const result = await method.apply(this, args)
      
      // Cache the result
      await cache.set(cacheKey, result, options)
      
      return result
    }
  }
}

// Utility functions for common cache patterns
export const cacheUtils = {
  // Generate cache key for course
  courseKey: (courseId: string) => `${CACHE_KEYS.COURSE}${courseId}`,
  
  // Generate cache key for course list with filters
  courseListKey: (filters: Record<string, any>) => {
    const filterString = Object.entries(filters)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join('|')
    return `${CACHE_KEYS.COURSE_LIST}${Buffer.from(filterString).toString('base64')}`
  },

  // Generate cache key for user data
  userKey: (userId: string) => `${CACHE_KEYS.USER}${userId}`,

  // Generate cache key for search results
  searchKey: (query: string, filters: Record<string, any>) => {
    const searchString = `${query}|${JSON.stringify(filters)}`
    return `${CACHE_KEYS.SEARCH}${Buffer.from(searchString).toString('base64')}`
  },

  // Generate cache key for trending courses
  trendingKey: (period: string, category?: string) => 
    `${CACHE_KEYS.TRENDING}${period}${category ? `:${category}` : ''}`,

  // Generate cache key for recommendations
  recommendationsKey: (userId: string, type: string) => 
    `${CACHE_KEYS.RECOMMENDATIONS}${userId}:${type}`,

  // Generate cache key for notifications
  notificationsKey: (userId: string, filters: Record<string, any>) => {
    const filterString = JSON.stringify(filters)
    return `${CACHE_KEYS.NOTIFICATIONS}${userId}:${Buffer.from(filterString).toString('base64')}`
  }
}

// Cache warming utilities
export const cacheWarming = {
  // Warm up popular courses
  async warmPopularCourses() {
    try {
      // This would typically fetch and cache the most popular courses
      console.log('Warming up popular courses cache...')
      // Implementation would go here
    } catch (error) {
      console.error('Error warming popular courses cache:', error)
    }
  },

  // Warm up trending data
  async warmTrendingData() {
    try {
      console.log('Warming up trending data cache...')
      // Implementation would go here
    } catch (error) {
      console.error('Error warming trending data cache:', error)
    }
  },

  // Warm up user-specific data
  async warmUserData(userId: string) {
    try {
      console.log(`Warming up cache for user ${userId}...`)
      // Implementation would go here
    } catch (error) {
      console.error('Error warming user data cache:', error)
    }
  }
}

// Cache invalidation patterns
export const cacheInvalidation = {
  // Invalidate course-related caches
  async invalidateCourse(courseId: string) {
    await cache.invalidateByTags([`course:${courseId}`, 'courses', 'trending', 'recommendations'])
  },

  // Invalidate user-related caches
  async invalidateUser(userId: string) {
    await cache.invalidateByTags([`user:${userId}`, 'recommendations', 'notifications'])
  },

  // Invalidate search caches
  async invalidateSearch() {
    await cache.invalidateByTags(['search', 'trending', 'recommendations'])
  }
}

// Performance monitoring
export const cacheMetrics = {
  hits: 0,
  misses: 0,
  
  recordHit() {
    this.hits++
  },
  
  recordMiss() {
    this.misses++
  },
  
  getHitRate() {
    const total = this.hits + this.misses
    return total > 0 ? (this.hits / total) * 100 : 0
  },
  
  reset() {
    this.hits = 0
    this.misses = 0
  }
}
