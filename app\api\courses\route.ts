import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const querySchema = commonSchemas.pagination.extend({
  search: z.string().optional(),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  level: z.string().optional(),
  minPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  maxPrice: z.string().transform(val => val ? parseFloat(val) : undefined).optional(),
  featured: z.enum(['true', 'false']).optional(),
  sortBy: z.enum(['newest', 'oldest', 'rating', 'price_low', 'price_high', 'popular']).optional().default('newest')
})

// GET /api/courses - Get all published courses with optional filtering
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: querySchema
  },
  async (_request: NextRequest, { validatedQuery, user }) => {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        category,
        subcategory,
        level,
        minPrice,
        maxPrice,
        featured,
        sortBy
      } = validatedQuery

      // Build where clause for filtering
      const where: any = {
        status: 'PUBLISHED',
        isPublished: true
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { shortDescription: { contains: search, mode: 'insensitive' } },
          { keywords: { hasSome: [search] } },
          { tags: { hasSome: [search] } }
        ]
      }

      if (category) {
        where.category = { equals: category, mode: 'insensitive' }
      }

      if (subcategory) {
        where.subcategory = { equals: subcategory, mode: 'insensitive' }
      }

      if (level) {
        where.level = { equals: level, mode: 'insensitive' }
      }

      if (featured === 'true') {
        where.isFeatured = true
      }

      if (minPrice !== undefined || maxPrice !== undefined) {
        where.price = {}
        if (minPrice !== undefined) where.price.gte = minPrice
        if (maxPrice !== undefined) where.price.lte = maxPrice
      }

      // Build order by clause
      let orderBy: any[] = []
      switch (sortBy) {
        case 'newest':
          orderBy = [{ publishedAt: 'desc' }, { createdAt: 'desc' }]
          break
        case 'oldest':
          orderBy = [{ publishedAt: 'asc' }, { createdAt: 'asc' }]
          break
        case 'rating':
          orderBy = [{ rating: 'desc' }, { reviewCount: 'desc' }]
          break
        case 'price_low':
          orderBy = [{ price: 'asc' }]
          break
        case 'price_high':
          orderBy = [{ price: 'desc' }]
          break
        case 'popular':
          orderBy = [{ enrollmentCount: 'desc' }, { viewCount: 'desc' }]
          break
        default:
          orderBy = [{ publishedAt: 'desc' }, { createdAt: 'desc' }]
      }

      // Get total count for pagination
      const total = await prisma.course.count({ where })

      // Get courses with pagination
      const courses = await prisma.course.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              expertise: true,
              isVerified: true
            }
          },
          sections: {
            where: { isPublished: true },
            select: {
              id: true,
              title: true,
              order: true,
              topics: {
                where: { isPublished: true },
                select: {
                  id: true,
                  title: true,
                  duration: true,
                  type: true
                }
              }
            },
            orderBy: { order: 'asc' }
          },
          _count: {
            select: {
              enrollments: true,
              reviews: true,
              sections: { where: { isPublished: true } }
            }
          }
        }
      })

      // Check which courses the user is enrolled in
      const userEnrollments = await prisma.courseEnrollment.findMany({
        where: {
          userId: user.id,
          courseId: { in: courses.map(c => c.id) },
          status: { in: ['ACTIVE', 'COMPLETED'] }
        },
        select: {
          courseId: true,
          status: true,
          progress: true,
          completedAt: true
        }
      })

      const enrollmentMap = new Map(
        userEnrollments.map(e => [e.courseId, e])
      )

      // Add enrollment status and calculated fields to courses
      const coursesWithEnrollment = courses.map(course => {
        const enrollment = enrollmentMap.get(course.id)
        const totalTopics = course.sections.reduce((sum, section) => sum + section.topics.length, 0)
        const totalDuration = course.sections.reduce((sum, section) =>
          sum + section.topics.reduce((topicSum, topic) => topicSum + (topic.duration || 0), 0), 0
        )

        return {
          id: course.id,
          title: course.title,
          description: course.description,
          shortDescription: course.shortDescription,
          slug: course.slug,
          thumbnailImage: course.thumbnailImage,
          previewVideo: course.previewVideo,
          category: course.category,
          subcategory: course.subcategory,
          level: course.level,
          language: course.language,
          price: course.price,
          originalPrice: course.originalPrice,
          currency: course.currency,
          rating: course.rating,
          reviewCount: course.reviewCount,
          enrollmentCount: course.enrollmentCount,
          viewCount: course.viewCount,
          isFeatured: course.isFeatured,
          tags: course.tags,
          keywords: course.keywords,
          publishedAt: course.publishedAt,
          createdAt: course.createdAt,
          updatedAt: course.updatedAt,
          // Instructor info
          instructor: course.instructor,
          // Course structure
          totalLessons: totalTopics,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          sectionsCount: course._count.sections,
          // Enrollment info
          isEnrolled: !!enrollment,
          enrollmentStatus: enrollment?.status || null,
          progress: enrollment?.progress || 0,
          completedAt: enrollment?.completedAt || null,
          // Sections (for preview)
          sections: course.sections.map(section => ({
            id: section.id,
            title: section.title,
            order: section.order,
            topicsCount: section.topics.length,
            topics: section.topics.map(topic => ({
              id: topic.id,
              title: topic.title,
              duration: topic.duration,
              type: topic.type
            }))
          }))
        }
      })

      return APIResponse.success({
        courses: coursesWithEnrollment,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })
    } catch (error) {
      console.error('Error fetching courses:', error)

      return APIResponse.error(
        'Failed to fetch courses: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
