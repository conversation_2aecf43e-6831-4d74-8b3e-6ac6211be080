import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const searchSchema = commonSchemas.pagination.extend({
  q: z.string().optional(),
  category: z.string().optional(),
  level: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED']).optional(),
  duration: z.enum(['SHORT', 'MEDIUM', 'LONG']).optional(), // <2h, 2-10h, >10h
  price: z.enum(['FREE', 'PAID']).optional(),
  rating: z.number().min(0).max(5).optional(),
  instructor: z.string().optional(),
  tags: z.string().optional(), // comma-separated
  sortBy: z.enum(['relevance', 'popularity', 'rating', 'newest', 'price_low', 'price_high']).default('relevance'),
  featured: z.enum(['true', 'false']).optional(),
  language: z.string().optional()
})

// GET /api/search/courses - Advanced course search
export const GET = createAPIHandler(
  {
    requireAuth: false,
    validateQuery: searchSchema
  },
  async (request: NextRequest, { validatedQuery }) => {
    try {
      const {
        q,
        category,
        level,
        duration,
        price,
        rating,
        instructor,
        tags,
        sortBy,
        featured,
        language,
        page = 1,
        limit = 20
      } = validatedQuery

      // Build search conditions
      const searchConditions: any = {
        isPublished: true,
        isDeleted: false
      }

      // Text search across title, description, and tags
      if (q) {
        searchConditions.OR = [
          { title: { contains: q, mode: 'insensitive' } },
          { description: { contains: q, mode: 'insensitive' } },
          { shortDescription: { contains: q, mode: 'insensitive' } },
          { tags: { hasSome: q.split(' ') } },
          {
            instructor: {
              name: { contains: q, mode: 'insensitive' }
            }
          }
        ]
      }

      // Category filter
      if (category) {
        searchConditions.category = category
      }

      // Level filter
      if (level) {
        searchConditions.level = level
      }

      // Duration filter (based on estimated duration)
      if (duration) {
        switch (duration) {
          case 'SHORT':
            searchConditions.estimatedDuration = { lt: 120 } // < 2 hours
            break
          case 'MEDIUM':
            searchConditions.estimatedDuration = { gte: 120, lte: 600 } // 2-10 hours
            break
          case 'LONG':
            searchConditions.estimatedDuration = { gt: 600 } // > 10 hours
            break
        }
      }

      // Price filter
      if (price) {
        if (price === 'FREE') {
          searchConditions.price = 0
        } else {
          searchConditions.price = { gt: 0 }
        }
      }

      // Rating filter
      if (rating) {
        searchConditions.averageRating = { gte: rating }
      }

      // Instructor filter
      if (instructor) {
        searchConditions.instructor = {
          name: { contains: instructor, mode: 'insensitive' }
        }
      }

      // Tags filter
      if (tags) {
        const tagArray = tags.split(',').map(tag => tag.trim())
        searchConditions.tags = { hasSome: tagArray }
      }

      // Featured filter
      if (featured === 'true') {
        searchConditions.isFeatured = true
      }

      // Language filter
      if (language) {
        searchConditions.language = language
      }

      // Build sort conditions
      let orderBy: any = {}
      switch (sortBy) {
        case 'popularity':
          orderBy = [
            { enrollmentCount: 'desc' },
            { averageRating: 'desc' }
          ]
          break
        case 'rating':
          orderBy = [
            { averageRating: 'desc' },
            { reviewCount: 'desc' }
          ]
          break
        case 'newest':
          orderBy = { createdAt: 'desc' }
          break
        case 'price_low':
          orderBy = { price: 'asc' }
          break
        case 'price_high':
          orderBy = { price: 'desc' }
          break
        default: // relevance
          if (q) {
            // For text search, prioritize title matches, then description matches
            orderBy = [
              { enrollmentCount: 'desc' },
              { averageRating: 'desc' },
              { createdAt: 'desc' }
            ]
          } else {
            orderBy = [
              { isFeatured: 'desc' },
              { enrollmentCount: 'desc' },
              { averageRating: 'desc' }
            ]
          }
      }

      // Get total count
      const total = await prisma.course.count({
        where: searchConditions
      })

      // Get courses with pagination
      const courses = await prisma.course.findMany({
        where: searchConditions,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true
            }
          },
          _count: {
            select: {
              enrollments: {
                where: { status: 'ACTIVE' }
              },
              reviews: true
            }
          }
        }
      })

      // Format response
      const formattedCourses = courses.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        shortDescription: course.shortDescription,
        thumbnailImage: course.thumbnailImage,
        price: course.price,
        originalPrice: course.originalPrice,
        currency: course.currency,
        level: course.level,
        category: course.category,
        tags: course.tags,
        language: course.language,
        estimatedDuration: course.estimatedDuration,
        averageRating: course.averageRating,
        reviewCount: course._count.reviews,
        enrollmentCount: course._count.enrollments,
        isFeatured: course.isFeatured,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        instructor: course.instructor
      }))

      return APIResponse.success({
        courses: formattedCourses,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        },
        filters: {
          query: q,
          category,
          level,
          duration,
          price,
          rating,
          instructor,
          tags,
          sortBy,
          featured,
          language
        }
      })

    } catch (error) {
      console.error('Error searching courses:', error)
      return APIResponse.error(
        'Failed to search courses: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// GET /api/search/courses/suggestions - Get search suggestions
export const POST = createAPIHandler(
  {
    requireAuth: false,
    validateBody: z.object({
      query: z.string().min(1),
      limit: z.number().min(1).max(10).default(5)
    })
  },
  async (request: NextRequest, { validatedBody }) => {
    try {
      const { query, limit } = validatedBody

      // Get course title suggestions
      const courseSuggestions = await prisma.course.findMany({
        where: {
          isPublished: true,
          isDeleted: false,
          title: {
            contains: query,
            mode: 'insensitive'
          }
        },
        select: {
          id: true,
          title: true,
          thumbnailImage: true
        },
        take: limit,
        orderBy: {
          enrollmentCount: 'desc'
        }
      })

      // Get instructor suggestions
      const instructorSuggestions = await prisma.user.findMany({
        where: {
          role: 'INSTRUCTOR',
          name: {
            contains: query,
            mode: 'insensitive'
          }
        },
        select: {
          id: true,
          name: true,
          image: true
        },
        take: Math.min(limit, 3)
      })

      // Get tag suggestions
      const tagSuggestions = await prisma.course.findMany({
        where: {
          isPublished: true,
          isDeleted: false,
          tags: {
            hasSome: [query]
          }
        },
        select: {
          tags: true
        },
        take: 20
      })

      // Extract and filter unique tags
      const allTags = tagSuggestions.flatMap(course => course.tags)
      const uniqueTags = [...new Set(allTags)]
        .filter(tag => tag.toLowerCase().includes(query.toLowerCase()))
        .slice(0, limit)

      return APIResponse.success({
        courses: courseSuggestions.map(course => ({
          type: 'course',
          id: course.id,
          title: course.title,
          image: course.thumbnailImage
        })),
        instructors: instructorSuggestions.map(instructor => ({
          type: 'instructor',
          id: instructor.id,
          name: instructor.name,
          image: instructor.image
        })),
        tags: uniqueTags.map(tag => ({
          type: 'tag',
          name: tag
        }))
      })

    } catch (error) {
      console.error('Error getting search suggestions:', error)
      return APIResponse.error(
        'Failed to get suggestions: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
