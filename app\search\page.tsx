'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  TrendingUp, 
  <PERSON>rkles, 
  Clock, 
  Star,
  BookOpen,
  Users,
  Filter,
  Search as SearchIcon
} from 'lucide-react'
import { toast } from 'sonner'

import { AdvancedSearch } from '@/components/search/advanced-search'
import { SearchResults } from '@/components/search/search-results'
import { GlassmorphicCard, GlassCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicButton } from '@/components/ui/glassmorphic-button'
import { AnimatedBackground, GradientText } from '@/components/ui/animated-background'

interface Course {
  id: string
  title: string
  description: string
  shortDescription: string
  thumbnailImage?: string
  price: number
  originalPrice?: number
  currency: string
  level: string
  category: string
  tags: string[]
  language: string
  estimatedDuration: number
  averageRating: number
  reviewCount: number
  enrollmentCount: number
  isFeatured: boolean
  createdAt: string
  instructor: {
    id: string
    name: string
    image?: string
  }
  trending?: {
    score: number
    recentEnrollments: number
    period: string
  }
  recommendationScore?: number
  recommendationReason?: string
}

interface SearchFilters {
  q: string
  category: string
  level: string
  duration: string
  price: string
  rating: number
  instructor: string
  tags: string
  sortBy: string
  featured: boolean
  language: string
}

export default function SearchPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [courses, setCourses] = useState<Course[]>([])
  const [trendingCourses, setTrendingCourses] = useState<Course[]>([])
  const [recommendedCourses, setRecommendedCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(false)
  const [totalResults, setTotalResults] = useState(0)
  const [activeFilters, setActiveFilters] = useState<SearchFilters | null>(null)

  // Get initial query from URL params
  const initialQuery = searchParams.get('q') || ''

  // Fetch trending courses
  useEffect(() => {
    const fetchTrendingCourses = async () => {
      try {
        const response = await fetch('/api/courses/trending?limit=6')
        if (response.ok) {
          const data = await response.json()
          setTrendingCourses(data.courses)
        }
      } catch (error) {
        console.error('Error fetching trending courses:', error)
      }
    }

    fetchTrendingCourses()
  }, [])

  // Fetch recommended courses
  useEffect(() => {
    const fetchRecommendedCourses = async () => {
      try {
        const response = await fetch('/api/courses/recommendations?type=popular&limit=6')
        if (response.ok) {
          const data = await response.json()
          setRecommendedCourses(data.courses)
        }
      } catch (error) {
        console.error('Error fetching recommended courses:', error)
      }
    }

    fetchRecommendedCourses()
  }, [])

  // Handle search results
  const handleSearchResults = (results: Course[], filters: SearchFilters) => {
    setCourses(results)
    setActiveFilters(filters)
    setTotalResults(results.length)
  }

  // Handle course selection
  const handleCourseSelect = (courseId: string) => {
    router.push(`/courses/${courseId}`)
  }

  // Handle course enrollment
  const handleCourseEnroll = async (courseId: string) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/enroll`, {
        method: 'POST'
      })

      if (response.ok) {
        toast.success('Successfully enrolled in course!')
        router.push(`/student/courses/${courseId}`)
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to enroll in course')
      }
    } catch (error) {
      console.error('Enrollment error:', error)
      toast.error('Failed to enroll in course')
    }
  }

  // Handle quick search actions
  const handleQuickSearch = (query: string) => {
    // This will trigger the search through the AdvancedSearch component
    window.history.pushState({}, '', `/search?q=${encodeURIComponent(query)}`)
  }

  const hasSearchResults = courses.length > 0 || (activeFilters && Object.values(activeFilters).some(v => v && v !== '' && v !== 0 && v !== false))

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Animated Background */}
      <AnimatedBackground variant="aurora" intensity="low" speed="slow" className="fixed inset-0" />
      
      <div className="relative z-10">
        {/* Header */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
          <div className="max-w-7xl mx-auto px-6 py-8">
            <div className="text-center">
              <motion.h1 
                className="text-4xl font-bold mb-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <GradientText>Discover Amazing Courses</GradientText>
              </motion.h1>
              <motion.p 
                className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                Find the perfect course to advance your skills and achieve your goals
              </motion.p>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Search Interface */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <AdvancedSearch
              onResults={handleSearchResults}
              onLoading={setLoading}
              initialQuery={initialQuery}
              className="mb-8"
            />
          </motion.div>

          {/* Search Results or Discovery Content */}
          {hasSearchResults ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <SearchResults
                courses={courses}
                loading={loading}
                query={activeFilters?.q || ''}
                totalResults={totalResults}
                onCourseSelect={handleCourseSelect}
                onEnroll={handleCourseEnroll}
              />
            </motion.div>
          ) : (
            <div className="space-y-12">
              {/* Quick Search Suggestions */}
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <GlassCard className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <SearchIcon className="w-6 h-6 mr-3 text-violet-500" />
                    Popular Searches
                  </h2>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      'JavaScript', 'React', 'Python', 'Machine Learning',
                      'UI/UX Design', 'Digital Marketing', 'Photography', 'Business'
                    ].map((term) => (
                      <GlassmorphicButton
                        key={term}
                        variant="glass"
                        className="justify-start"
                        onClick={() => handleQuickSearch(term)}
                      >
                        {term}
                      </GlassmorphicButton>
                    ))}
                  </div>
                </GlassCard>
              </motion.section>

              {/* Trending Courses */}
              {trendingCourses.length > 0 && (
                <motion.section
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                      <TrendingUp className="w-6 h-6 mr-3 text-red-500" />
                      Trending This Week
                    </h2>
                    <GlassmorphicButton
                      variant="ghost"
                      onClick={() => handleQuickSearch('trending')}
                    >
                      View All
                    </GlassmorphicButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {trendingCourses.map((course, index) => (
                      <motion.div
                        key={course.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                      >
                        <TrendingCourseCard
                          course={course}
                          onSelect={() => handleCourseSelect(course.id)}
                          onEnroll={() => handleCourseEnroll(course.id)}
                        />
                      </motion.div>
                    ))}
                  </div>
                </motion.section>
              )}

              {/* Recommended Courses */}
              {recommendedCourses.length > 0 && (
                <motion.section
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                      <Sparkles className="w-6 h-6 mr-3 text-yellow-500" />
                      Recommended for You
                    </h2>
                    <GlassmorphicButton
                      variant="ghost"
                      onClick={() => handleQuickSearch('recommended')}
                    >
                      View All
                    </GlassmorphicButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {recommendedCourses.map((course, index) => (
                      <motion.div
                        key={course.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index }}
                      >
                        <RecommendedCourseCard
                          course={course}
                          onSelect={() => handleCourseSelect(course.id)}
                          onEnroll={() => handleCourseEnroll(course.id)}
                        />
                      </motion.div>
                    ))}
                  </div>
                </motion.section>
              )}

              {/* Browse by Category */}
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <GlassCard className="p-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <Filter className="w-6 h-6 mr-3 text-violet-500" />
                    Browse by Category
                  </h2>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    {[
                      { name: 'Programming', icon: '💻', color: 'from-blue-500 to-blue-600' },
                      { name: 'Design', icon: '🎨', color: 'from-pink-500 to-pink-600' },
                      { name: 'Business', icon: '💼', color: 'from-green-500 to-green-600' },
                      { name: 'Marketing', icon: '📈', color: 'from-purple-500 to-purple-600' },
                      { name: 'Photography', icon: '📸', color: 'from-yellow-500 to-yellow-600' },
                      { name: 'Music', icon: '🎵', color: 'from-red-500 to-red-600' },
                      { name: 'Health', icon: '🏃', color: 'from-teal-500 to-teal-600' },
                      { name: 'Language', icon: '🌍', color: 'from-indigo-500 to-indigo-600' },
                      { name: 'Data Science', icon: '📊', color: 'from-orange-500 to-orange-600' },
                      { name: 'Personal Dev', icon: '🚀', color: 'from-violet-500 to-violet-600' }
                    ].map((category) => (
                      <GlassmorphicButton
                        key={category.name}
                        variant="glass"
                        className="h-20 flex-col space-y-2"
                        onClick={() => handleQuickSearch(category.name)}
                      >
                        <span className="text-2xl">{category.icon}</span>
                        <span className="text-sm font-medium">{category.name}</span>
                      </GlassmorphicButton>
                    ))}
                  </div>
                </GlassCard>
              </motion.section>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Trending Course Card Component (simplified)
function TrendingCourseCard({ course, onSelect, onEnroll }: any) {
  return (
    <GlassmorphicCard className="overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer" onClick={onSelect}>
      <div className="relative h-32 bg-gradient-to-br from-red-500 to-red-600">
        <div className="absolute top-2 left-2">
          <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full flex items-center">
            <TrendingUp className="w-3 h-3 mr-1" />
            Trending
          </span>
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
          {course.title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          {course.instructor.name}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span>{course.averageRating.toFixed(1)}</span>
          </div>
          <GlassmorphicButton variant="primary" size="sm" onClick={(e: any) => { e.stopPropagation(); onEnroll(); }}>
            Enroll
          </GlassmorphicButton>
        </div>
      </div>
    </GlassmorphicCard>
  )
}

// Recommended Course Card Component (simplified)
function RecommendedCourseCard({ course, onSelect, onEnroll }: any) {
  return (
    <GlassmorphicCard className="overflow-hidden hover:shadow-lg transition-all duration-200 cursor-pointer" onClick={onSelect}>
      <div className="relative h-32 bg-gradient-to-br from-yellow-500 to-yellow-600">
        <div className="absolute top-2 left-2">
          <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full flex items-center">
            <Sparkles className="w-3 h-3 mr-1" />
            Recommended
          </span>
        </div>
      </div>
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
          {course.title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          {course.instructor.name}
        </p>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span>{course.averageRating.toFixed(1)}</span>
          </div>
          <GlassmorphicButton variant="primary" size="sm" onClick={(e: any) => { e.stopPropagation(); onEnroll(); }}>
            Enroll
          </GlassmorphicButton>
        </div>
      </div>
    </GlassmorphicCard>
  )
}
