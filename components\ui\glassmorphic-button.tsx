'use client'

import React, { forwardRef } from 'react'
import { motion, HTMLMotionProps } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'

interface GlassmorphicButtonProps extends Omit<HTMLMotionProps<'button'>, 'size'> {
  variant?: 'primary' | 'secondary' | 'glass' | 'ghost' | 'outline' | 'gradient'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  disabled?: boolean
  glow?: boolean
  ripple?: boolean
  children: React.ReactNode
  className?: string
}

const buttonVariants = {
  primary: {
    base: 'bg-gradient-to-r from-violet-500 to-purple-600 text-white shadow-lg shadow-violet-500/25',
    hover: 'from-violet-600 to-purple-700 shadow-xl shadow-violet-500/30 -translate-y-0.5',
    active: 'scale-95'
  },
  secondary: {
    base: 'bg-gradient-to-r from-gray-500 to-gray-600 text-white shadow-lg shadow-gray-500/25',
    hover: 'from-gray-600 to-gray-700 shadow-xl shadow-gray-500/30 -translate-y-0.5',
    active: 'scale-95'
  },
  glass: {
    base: 'bg-white/10 backdrop-blur-md border border-white/20 text-gray-900 dark:text-white shadow-lg',
    hover: 'bg-white/20 border-white/30 shadow-xl -translate-y-0.5',
    active: 'scale-95'
  },
  ghost: {
    base: 'bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800',
    hover: 'bg-gray-100 dark:bg-gray-800',
    active: 'scale-95'
  },
  outline: {
    base: 'bg-transparent border-2 border-violet-500 text-violet-500 hover:bg-violet-500 hover:text-white',
    hover: 'bg-violet-500 text-white shadow-lg shadow-violet-500/25',
    active: 'scale-95'
  },
  gradient: {
    base: 'bg-gradient-to-r from-violet-500 via-purple-500 to-pink-500 text-white shadow-lg',
    hover: 'from-violet-600 via-purple-600 to-pink-600 shadow-xl -translate-y-0.5',
    active: 'scale-95'
  }
}

const sizeVariants = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
  xl: 'px-8 py-4 text-xl'
}

export const GlassmorphicButton = forwardRef<HTMLButtonElement, GlassmorphicButtonProps>(
  ({ 
    variant = 'primary',
    size = 'md',
    loading = false,
    disabled = false,
    glow = false,
    ripple = true,
    children,
    className,
    onClick,
    ...props 
  }, ref) => {
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number }>>([])
    
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || loading) return
      
      // Create ripple effect
      if (ripple) {
        const rect = e.currentTarget.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top
        const newRipple = { id: Date.now(), x, y }
        
        setRipples(prev => [...prev, newRipple])
        
        // Remove ripple after animation
        setTimeout(() => {
          setRipples(prev => prev.filter(r => r.id !== newRipple.id))
        }, 600)
      }
      
      onClick?.(e)
    }

    const variantStyles = buttonVariants[variant]
    const sizeStyles = sizeVariants[size]

    return (
      <motion.button
        ref={ref}
        className={cn(
          // Base styles
          'relative inline-flex items-center justify-center',
          'font-medium rounded-xl',
          'transition-all duration-200 ease-out',
          'focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'overflow-hidden',
          
          // Size styles
          sizeStyles,
          
          // Variant base styles
          variantStyles.base,
          
          // Glow effect
          glow && 'shadow-2xl shadow-violet-500/50',
          
          // Custom className
          className
        )}
        disabled={disabled || loading}
        onClick={handleClick}
        whileHover={!disabled && !loading ? {
          scale: 1.05,
          y: -2
        } : undefined}
        whileTap={!disabled && !loading ? {
          scale: 0.95
        } : undefined}
        transition={{
          type: 'spring',
          stiffness: 400,
          damping: 25
        }}
        {...props}
      >
        {/* Background gradient overlay for glass variant */}
        {variant === 'glass' && (
          <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 rounded-xl" />
        )}
        
        {/* Glow effect */}
        {glow && (
          <div className="absolute -inset-1 bg-gradient-to-r from-violet-500 to-purple-500 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-300" />
        )}
        
        {/* Shimmer effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-700 ease-out" />
        
        {/* Ripple effects */}
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        ))}
        
        {/* Content */}
        <span className="relative z-10 flex items-center justify-center space-x-2">
          {loading && (
            <Loader2 className="w-4 h-4 animate-spin" />
          )}
          <span>{children}</span>
        </span>
      </motion.button>
    )
  }
)

GlassmorphicButton.displayName = 'GlassmorphicButton'

// Specialized button variants
export const PrimaryButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="primary" className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

export const SecondaryButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="secondary" className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

export const GlassButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="glass" className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

export const GhostButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="ghost" className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

export const OutlineButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="outline" className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

export const GradientButton = ({ children, className, ...props }: Omit<GlassmorphicButtonProps, 'variant'>) => (
  <GlassmorphicButton variant="gradient" glow={true} className={className} {...props}>
    {children}
  </GlassmorphicButton>
)

// Floating Action Button
export const FloatingActionButton = ({ 
  children, 
  className,
  position = 'bottom-right',
  ...props 
}: GlassmorphicButtonProps & { 
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' 
}) => {
  const positionClasses = {
    'bottom-right': 'fixed bottom-6 right-6',
    'bottom-left': 'fixed bottom-6 left-6',
    'top-right': 'fixed top-6 right-6',
    'top-left': 'fixed top-6 left-6'
  }

  return (
    <GlassmorphicButton
      variant="primary"
      size="lg"
      glow={true}
      className={cn(
        'z-50 rounded-full w-14 h-14 p-0',
        'shadow-2xl shadow-violet-500/50',
        positionClasses[position],
        className
      )}
      whileHover={{
        scale: 1.1,
        rotate: 5
      }}
      whileTap={{
        scale: 0.9
      }}
      {...props}
    >
      {children}
    </GlassmorphicButton>
  )
}

// Icon Button
export const IconButton = ({ 
  children, 
  className,
  variant = 'glass',
  size = 'md',
  ...props 
}: GlassmorphicButtonProps) => {
  const iconSizes = {
    sm: 'w-8 h-8 p-1.5',
    md: 'w-10 h-10 p-2',
    lg: 'w-12 h-12 p-2.5',
    xl: 'w-14 h-14 p-3'
  }

  return (
    <GlassmorphicButton
      variant={variant}
      className={cn(
        'rounded-full',
        iconSizes[size],
        className
      )}
      {...props}
    >
      {children}
    </GlassmorphicButton>
  )
}

// Button Group
export const ButtonGroup = ({ 
  children, 
  className,
  orientation = 'horizontal'
}: {
  children: React.ReactNode
  className?: string
  orientation?: 'horizontal' | 'vertical'
}) => (
  <div className={cn(
    'inline-flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    '[&>button]:rounded-none',
    '[&>button:first-child]:rounded-l-xl',
    '[&>button:last-child]:rounded-r-xl',
    orientation === 'vertical' && '[&>button:first-child]:rounded-t-xl [&>button:first-child]:rounded-l-none',
    orientation === 'vertical' && '[&>button:last-child]:rounded-b-xl [&>button:last-child]:rounded-r-none',
    '[&>button:not(:last-child)]:border-r-0',
    orientation === 'vertical' && '[&>button:not(:last-child)]:border-r [&>button:not(:last-child)]:border-b-0',
    className
  )}>
    {children}
  </div>
)
