import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from 'next-themes'
import { Toaster } from 'sonner'

// Mock session data
export const mockSession = {
  user: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'STUDENT',
    image: null
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
}

export const mockInstructorSession = {
  user: {
    id: 'test-instructor-id',
    name: 'Test Instructor',
    email: '<EMAIL>',
    role: 'INSTRUCTOR',
    image: null
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
}

export const mockAdminSession = {
  user: {
    id: 'test-admin-id',
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'ADMIN',
    image: null
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
}

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
  session?: any
  queryClient?: QueryClient
}

function TestWrapper({ children, session = mockSession, queryClient }: TestWrapperProps) {
  const testQueryClient = queryClient || new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0
      }
    }
  })

  return (
    <SessionProvider session={session}>
      <QueryClientProvider client={testQueryClient}>
        <ThemeProvider attribute="class" defaultTheme="light">
          {children}
          <Toaster />
        </ThemeProvider>
      </QueryClientProvider>
    </SessionProvider>
  )
}

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  session?: any
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  const { session, queryClient, ...renderOptions } = options

  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper session={session} queryClient={queryClient}>
        {children}
      </TestWrapper>
    ),
    ...renderOptions
  })
}

// Mock data generators
export const mockCourse = {
  id: 'test-course-id',
  title: 'Test Course',
  description: 'This is a test course description',
  shortDescription: 'Test course',
  thumbnailImage: 'https://example.com/image.jpg',
  price: 99.99,
  originalPrice: 149.99,
  currency: 'USD',
  level: 'BEGINNER',
  category: 'Programming',
  tags: ['javascript', 'react', 'web development'],
  language: 'English',
  estimatedDuration: 600, // 10 hours
  averageRating: 4.5,
  reviewCount: 150,
  enrollmentCount: 1200,
  isFeatured: true,
  isPublished: true,
  isDeleted: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  instructor: {
    id: 'test-instructor-id',
    name: 'Test Instructor',
    image: 'https://example.com/instructor.jpg',
    bio: 'Experienced instructor'
  }
}

export const mockQuiz = {
  id: 'test-quiz-id',
  title: 'Test Quiz',
  description: 'This is a test quiz',
  difficulty: 'MEDIUM',
  timeLimit: 30,
  passingScore: 70,
  totalQuestions: 10,
  isPublished: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  questions: [
    {
      id: 'test-question-1',
      question: 'What is React?',
      type: 'MULTIPLE_CHOICE',
      options: [
        { id: 'opt-1', text: 'A JavaScript library', isCorrect: true },
        { id: 'opt-2', text: 'A database', isCorrect: false },
        { id: 'opt-3', text: 'A server', isCorrect: false },
        { id: 'opt-4', text: 'An operating system', isCorrect: false }
      ]
    }
  ]
}

export const mockNotification = {
  id: 'test-notification-id',
  type: 'COURSE',
  title: 'New Course Available',
  message: 'A new course has been published',
  priority: 'normal',
  isRead: false,
  actionUrl: '/courses/test-course-id',
  createdAt: new Date().toISOString()
}

// API mocking utilities
export const mockApiResponse = {
  success: (data: any) => ({
    ok: true,
    json: async () => ({ success: true, data }),
    status: 200
  }),
  
  error: (message: string, status = 400) => ({
    ok: false,
    json: async () => ({ success: false, error: message }),
    status
  })
}

// Mock fetch function
export function mockFetch(responses: Record<string, any>) {
  const originalFetch = global.fetch
  
  global.fetch = jest.fn((url: string, options?: any) => {
    const method = options?.method || 'GET'
    const key = `${method} ${url}`
    
    if (responses[key]) {
      return Promise.resolve(responses[key])
    }
    
    // Default response
    return Promise.resolve(mockApiResponse.error('Not found', 404))
  }) as jest.Mock

  return () => {
    global.fetch = originalFetch
  }
}

// Performance testing utilities
export class PerformanceTester {
  private measurements: Map<string, number[]> = new Map()

  async measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    
    this.recordMeasurement(name, end - start)
    return result
  }

  measure<T>(name: string, fn: () => T): T {
    const start = performance.now()
    const result = fn()
    const end = performance.now()
    
    this.recordMeasurement(name, end - start)
    return result
  }

  private recordMeasurement(name: string, duration: number) {
    if (!this.measurements.has(name)) {
      this.measurements.set(name, [])
    }
    this.measurements.get(name)!.push(duration)
  }

  getStats(name: string) {
    const measurements = this.measurements.get(name) || []
    if (measurements.length === 0) {
      return null
    }

    const sorted = [...measurements].sort((a, b) => a - b)
    const sum = measurements.reduce((a, b) => a + b, 0)

    return {
      count: measurements.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      average: sum / measurements.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    }
  }

  getAllStats() {
    const stats: Record<string, any> = {}
    for (const [name] of this.measurements) {
      stats[name] = this.getStats(name)
    }
    return stats
  }

  reset() {
    this.measurements.clear()
  }
}

// Accessibility testing utilities
export const a11yUtils = {
  // Check for common accessibility issues
  checkAccessibility: async (container: HTMLElement) => {
    const issues: string[] = []

    // Check for images without alt text
    const images = container.querySelectorAll('img')
    images.forEach((img, index) => {
      if (!img.getAttribute('alt')) {
        issues.push(`Image ${index + 1} missing alt text`)
      }
    })

    // Check for buttons without accessible names
    const buttons = container.querySelectorAll('button')
    buttons.forEach((button, index) => {
      const hasText = button.textContent?.trim()
      const hasAriaLabel = button.getAttribute('aria-label')
      const hasAriaLabelledBy = button.getAttribute('aria-labelledby')
      
      if (!hasText && !hasAriaLabel && !hasAriaLabelledBy) {
        issues.push(`Button ${index + 1} missing accessible name`)
      }
    })

    // Check for form inputs without labels
    const inputs = container.querySelectorAll('input, textarea, select')
    inputs.forEach((input, index) => {
      const id = input.getAttribute('id')
      const hasLabel = id && container.querySelector(`label[for="${id}"]`)
      const hasAriaLabel = input.getAttribute('aria-label')
      const hasAriaLabelledBy = input.getAttribute('aria-labelledby')
      
      if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        issues.push(`Form input ${index + 1} missing label`)
      }
    })

    return issues
  }
}

// Visual regression testing utilities
export const visualTesting = {
  // Take screenshot for visual comparison
  takeScreenshot: async (element: HTMLElement, name: string) => {
    // This would integrate with a visual testing tool like Percy or Chromatic
    console.log(`Taking screenshot: ${name}`)
    return `screenshot-${name}-${Date.now()}.png`
  },

  // Compare screenshots
  compareScreenshots: async (baseline: string, current: string) => {
    // This would compare two screenshots and return differences
    console.log(`Comparing ${baseline} with ${current}`)
    return {
      identical: true,
      differences: 0,
      diffImage: null
    }
  }
}

// Load testing utilities
export const loadTesting = {
  // Simulate concurrent users
  simulateConcurrentUsers: async (userCount: number, testFn: () => Promise<void>) => {
    const promises = Array.from({ length: userCount }, () => testFn())
    
    const start = performance.now()
    const results = await Promise.allSettled(promises)
    const end = performance.now()

    const successful = results.filter(r => r.status === 'fulfilled').length
    const failed = results.filter(r => r.status === 'rejected').length

    return {
      totalUsers: userCount,
      successful,
      failed,
      duration: end - start,
      successRate: (successful / userCount) * 100
    }
  },

  // Memory usage monitoring
  monitorMemoryUsage: () => {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }
}

// Test data cleanup utilities
export const testCleanup = {
  // Clean up test data from database
  cleanupTestData: async () => {
    // This would clean up any test data created during tests
    console.log('Cleaning up test data...')
  },

  // Reset mocks
  resetMocks: () => {
    jest.clearAllMocks()
    jest.resetAllMocks()
  }
}

// Export everything for easy importing
export * from '@testing-library/react'
export * from '@testing-library/jest-dom'
export * from '@testing-library/user-event'
