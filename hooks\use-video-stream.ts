'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { toast } from 'sonner'

interface VideoStreamData {
  content: {
    id: string
    title: string
    description?: string
    duration?: number
    thumbnailUrl?: string
    type: string
  }
  streaming: {
    videoUrl: string
    expiresAt: string
    qualities: any[]
    isSecure: boolean
  }
  progress: {
    watchedDuration: number
    totalDuration: number
    lastPosition: number
    isCompleted: boolean
    watchPercentage: number
  }
  course: {
    id: string
    title: string
  }
  topic: {
    id: string
    title: string
  }
  section: {
    id: string
    title: string
  }
  access: {
    isEnrolled: boolean
    isFreePreview: boolean
    enrollmentStatus: string | null
  }
}

interface ProgressUpdate {
  currentTime: number
  duration: number
  watchedDuration: number
  isCompleted: boolean
}

export function useVideoStream(contentId: string) {
  const [data, setData] = useState<VideoStreamData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUpdatingProgress, setIsUpdatingProgress] = useState(false)
  
  const progressUpdateTimeoutRef = useRef<NodeJS.Timeout>()
  const lastProgressUpdateRef = useRef<ProgressUpdate | null>(null)

  // Fetch video stream data
  const fetchStreamData = useCallback(async () => {
    if (!contentId) return

    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/api/student/stream/${contentId}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to load video')
      }

      const streamData = await response.json()
      setData(streamData)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load video'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }, [contentId])

  // Update video progress with debouncing
  const updateProgress = useCallback(async (progress: ProgressUpdate) => {
    if (!contentId || isUpdatingProgress) return

    // Store the latest progress update
    lastProgressUpdateRef.current = progress

    // Clear existing timeout
    if (progressUpdateTimeoutRef.current) {
      clearTimeout(progressUpdateTimeoutRef.current)
    }

    // Debounce progress updates (send every 5 seconds or on completion)
    const delay = progress.isCompleted ? 0 : 5000

    progressUpdateTimeoutRef.current = setTimeout(async () => {
      const latestProgress = lastProgressUpdateRef.current
      if (!latestProgress) return

      try {
        setIsUpdatingProgress(true)

        const response = await fetch(`/api/student/stream/${contentId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            currentTime: latestProgress.currentTime,
            duration: latestProgress.duration,
            watchedDuration: latestProgress.watchedDuration,
            isCompleted: latestProgress.isCompleted
          })
        })

        if (!response.ok) {
          throw new Error('Failed to update progress')
        }

        const result = await response.json()
        
        // Update local progress data
        if (data) {
          setData(prev => prev ? {
            ...prev,
            progress: {
              ...prev.progress,
              ...result.progress
            }
          } : null)
        }

        // Show completion message
        if (latestProgress.isCompleted && !data?.progress.isCompleted) {
          toast.success('Video completed! 🎉')
        }

      } catch (err) {
        console.error('Failed to update video progress:', err)
        // Don't show error toast for progress updates to avoid spam
      } finally {
        setIsUpdatingProgress(false)
      }
    }, delay)

  }, [contentId, isUpdatingProgress, data])

  // Refresh stream URL if expired
  const refreshStreamUrl = useCallback(async () => {
    if (!data?.streaming.expiresAt) return

    const expirationTime = new Date(data.streaming.expiresAt).getTime()
    const currentTime = Date.now()
    const timeUntilExpiry = expirationTime - currentTime

    // Refresh if expires in less than 30 minutes
    if (timeUntilExpiry < 30 * 60 * 1000) {
      await fetchStreamData()
    }
  }, [data, fetchStreamData])

  // Check for URL expiration periodically
  useEffect(() => {
    if (!data?.streaming.expiresAt) return

    const interval = setInterval(() => {
      refreshStreamUrl()
    }, 10 * 60 * 1000) // Check every 10 minutes

    return () => clearInterval(interval)
  }, [data, refreshStreamUrl])

  // Initial data fetch
  useEffect(() => {
    fetchStreamData()
  }, [fetchStreamData])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (progressUpdateTimeoutRef.current) {
        clearTimeout(progressUpdateTimeoutRef.current)
      }
    }
  }, [])

  // Force progress update on unmount or page leave
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (lastProgressUpdateRef.current && !isUpdatingProgress) {
        // Send final progress update synchronously
        const progress = lastProgressUpdateRef.current
        navigator.sendBeacon(
          `/api/student/stream/${contentId}`,
          JSON.stringify({
            currentTime: progress.currentTime,
            duration: progress.duration,
            watchedDuration: progress.watchedDuration,
            isCompleted: progress.isCompleted
          })
        )
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [contentId, isUpdatingProgress])

  return {
    data,
    isLoading,
    error,
    isUpdatingProgress,
    updateProgress,
    refreshStreamData: fetchStreamData,
    refreshStreamUrl
  }
}

// Hook for managing multiple video streams (for playlists)
export function useVideoPlaylist(contentIds: string[]) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [playlist, setPlaylist] = useState<VideoStreamData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const currentContentId = contentIds[currentIndex]
  const { data: currentVideo, updateProgress } = useVideoStream(currentContentId)

  // Load all videos in playlist
  useEffect(() => {
    const loadPlaylist = async () => {
      if (contentIds.length === 0) return

      try {
        setIsLoading(true)
        setError(null)

        const promises = contentIds.map(async (contentId) => {
          const response = await fetch(`/api/student/stream/${contentId}`)
          if (!response.ok) {
            throw new Error(`Failed to load video ${contentId}`)
          }
          return response.json()
        })

        const results = await Promise.all(promises)
        setPlaylist(results)

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load playlist'
        setError(errorMessage)
        toast.error(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }

    loadPlaylist()
  }, [contentIds])

  const goToNext = useCallback(() => {
    if (currentIndex < contentIds.length - 1) {
      setCurrentIndex(prev => prev + 1)
    }
  }, [currentIndex, contentIds.length])

  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1)
    }
  }, [currentIndex])

  const goToIndex = useCallback((index: number) => {
    if (index >= 0 && index < contentIds.length) {
      setCurrentIndex(index)
    }
  }, [contentIds.length])

  return {
    playlist,
    currentVideo,
    currentIndex,
    isLoading,
    error,
    hasNext: currentIndex < contentIds.length - 1,
    hasPrevious: currentIndex > 0,
    goToNext,
    goToPrevious,
    goToIndex,
    updateProgress
  }
}
