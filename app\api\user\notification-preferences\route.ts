import { NextRequest } from 'next/server'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const notificationPreferencesSchema = z.object({
  preferences: z.object({
    email: z.object({
      courseUpdates: z.boolean().default(true),
      assignments: z.boolean().default(true),
      discussions: z.boolean().default(false),
      achievements: z.boolean().default(true),
      systemUpdates: z.boolean().default(true),
      marketing: z.boolean().default(false)
    }).optional(),
    push: z.object({
      courseUpdates: z.boolean().default(true),
      assignments: z.boolean().default(true),
      discussions: z.boolean().default(true),
      achievements: z.boolean().default(true),
      systemUpdates: z.boolean().default(false),
      liveQuiz: z.boolean().default(true)
    }).optional(),
    inApp: z.object({
      courseUpdates: z.boolean().default(true),
      assignments: z.boolean().default(true),
      discussions: z.boolean().default(true),
      achievements: z.boolean().default(true),
      systemUpdates: z.boolean().default(true),
      liveQuiz: z.boolean().default(true)
    }).optional(),
    sound: z.object({
      enabled: z.boolean().default(true),
      volume: z.number().min(0).max(100).default(50),
      highPriorityOnly: z.boolean().default(false)
    }).optional(),
    quietHours: z.object({
      enabled: z.boolean().default(false),
      startTime: z.string().default('22:00'),
      endTime: z.string().default('08:00'),
      timezone: z.string().default('UTC')
    }).optional(),
    frequency: z.object({
      digest: z.enum(['never', 'daily', 'weekly']).default('never'),
      digestTime: z.string().default('09:00')
    }).optional()
  })
})

// GET /api/user/notification-preferences - Get user notification preferences
export const GET = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      // Get user preferences from database
      const userPreferences = await prisma.userNotificationPreferences.findUnique({
        where: { userId: user.id }
      })

      // Default preferences
      const defaultPreferences = {
        email: {
          courseUpdates: true,
          assignments: true,
          discussions: false,
          achievements: true,
          systemUpdates: true,
          marketing: false
        },
        push: {
          courseUpdates: true,
          assignments: true,
          discussions: true,
          achievements: true,
          systemUpdates: false,
          liveQuiz: true
        },
        inApp: {
          courseUpdates: true,
          assignments: true,
          discussions: true,
          achievements: true,
          systemUpdates: true,
          liveQuiz: true
        },
        sound: {
          enabled: true,
          volume: 50,
          highPriorityOnly: false
        },
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC'
        },
        frequency: {
          digest: 'never' as const,
          digestTime: '09:00'
        }
      }

      // Merge with saved preferences
      const preferences = userPreferences 
        ? { ...defaultPreferences, ...userPreferences.preferences }
        : defaultPreferences

      return APIResponse.success({
        preferences,
        lastUpdated: userPreferences?.updatedAt || null
      })

    } catch (error) {
      console.error('Error fetching notification preferences:', error)
      return APIResponse.error(
        'Failed to fetch notification preferences: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/user/notification-preferences - Update user notification preferences
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    validateBody: notificationPreferencesSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { preferences } = validatedBody

      // Upsert user preferences
      const updatedPreferences = await prisma.userNotificationPreferences.upsert({
        where: { userId: user.id },
        update: {
          preferences: preferences as any,
          updatedAt: new Date()
        },
        create: {
          userId: user.id,
          preferences: preferences as any
        }
      })

      return APIResponse.success({
        preferences: updatedPreferences.preferences,
        message: 'Notification preferences updated successfully',
        lastUpdated: updatedPreferences.updatedAt
      })

    } catch (error) {
      console.error('Error updating notification preferences:', error)
      return APIResponse.error(
        'Failed to update notification preferences: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/user/notification-preferences - Reset to default preferences
export const DELETE = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      // Delete user preferences (will fall back to defaults)
      await prisma.userNotificationPreferences.deleteMany({
        where: { userId: user.id }
      })

      return APIResponse.success({
        message: 'Notification preferences reset to defaults'
      })

    } catch (error) {
      console.error('Error resetting notification preferences:', error)
      return APIResponse.error(
        'Failed to reset notification preferences: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
