'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  Heart, 
  Reply, 
  MoreHorizontal, 
  Pin, 
  Lock, 
  Edit3, 
  Trash2,
  User,
  Clock,
  Send,
  Quote,
  Flag,
  Share2
} from 'lucide-react'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'
import { getCategoryIcon } from './discussion-forum'

interface Discussion {
  id: string
  title: string
  content: string
  category: string
  isSticky: boolean
  isPinned: boolean
  isLocked: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string
    image?: string
    role: 'STUDENT' | 'INSTRUCTOR'
  }
  topic?: {
    id: string
    title: string
    section: {
      id: string
      title: string
    }
  }
  replyCount: number
  likeCount: number
  isLiked: boolean
  replies: Reply[]
  canEdit: boolean
  canDelete: boolean
  canModerate: boolean
}

interface Reply {
  id: string
  content: string
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string
    image?: string
    role: 'STUDENT' | 'INSTRUCTOR'
  }
  parentReply?: {
    id: string
    content: string
    author: {
      id: string
      name: string
    }
  }
  childReplies: Reply[]
  likeCount: number
  childReplyCount: number
  isLiked: boolean
  canEdit: boolean
  canDelete: boolean
}

interface DiscussionViewProps {
  discussionId: string
  courseId: string
  userRole: 'STUDENT' | 'INSTRUCTOR'
  onBack: () => void
  className?: string
}

export function DiscussionView({ 
  discussionId, 
  courseId, 
  userRole, 
  onBack, 
  className = '' 
}: DiscussionViewProps) {
  const [discussion, setDiscussion] = useState<Discussion | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [replyContent, setReplyContent] = useState('')
  const [isSubmittingReply, setIsSubmittingReply] = useState(false)
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [showAllReplies, setShowAllReplies] = useState(false)

  // Fetch discussion details
  useEffect(() => {
    const fetchDiscussion = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/courses/${courseId}/discussions/${discussionId}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch discussion')
        }

        const data = await response.json()
        setDiscussion(data.discussion)
      } catch (error) {
        console.error('Error fetching discussion:', error)
        toast.error('Failed to load discussion')
      } finally {
        setIsLoading(false)
      }
    }

    if (discussionId && courseId) {
      fetchDiscussion()
    }
  }, [discussionId, courseId])

  // Handle like discussion
  const handleLikeDiscussion = async () => {
    if (!discussion) return

    try {
      const response = await fetch(`/api/courses/${courseId}/discussions/${discussionId}/like`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to toggle like')
      }

      const data = await response.json()
      setDiscussion(prev => prev ? {
        ...prev,
        isLiked: data.isLiked,
        likeCount: data.likeCount
      } : null)
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to toggle like')
    }
  }

  // Handle like reply
  const handleLikeReply = async (replyId: string) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/discussions/${discussionId}/replies/${replyId}/like`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to toggle like')
      }

      const data = await response.json()
      
      setDiscussion(prev => {
        if (!prev) return null
        
        const updateReplies = (replies: Reply[]): Reply[] => {
          return replies.map(reply => {
            if (reply.id === replyId) {
              return {
                ...reply,
                isLiked: data.isLiked,
                likeCount: data.likeCount
              }
            }
            if (reply.childReplies.length > 0) {
              return {
                ...reply,
                childReplies: updateReplies(reply.childReplies)
              }
            }
            return reply
          })
        }

        return {
          ...prev,
          replies: updateReplies(prev.replies)
        }
      })
    } catch (error) {
      console.error('Error toggling reply like:', error)
      toast.error('Failed to toggle like')
    }
  }

  // Submit reply
  const handleSubmitReply = async () => {
    if (!replyContent.trim() || isSubmittingReply) return

    try {
      setIsSubmittingReply(true)
      const response = await fetch(`/api/courses/${courseId}/discussions/${discussionId}/replies`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: replyContent,
          ...(replyingTo && { parentReplyId: replyingTo })
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit reply')
      }

      const data = await response.json()
      
      setDiscussion(prev => prev ? {
        ...prev,
        replies: [...prev.replies, data.reply],
        replyCount: prev.replyCount + 1
      } : null)

      setReplyContent('')
      setReplyingTo(null)
      toast.success('Reply posted successfully!')
    } catch (error) {
      console.error('Error submitting reply:', error)
      toast.error('Failed to post reply')
    } finally {
      setIsSubmittingReply(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading discussion...</p>
        </div>
      </div>
    )
  }

  if (!discussion) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Discussion Not Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          The discussion you're looking for doesn't exist or has been removed.
        </p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
        >
          Back to Discussions
        </button>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <button
          onClick={onBack}
          className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Discussions</span>
        </button>

        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors">
            <Share2 className="w-4 h-4" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors">
            <Flag className="w-4 h-4" />
          </button>
          {(discussion.canEdit || discussion.canModerate) && (
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors">
              <MoreHorizontal className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Discussion Post */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        {/* Discussion Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-2 flex-wrap">
            {discussion.isSticky && (
              <Pin className="w-4 h-4 text-violet-500" />
            )}
            {discussion.isLocked && (
              <Lock className="w-4 h-4 text-red-500" />
            )}
            
            <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(discussion.category)}`}>
              {getCategoryIcon(discussion.category)}
              <span>{discussion.category}</span>
            </span>

            {discussion.author.role === 'INSTRUCTOR' && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 rounded-full text-xs font-medium">
                Instructor
              </span>
            )}
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {discussion.title}
        </h1>

        {/* Author Info */}
        <div className="flex items-center space-x-3 mb-4">
          {discussion.author.image ? (
            <img
              src={discussion.author.image}
              alt={discussion.author.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
          )}
          
          <div>
            <div className="font-medium text-gray-900 dark:text-white">
              {discussion.author.name}
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <Clock className="w-3 h-3" />
              <span>{formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="prose dark:prose-invert max-w-none mb-6">
          <p className="text-gray-700 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
            {discussion.content}
          </p>
        </div>

        {/* Tags */}
        {discussion.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {discussion.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-sm"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}

        {/* Topic Link */}
        {discussion.topic && (
          <div className="mb-4">
            <span className="text-sm text-violet-600 dark:text-violet-400">
              Related to: {discussion.topic.section.title} → {discussion.topic.title}
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleLikeDiscussion}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                discussion.isLiked
                  ? 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                  : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-red-900/20 dark:hover:text-red-400'
              }`}
            >
              <Heart className={`w-4 h-4 ${discussion.isLiked ? 'fill-current' : ''}`} />
              <span>{discussion.likeCount}</span>
            </button>

            <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
              <Reply className="w-4 h-4" />
              <span>{discussion.replyCount} replies</span>
            </div>
          </div>

          {discussion.canEdit && (
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors">
                <Edit3 className="w-4 h-4" />
              </button>
              {discussion.canDelete && (
                <button className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors">
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Reply Form */}
      {!discussion.isLocked && (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
            {replyingTo ? 'Reply to Comment' : 'Add a Reply'}
          </h3>
          
          {replyingTo && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-violet-500">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Replying to a comment
                </span>
                <button
                  onClick={() => setReplyingTo(null)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                >
                  ×
                </button>
              </div>
            </div>
          )}

          <textarea
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="Write your reply..."
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
          />

          <div className="flex justify-end mt-4">
            <button
              onClick={handleSubmitReply}
              disabled={!replyContent.trim() || isSubmittingReply}
              className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              <Send className="w-4 h-4" />
              <span>{isSubmittingReply ? 'Posting...' : 'Post Reply'}</span>
            </button>
          </div>
        </div>
      )}

      {/* Replies */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Replies ({discussion.replyCount})
        </h3>

        {discussion.replies.length === 0 ? (
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-8 text-center">
            <Reply className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              No Replies Yet
            </h4>
            <p className="text-gray-600 dark:text-gray-400">
              Be the first to reply to this discussion
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {discussion.replies.map((reply) => (
              <ReplyCard
                key={reply.id}
                reply={reply}
                onLike={() => handleLikeReply(reply.id)}
                onReply={() => setReplyingTo(reply.id)}
                userRole={userRole}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Reply Card Component (placeholder)
interface ReplyCardProps {
  reply: Reply
  onLike: () => void
  onReply: () => void
  userRole: 'STUDENT' | 'INSTRUCTOR'
}

function ReplyCard({ reply, onLike, onReply, userRole }: ReplyCardProps) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-start space-x-3">
        {/* Author Avatar */}
        <div className="flex-shrink-0">
          {reply.author.image ? (
            <img
              src={reply.author.image}
              alt={reply.author.name}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
          )}
        </div>

        {/* Reply Content */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="font-medium text-gray-900 dark:text-white">
              {reply.author.name}
            </span>
            {reply.author.role === 'INSTRUCTOR' && (
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 rounded-full text-xs font-medium">
                Instructor
              </span>
            )}
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
            </span>
          </div>

          <p className="text-gray-700 dark:text-gray-300 mb-3 whitespace-pre-wrap">
            {reply.content}
          </p>

          <div className="flex items-center space-x-4">
            <button
              onClick={onLike}
              className={`flex items-center space-x-1 text-sm transition-colors ${
                reply.isLiked
                  ? 'text-red-600 dark:text-red-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400'
              }`}
            >
              <Heart className={`w-4 h-4 ${reply.isLiked ? 'fill-current' : ''}`} />
              <span>{reply.likeCount}</span>
            </button>

            <button
              onClick={onReply}
              className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
            >
              <Reply className="w-4 h-4" />
              <span>Reply</span>
            </button>

            {reply.canEdit && (
              <button className="text-sm text-gray-500 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors">
                Edit
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
