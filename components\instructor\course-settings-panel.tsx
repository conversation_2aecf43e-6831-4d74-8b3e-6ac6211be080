'use client'

import React, { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  Upload, 
  Image as ImageIcon, 
  DollarSign, 
  Globe, 
  Tag, 
  BookOpen,
  Settings,
  Eye,
  Users,
  Clock,
  Star
} from 'lucide-react'
import { toast } from 'sonner'

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  slug: string
  thumbnailImage?: string
  previewVideo?: string
  category?: string
  subcategory?: string
  level?: string
  language: string
  price: number
  originalPrice?: number
  currency: string
  tags: string[]
  keywords: string[]
  metaTitle?: string
  metaDescription?: string
  allowComments: boolean
  allowDownloads: boolean
  isFeatured: boolean
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' | 'PRIVATE'
}

interface CourseSettingsPanelProps {
  course: Course
  onCourseUpdate: (course: Partial<Course>) => void
}

export function CourseSettingsPanel({ course, onCourseUpdate }: CourseSettingsPanelProps) {
  const [activeSection, setActiveSection] = useState<string>('basic')
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Handle thumbnail upload
  const handleThumbnailUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB')
      return
    }

    try {
      setIsUploading(true)
      
      const formData = new FormData()
      formData.append('file', file)
      formData.append('courseId', course.id)

      const response = await fetch('/api/instructor/upload/document', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('Failed to upload thumbnail')
      }

      const data = await response.json()
      onCourseUpdate({ thumbnailImage: data.content.fileUrl })
      toast.success('Thumbnail uploaded successfully!')
    } catch (error) {
      console.error('Error uploading thumbnail:', error)
      toast.error('Failed to upload thumbnail')
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleThumbnailUpload(file)
    }
  }

  const sections = [
    {
      id: 'basic',
      title: 'Basic Information',
      icon: BookOpen,
      description: 'Course title, description, and basic details'
    },
    {
      id: 'media',
      title: 'Media & Branding',
      icon: ImageIcon,
      description: 'Thumbnail, preview video, and visual elements'
    },
    {
      id: 'pricing',
      title: 'Pricing & Access',
      icon: DollarSign,
      description: 'Course pricing, currency, and access settings'
    },
    {
      id: 'categorization',
      title: 'Categorization',
      icon: Tag,
      description: 'Categories, tags, and course classification'
    },
    {
      id: 'seo',
      title: 'SEO & Marketing',
      icon: Globe,
      description: 'Search optimization and marketing settings'
    },
    {
      id: 'advanced',
      title: 'Advanced Settings',
      icon: Settings,
      description: 'Comments, downloads, and other preferences'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Course Settings
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Configure your course details, pricing, and preferences
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
              Settings Sections
            </h3>
            <div className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon
                const isActive = activeSection === section.id

                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="w-4 h-4" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm">{section.title}</div>
                        <div className={`text-xs mt-1 ${
                          isActive 
                            ? 'text-white/80' 
                            : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {section.description}
                        </div>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            {/* Basic Information */}
            {activeSection === 'basic' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Basic Information
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Course Title *
                    </label>
                    <input
                      type="text"
                      value={course.title}
                      onChange={(e) => onCourseUpdate({ title: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Enter course title..."
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Short Description
                    </label>
                    <input
                      type="text"
                      value={course.shortDescription || ''}
                      onChange={(e) => onCourseUpdate({ shortDescription: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Brief description for course cards..."
                      maxLength={160}
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {(course.shortDescription || '').length}/160 characters
                    </p>
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Full Description
                    </label>
                    <textarea
                      value={course.description || ''}
                      onChange={(e) => onCourseUpdate({ description: e.target.value })}
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                      placeholder="Detailed course description..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Course Level
                    </label>
                    <select
                      value={course.level || ''}
                      onChange={(e) => onCourseUpdate({ level: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select level...</option>
                      <option value="Beginner">Beginner</option>
                      <option value="Intermediate">Intermediate</option>
                      <option value="Advanced">Advanced</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Language
                    </label>
                    <select
                      value={course.language}
                      onChange={(e) => onCourseUpdate({ language: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                      <option value="hi">Hindi</option>
                      <option value="zh">Chinese</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Media & Branding */}
            {activeSection === 'media' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Media & Branding
                  </h3>
                </div>

                {/* Thumbnail Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Course Thumbnail
                  </label>
                  <div className="flex items-start space-x-4">
                    {course.thumbnailImage ? (
                      <div className="w-32 h-20 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img
                          src={course.thumbnailImage}
                          alt="Course thumbnail"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-32 h-20 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                    
                    <div className="flex-1">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                        className="flex items-center space-x-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Upload className="w-4 h-4" />
                        <span>{isUploading ? 'Uploading...' : 'Upload Thumbnail'}</span>
                      </button>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                        Recommended: 1280x720px, JPG or PNG, max 5MB
                      </p>
                    </div>
                  </div>
                </div>

                {/* Preview Video */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Preview Video URL (Optional)
                  </label>
                  <input
                    type="url"
                    value={course.previewVideo || ''}
                    onChange={(e) => onCourseUpdate({ previewVideo: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="https://example.com/preview-video.mp4"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Add a preview video to showcase your course content
                  </p>
                </div>
              </motion.div>
            )}

            {/* Pricing & Access */}
            {activeSection === 'pricing' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Pricing & Access
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Price *
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={course.price}
                        onChange={(e) => onCourseUpdate({ price: parseFloat(e.target.value) || 0 })}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Original Price (Optional)
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={course.originalPrice || ''}
                        onChange={(e) => onCourseUpdate({ originalPrice: parseFloat(e.target.value) || undefined })}
                        className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Currency
                    </label>
                    <select
                      value={course.currency}
                      onChange={(e) => onCourseUpdate({ currency: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                      <option value="GBP">GBP (£)</option>
                      <option value="INR">INR (₹)</option>
                      <option value="JPY">JPY (¥)</option>
                    </select>
                  </div>
                </div>

                {/* Course Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Course Status
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {[
                      { value: 'DRAFT', label: 'Draft', icon: Eye, color: 'yellow' },
                      { value: 'PUBLISHED', label: 'Published', icon: Globe, color: 'green' },
                      { value: 'PRIVATE', label: 'Private', icon: Users, color: 'blue' },
                      { value: 'ARCHIVED', label: 'Archived', icon: Clock, color: 'gray' }
                    ].map((status) => {
                      const Icon = status.icon
                      const isSelected = course.status === status.value

                      return (
                        <button
                          key={status.value}
                          onClick={() => onCourseUpdate({ status: status.value as any })}
                          className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                            isSelected
                              ? `border-${status.color}-500 bg-${status.color}-50 dark:bg-${status.color}-900/20`
                              : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                          }`}
                        >
                          <Icon className={`w-5 h-5 mx-auto mb-2 ${
                            isSelected 
                              ? `text-${status.color}-600 dark:text-${status.color}-400`
                              : 'text-gray-400'
                          }`} />
                          <div className={`text-sm font-medium ${
                            isSelected 
                              ? `text-${status.color}-900 dark:text-${status.color}-100`
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {status.label}
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </div>

                {/* Featured Course Toggle */}
                <div className="flex items-center justify-between p-4 bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg border border-violet-200 dark:border-violet-800">
                  <div className="flex items-center space-x-3">
                    <Star className="w-5 h-5 text-violet-600 dark:text-violet-400" />
                    <div>
                      <div className="font-medium text-violet-900 dark:text-violet-100">
                        Featured Course
                      </div>
                      <div className="text-sm text-violet-700 dark:text-violet-300">
                        Highlight this course on your homepage
                      </div>
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={course.isFeatured}
                      onChange={(e) => onCourseUpdate({ isFeatured: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
                  </label>
                </div>
              </motion.div>
            )}

            {/* Add other sections here... */}
            {activeSection === 'categorization' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Categorization
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Help students find your course by categorizing it properly
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Primary Category
                    </label>
                    <select
                      value={course.category || ''}
                      onChange={(e) => onCourseUpdate({ category: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">Select category...</option>
                      <option value="Programming">Programming</option>
                      <option value="Design">Design</option>
                      <option value="Business">Business</option>
                      <option value="Marketing">Marketing</option>
                      <option value="Photography">Photography</option>
                      <option value="Music">Music</option>
                      <option value="Health">Health & Fitness</option>
                      <option value="Language">Language</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Subcategory (Optional)
                    </label>
                    <input
                      type="text"
                      value={course.subcategory || ''}
                      onChange={(e) => onCourseUpdate({ subcategory: e.target.value })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="e.g., Web Development, React, etc."
                    />
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Course Tags
                  </label>
                  <input
                    type="text"
                    value={course.tags.join(', ')}
                    onChange={(e) => onCourseUpdate({ 
                      tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)
                    })}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter tags separated by commas..."
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Add relevant tags to help students discover your course
                  </p>
                </div>
              </motion.div>
            )}

            {/* Placeholder for other sections */}
            {(activeSection === 'seo' || activeSection === 'advanced') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center py-12"
              >
                <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {activeSection === 'seo' ? 'SEO & Marketing' : 'Advanced Settings'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  This section is coming soon...
                </p>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
