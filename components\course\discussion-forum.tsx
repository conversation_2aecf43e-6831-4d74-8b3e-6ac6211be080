'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  MessageSquare, 
  Plus, 
  Search, 
  Filter, 
  Pin, 
  Lock, 
  Heart, 
  Reply, 
  MoreHorizontal,
  Clock,
  User,
  Tag,
  TrendingUp,
  MessageCircle,
  Eye,
  ChevronDown,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

interface Discussion {
  id: string
  title: string
  content: string
  category: 'GENERAL' | 'QUESTION' | 'ANNOUNCEMENT' | 'ASSIGNMENT' | 'TECHNICAL'
  isSticky: boolean
  isPinned: boolean
  isLocked: boolean
  tags: string[]
  createdAt: string
  updatedAt: string
  author: {
    id: string
    name: string
    image?: string
    role: 'STUDENT' | 'INSTRUCTOR'
  }
  topic?: {
    id: string
    title: string
    section: {
      id: string
      title: string
    }
  }
  replyCount: number
  likeCount: number
  isLiked: boolean
  recentReplies: any[]
}

interface DiscussionForumProps {
  courseId: string
  userRole: 'STUDENT' | 'INSTRUCTOR'
  className?: string
}

type SortBy = 'recent' | 'popular' | 'unanswered'
type FilterCategory = 'ALL' | 'GENERAL' | 'QUESTION' | 'ANNOUNCEMENT' | 'ASSIGNMENT' | 'TECHNICAL'

export function DiscussionForum({ courseId, userRole, className = '' }: DiscussionForumProps) {
  const [discussions, setDiscussions] = useState<Discussion[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<SortBy>('recent')
  const [filterCategory, setFilterCategory] = useState<FilterCategory>('ALL')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // Fetch discussions
  useEffect(() => {
    const fetchDiscussions = async () => {
      try {
        setIsLoading(true)
        const params = new URLSearchParams({
          page: page.toString(),
          limit: '20',
          sortBy,
          ...(filterCategory !== 'ALL' && { category: filterCategory }),
          ...(searchQuery && { search: searchQuery })
        })

        const response = await fetch(`/api/courses/${courseId}/discussions?${params}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch discussions')
        }

        const data = await response.json()
        
        if (page === 1) {
          setDiscussions(data.discussions)
        } else {
          setDiscussions(prev => [...prev, ...data.discussions])
        }
        
        setHasMore(data.pagination.hasNext)
      } catch (error) {
        console.error('Error fetching discussions:', error)
        toast.error('Failed to load discussions')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDiscussions()
  }, [courseId, page, sortBy, filterCategory, searchQuery])

  // Reset page when filters change
  useEffect(() => {
    setPage(1)
  }, [sortBy, filterCategory, searchQuery])

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'QUESTION':
        return <AlertCircle className="w-4 h-4 text-blue-500" />
      case 'ANNOUNCEMENT':
        return <MessageCircle className="w-4 h-4 text-purple-500" />
      case 'ASSIGNMENT':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'TECHNICAL':
        return <Tag className="w-4 h-4 text-red-500" />
      default:
        return <MessageSquare className="w-4 h-4 text-gray-500" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'QUESTION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
      case 'ANNOUNCEMENT':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
      case 'ASSIGNMENT':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
      case 'TECHNICAL':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }

  // Export utility functions for use in other components
  export { getCategoryIcon, getCategoryColor }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Course Discussions
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Connect with your classmates and instructor
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
        >
          <Plus className="w-4 h-4" />
          <span>New Discussion</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search discussions..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value as FilterCategory)}
              className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500 focus:border-transparent"
            >
              <option value="ALL">All Categories</option>
              <option value="GENERAL">General</option>
              <option value="QUESTION">Questions</option>
              <option value="ANNOUNCEMENT">Announcements</option>
              <option value="ASSIGNMENT">Assignments</option>
              <option value="TECHNICAL">Technical</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>

          {/* Sort */}
          <div className="relative">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as SortBy)}
              className="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-gray-900 dark:text-white focus:ring-2 focus:ring-violet-500 focus:border-transparent"
            >
              <option value="recent">Most Recent</option>
              <option value="popular">Most Popular</option>
              <option value="unanswered">Unanswered</option>
            </select>
            <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
          </div>
        </div>
      </div>

      {/* Discussions List */}
      <div className="space-y-4">
        {isLoading && page === 1 ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">Loading discussions...</p>
            </div>
          </div>
        ) : discussions.length === 0 ? (
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
            <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Discussions Yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Be the first to start a discussion in this course
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
            >
              Start First Discussion
            </button>
          </div>
        ) : (
          <AnimatePresence>
            {discussions.map((discussion, index) => (
              <motion.div
                key={discussion.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.05 }}
              >
                <DiscussionCard
                  discussion={discussion}
                  userRole={userRole}
                  onLike={(discussionId, isLiked) => {
                    setDiscussions(prev => prev.map(d => 
                      d.id === discussionId 
                        ? { 
                            ...d, 
                            isLiked, 
                            likeCount: d.likeCount + (isLiked ? 1 : -1) 
                          }
                        : d
                    ))
                  }}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        )}

        {/* Load More */}
        {hasMore && !isLoading && (
          <div className="text-center">
            <button
              onClick={() => setPage(prev => prev + 1)}
              className="px-6 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Load More Discussions
            </button>
          </div>
        )}
      </div>

      {/* Create Discussion Modal */}
      <AnimatePresence>
        {showCreateModal && (
          <CreateDiscussionModal
            courseId={courseId}
            userRole={userRole}
            onClose={() => setShowCreateModal(false)}
            onCreated={(newDiscussion) => {
              setDiscussions(prev => [newDiscussion, ...prev])
              setShowCreateModal(false)
              toast.success('Discussion created successfully!')
            }}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Discussion Card Component
interface DiscussionCardProps {
  discussion: Discussion
  userRole: 'STUDENT' | 'INSTRUCTOR'
  onLike: (discussionId: string, isLiked: boolean) => void
}

function DiscussionCard({ discussion, userRole, onLike }: DiscussionCardProps) {
  const [isLiking, setIsLiking] = useState(false)

  const handleLike = async () => {
    if (isLiking) return

    try {
      setIsLiking(true)
      const response = await fetch(`/api/courses/${discussion.id.split('-')[0]}/discussions/${discussion.id}/like`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to toggle like')
      }

      const data = await response.json()
      onLike(discussion.id, data.isLiked)
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to toggle like')
    } finally {
      setIsLiking(false)
    }
  }

  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200">
      <div className="flex items-start space-x-4">
        {/* Author Avatar */}
        <div className="flex-shrink-0">
          {discussion.author.image ? (
            <img
              src={discussion.author.image}
              alt={discussion.author.name}
              className="w-10 h-10 rounded-full object-cover"
            />
          ) : (
            <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        {/* Discussion Content */}
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-2 flex-wrap">
              {discussion.isSticky && (
                <Pin className="w-4 h-4 text-violet-500" />
              )}
              {discussion.isLocked && (
                <Lock className="w-4 h-4 text-red-500" />
              )}
              
              <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(discussion.category)}`}>
                {getCategoryIcon(discussion.category)}
                <span>{discussion.category}</span>
              </span>

              {discussion.author.role === 'INSTRUCTOR' && (
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300 rounded-full text-xs font-medium">
                  Instructor
                </span>
              )}
            </div>

            <button className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors">
              <MoreHorizontal className="w-4 h-4" />
            </button>
          </div>

          {/* Title */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 hover:text-violet-600 dark:hover:text-violet-400 cursor-pointer transition-colors">
            {discussion.title}
          </h3>

          {/* Content Preview */}
          <p className="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            {discussion.content}
          </p>

          {/* Tags */}
          {discussion.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-3">
              {discussion.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded text-xs"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}

          {/* Topic Link */}
          {discussion.topic && (
            <div className="mb-3">
              <span className="text-sm text-violet-600 dark:text-violet-400">
                Related to: {discussion.topic.section.title} → {discussion.topic.title}
              </span>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <User className="w-4 h-4" />
                <span>{discussion.author.name}</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{formatDistanceToNow(new Date(discussion.createdAt), { addSuffix: true })}</span>
              </div>

              <div className="flex items-center space-x-1">
                <Reply className="w-4 h-4" />
                <span>{discussion.replyCount} replies</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={handleLike}
                disabled={isLiking}
                className={`flex items-center space-x-1 px-3 py-1 rounded-lg transition-colors ${
                  discussion.isLiked
                    ? 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                    : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-red-900/20 dark:hover:text-red-400'
                }`}
              >
                <Heart className={`w-4 h-4 ${discussion.isLiked ? 'fill-current' : ''}`} />
                <span>{discussion.likeCount}</span>
              </button>

              <button className="flex items-center space-x-1 px-3 py-1 bg-violet-100 text-violet-600 hover:bg-violet-200 dark:bg-violet-900/20 dark:text-violet-400 dark:hover:bg-violet-900/30 rounded-lg transition-colors">
                <Reply className="w-4 h-4" />
                <span>Reply</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Create Discussion Modal (placeholder)
function CreateDiscussionModal({ courseId, userRole, onClose, onCreated }: any) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          Create New Discussion
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Discussion creation form will be implemented here...
        </p>
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Cancel
          </button>
          <button className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors">
            Create Discussion
          </button>
        </div>
      </motion.div>
    </motion.div>
  )
}
