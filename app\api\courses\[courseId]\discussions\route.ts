import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createDiscussionSchema = z.object({
  title: z.string().min(1, 'Discussion title is required'),
  content: z.string().min(1, 'Discussion content is required'),
  category: z.enum(['GENERAL', 'QUESTION', 'ANNOUNCEMENT', 'ASSIGNMENT', 'TECHNICAL']).default('GENERAL'),
  topicId: z.string().optional(), // Link to specific course topic
  isSticky: z.boolean().default(false), // Only instructors can set this
  tags: z.array(z.string()).default([])
})

const querySchema = commonSchemas.pagination.extend({
  category: z.enum(['GENERAL', 'QUESTION', 'ANNOUNCEMENT', 'ASSIGNMENT', 'TECHNICAL']).optional(),
  topicId: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['recent', 'popular', 'unanswered']).default('recent'),
  isSticky: z.enum(['true', 'false']).optional()
})

// GET /api/courses/[courseId]/discussions - Get course discussions
export const GET = createAPIHandler(
  {
    requireAuth: true,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]
      const { 
        page = 1, 
        limit = 20, 
        category, 
        topicId, 
        search, 
        sortBy = 'recent',
        isSticky 
      } = validatedQuery

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true, status: true, isPublished: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied to course discussions', 403)
      }

      // Build where clause
      const where: any = {
        courseId,
        isDeleted: false
      }

      if (category) {
        where.category = category
      }

      if (topicId) {
        where.topicId = topicId
      }

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { content: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (isSticky !== undefined) {
        where.isSticky = isSticky === 'true'
      }

      // Build order by clause
      let orderBy: any = { createdAt: 'desc' }
      
      switch (sortBy) {
        case 'popular':
          orderBy = [
            { replies: { _count: 'desc' } },
            { createdAt: 'desc' }
          ]
          break
        case 'unanswered':
          orderBy = [
            { replies: { _count: 'asc' } },
            { createdAt: 'desc' }
          ]
          break
        default: // recent
          orderBy = [
            { isSticky: 'desc' }, // Sticky posts first
            { updatedAt: 'desc' }
          ]
      }

      // Get total count
      const total = await prisma.discussionPost.count({ where })

      // Get discussions
      const discussions = await prisma.discussionPost.findMany({
        where,
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          topic: {
            select: {
              id: true,
              title: true,
              section: {
                select: {
                  id: true,
                  title: true
                }
              }
            }
          },
          replies: {
            take: 3,
            orderBy: { createdAt: 'desc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  role: true
                }
              }
            }
          },
          _count: {
            select: {
              replies: true,
              likes: true
            }
          }
        }
      })

      // Format discussions
      const formattedDiscussions = discussions.map(discussion => ({
        id: discussion.id,
        title: discussion.title,
        content: discussion.content,
        category: discussion.category,
        isSticky: discussion.isSticky,
        isPinned: discussion.isPinned,
        isLocked: discussion.isLocked,
        tags: discussion.tags,
        createdAt: discussion.createdAt,
        updatedAt: discussion.updatedAt,
        author: discussion.author,
        topic: discussion.topic,
        replyCount: discussion._count.replies,
        likeCount: discussion._count.likes,
        recentReplies: discussion.replies,
        // Check if current user has liked this post
        isLiked: false // TODO: Implement user likes check
      }))

      return APIResponse.success({
        discussions: formattedDiscussions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching discussions:', error)
      return APIResponse.error(
        'Failed to fetch discussions: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/courses/[courseId]/discussions - Create new discussion
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: createDiscussionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true, status: true, isPublished: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied to course discussions', 403)
      }

      const discussionData = validatedBody

      // Only instructors can create sticky posts or announcements
      if (!isInstructor) {
        discussionData.isSticky = false
        if (discussionData.category === 'ANNOUNCEMENT') {
          discussionData.category = 'GENERAL'
        }
      }

      // Verify topic exists if provided
      if (discussionData.topicId) {
        const topic = await prisma.courseTopic.findFirst({
          where: {
            id: discussionData.topicId,
            section: {
              courseId
            }
          }
        })

        if (!topic) {
          return APIResponse.error('Invalid topic ID', 400)
        }
      }

      // Create discussion
      const discussion = await prisma.discussionPost.create({
        data: {
          ...discussionData,
          courseId,
          authorId: user.id
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          topic: {
            select: {
              id: true,
              title: true,
              section: {
                select: {
                  id: true,
                  title: true
                }
              }
            }
          },
          _count: {
            select: {
              replies: true,
              likes: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Discussion created successfully',
        discussion: {
          ...discussion,
          replyCount: discussion._count.replies,
          likeCount: discussion._count.likes,
          isLiked: false
        }
      })

    } catch (error) {
      console.error('Error creating discussion:', error)
      return APIResponse.error(
        'Failed to create discussion: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
