import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createQuestionSchema = z.object({
  type: z.enum(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'SHORT_ANSWER', 'ESSAY']),
  question: z.string().min(1, 'Question text is required'),
  options: z.array(z.string()).optional(), // For MCQ and T/F
  correctAnswer: z.string().min(1, 'Correct answer is required'),
  explanation: z.string().optional(),
  points: z.number().int().min(1).default(1),
  order: z.number().int().min(0).optional()
})

const updateQuestionSchema = createQuestionSchema.partial()

const reorderQuestionsSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    order: z.number().int().min(0)
  }))
})

// GET /api/instructor/courses/[courseId]/quizzes/[quizId]/questions - Get quiz questions
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership and quiz existence
      const quiz = await prisma.courseQuiz.findFirst({
        where: { 
          id: quizId,
          courseId,
          course: {
            instructorId: user.id
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      // Get questions
      const questions = await prisma.courseQuizQuestion.findMany({
        where: { quizId },
        orderBy: { order: 'asc' }
      })

      return APIResponse.success({
        questions
      })

    } catch (error) {
      console.error('Error fetching quiz questions:', error)
      return APIResponse.error(
        'Failed to fetch questions: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/instructor/courses/[courseId]/quizzes/[quizId]/questions - Create question
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: createQuestionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership and quiz existence
      const quiz = await prisma.courseQuiz.findFirst({
        where: { 
          id: quizId,
          courseId,
          course: {
            instructorId: user.id
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      const questionData = validatedBody

      // Validate question data based on type
      if (questionData.type === 'MULTIPLE_CHOICE') {
        if (!questionData.options || questionData.options.length < 2) {
          return APIResponse.error('Multiple choice questions must have at least 2 options', 400)
        }
        if (!questionData.options.includes(questionData.correctAnswer)) {
          return APIResponse.error('Correct answer must be one of the provided options', 400)
        }
      } else if (questionData.type === 'TRUE_FALSE') {
        if (!['True', 'False'].includes(questionData.correctAnswer)) {
          return APIResponse.error('True/False questions must have "True" or "False" as correct answer', 400)
        }
        questionData.options = ['True', 'False']
      }

      // If no order specified, add to end
      if (questionData.order === undefined) {
        const lastQuestion = await prisma.courseQuizQuestion.findFirst({
          where: { quizId },
          orderBy: { order: 'desc' }
        })
        questionData.order = (lastQuestion?.order || 0) + 1
      }

      // Create question
      const question = await prisma.courseQuizQuestion.create({
        data: {
          ...questionData,
          quizId
        }
      })

      return APIResponse.success({
        message: 'Question created successfully',
        question
      })

    } catch (error) {
      console.error('Error creating question:', error)
      return APIResponse.error(
        'Failed to create question: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId]/quizzes/[quizId]/questions - Reorder questions
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: reorderQuestionsSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership and quiz existence
      const quiz = await prisma.courseQuiz.findFirst({
        where: { 
          id: quizId,
          courseId,
          course: {
            instructorId: user.id
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      const { questions } = validatedBody

      // Update question orders in a transaction
      await prisma.$transaction(
        questions.map(question =>
          prisma.courseQuizQuestion.update({
            where: { 
              id: question.id,
              quizId // Ensure question belongs to this quiz
            },
            data: { order: question.order }
          })
        )
      )

      return APIResponse.success({
        message: 'Questions reordered successfully'
      })

    } catch (error) {
      console.error('Error reordering questions:', error)
      return APIResponse.error(
        'Failed to reorder questions: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// Individual question management
// PUT /api/instructor/courses/[courseId]/quizzes/[quizId]/questions/[questionId]
export async function PUT_QUESTION(request: NextRequest, questionId: string, quizId: string, courseId: string, user: any) {
  try {
    const body = await request.json()
    const validatedBody = updateQuestionSchema.parse(body)

    // Verify course ownership and quiz existence
    const quiz = await prisma.courseQuiz.findFirst({
      where: { 
        id: quizId,
        courseId,
        course: {
          instructorId: user.id
        }
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Check if question exists
    const existingQuestion = await prisma.courseQuizQuestion.findUnique({
      where: { 
        id: questionId,
        quizId
      }
    })

    if (!existingQuestion) {
      return APIResponse.error('Question not found', 404)
    }

    // Validate question data based on type if type is being updated
    if (validatedBody.type || validatedBody.options || validatedBody.correctAnswer) {
      const questionType = validatedBody.type || existingQuestion.type
      const options = validatedBody.options || existingQuestion.options
      const correctAnswer = validatedBody.correctAnswer || existingQuestion.correctAnswer

      if (questionType === 'MULTIPLE_CHOICE') {
        if (!options || (options as string[]).length < 2) {
          return APIResponse.error('Multiple choice questions must have at least 2 options', 400)
        }
        if (!(options as string[]).includes(correctAnswer)) {
          return APIResponse.error('Correct answer must be one of the provided options', 400)
        }
      } else if (questionType === 'TRUE_FALSE') {
        if (!['True', 'False'].includes(correctAnswer)) {
          return APIResponse.error('True/False questions must have "True" or "False" as correct answer', 400)
        }
        validatedBody.options = ['True', 'False']
      }
    }

    // Update question
    const updatedQuestion = await prisma.courseQuizQuestion.update({
      where: { id: questionId },
      data: validatedBody
    })

    return APIResponse.success({
      message: 'Question updated successfully',
      question: updatedQuestion
    })

  } catch (error) {
    console.error('Error updating question:', error)
    return APIResponse.error(
      'Failed to update question: ' + (error instanceof Error ? error.message : 'Unknown error'),
      500
    )
  }
}

// DELETE /api/instructor/courses/[courseId]/quizzes/[quizId]/questions/[questionId]
export async function DELETE_QUESTION(questionId: string, quizId: string, courseId: string, user: any) {
  try {
    // Verify course ownership and quiz existence
    const quiz = await prisma.courseQuiz.findFirst({
      where: { 
        id: quizId,
        courseId,
        course: {
          instructorId: user.id
        }
      }
    })

    if (!quiz) {
      return APIResponse.error('Quiz not found', 404)
    }

    // Check if question exists
    const question = await prisma.courseQuizQuestion.findUnique({
      where: { 
        id: questionId,
        quizId
      }
    })

    if (!question) {
      return APIResponse.error('Question not found', 404)
    }

    // Delete question
    await prisma.courseQuizQuestion.delete({
      where: { id: questionId }
    })

    // Reorder remaining questions
    const remainingQuestions = await prisma.courseQuizQuestion.findMany({
      where: { quizId },
      orderBy: { order: 'asc' }
    })

    await prisma.$transaction(
      remainingQuestions.map((question, index) =>
        prisma.courseQuizQuestion.update({
          where: { id: question.id },
          data: { order: index + 1 }
        })
      )
    )

    return APIResponse.success({
      message: 'Question deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting question:', error)
    return APIResponse.error(
      'Failed to delete question: ' + (error instanceof Error ? error.message : 'Unknown error'),
      500
    )
  }
}
