#!/usr/bin/env node

/**
 * Test script to verify Bunny CDN connectivity and configuration
 */

const https = require('https')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config()

// Bunny CDN configuration
const BUNNY_API_KEY = process.env.BUNNY_API_KEY
const BUNNY_STORAGE_ZONE_NAME = process.env.BUNNY_STORAGE_ZONE_NAME
const BUNNY_STORAGE_PASSWORD = process.env.BUNNY_STORAGE_PASSWORD
const BUNNY_PULL_ZONE_URL = process.env.BUNNY_PULL_ZONE_URL

function testBunnyCDN() {
  console.log('🧪 Testing Bunny CDN Connectivity...\n')
  
  // Test 1: Check environment variables
  console.log('📋 Checking environment variables:')
  console.log(`✅ BUNNY_API_KEY: ${BUNNY_API_KEY ? 'Set (' + BUNNY_API_KEY.substring(0, 8) + '...)' : '❌ Not set'}`)
  console.log(`✅ BUNNY_STORAGE_ZONE_NAME: ${BUNNY_STORAGE_ZONE_NAME || '❌ Not set'}`)
  console.log(`✅ BUNNY_STORAGE_PASSWORD: ${BUNNY_STORAGE_PASSWORD ? 'Set (' + BUNNY_STORAGE_PASSWORD.substring(0, 8) + '...)' : '❌ Not set'}`)
  console.log(`✅ BUNNY_PULL_ZONE_URL: ${BUNNY_PULL_ZONE_URL || '❌ Not set'}`)
  console.log('')

  if (!BUNNY_API_KEY || !BUNNY_STORAGE_ZONE_NAME || !BUNNY_STORAGE_PASSWORD || !BUNNY_PULL_ZONE_URL) {
    console.log('❌ Missing required environment variables. Please check your .env file.')
    return
  }

  // Test 2: Test API connectivity
  testApiConnectivity()
}

function testApiConnectivity() {
  console.log('🌐 Testing Bunny CDN API connectivity...')
  
  const options = {
    hostname: 'api.bunny.net',
    port: 443,
    path: '/storagezone',
    method: 'GET',
    headers: {
      'AccessKey': BUNNY_API_KEY,
      'Content-Type': 'application/json'
    }
  }

  const req = https.request(options, (res) => {
    console.log(`📡 API Response Status: ${res.statusCode}`)
    
    let data = ''
    res.on('data', (chunk) => {
      data += chunk
    })

    res.on('end', () => {
      try {
        if (res.statusCode === 200) {
          const storageZones = JSON.parse(data)
          console.log('✅ API connectivity successful!')
          console.log(`📦 Found ${storageZones.length} storage zones`)
          
          // Check if our storage zone exists
          const ourZone = storageZones.find(zone => zone.Name === BUNNY_STORAGE_ZONE_NAME)
          if (ourZone) {
            console.log(`✅ Storage zone '${BUNNY_STORAGE_ZONE_NAME}' found`)
            console.log(`📊 Zone details:`)
            console.log(`   - ID: ${ourZone.Id}`)
            console.log(`   - Region: ${ourZone.Region}`)
            console.log(`   - Files: ${ourZone.FilesStored || 0}`)
            console.log(`   - Storage Used: ${formatBytes(ourZone.StorageUsed || 0)}`)
            
            // Test storage connectivity
            testStorageConnectivity()
          } else {
            console.log(`❌ Storage zone '${BUNNY_STORAGE_ZONE_NAME}' not found`)
            console.log('Available zones:', storageZones.map(z => z.Name).join(', '))
          }
        } else {
          console.log('❌ API connectivity failed')
          console.log('Response:', data)
        }
      } catch (error) {
        console.log('❌ Error parsing API response:', error.message)
        console.log('Raw response:', data)
      }
    })
  })

  req.on('error', (error) => {
    console.log('❌ API request failed:', error.message)
  })

  req.end()
}

function testStorageConnectivity() {
  console.log('\n💾 Testing storage connectivity...')
  
  // Create a test file
  const testContent = `Bunny CDN Test File - ${new Date().toISOString()}`
  const testFileName = `test-${Date.now()}.txt`
  const testPath = `test/${testFileName}`
  
  const options = {
    hostname: 'storage.bunnycdn.com',
    port: 443,
    path: `/${BUNNY_STORAGE_ZONE_NAME}/${testPath}`,
    method: 'PUT',
    headers: {
      'AccessKey': BUNNY_STORAGE_PASSWORD,
      'Content-Type': 'text/plain',
      'Content-Length': Buffer.byteLength(testContent)
    }
  }

  console.log(`📤 Uploading test file: ${testPath}`)

  const req = https.request(options, (res) => {
    console.log(`📡 Storage Response Status: ${res.statusCode}`)
    
    if (res.statusCode === 201) {
      console.log('✅ File upload successful!')
      
      // Test file retrieval
      setTimeout(() => {
        testFileRetrieval(testPath, testFileName)
      }, 2000) // Wait 2 seconds for CDN propagation
      
    } else {
      console.log('❌ File upload failed')
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => console.log('Response:', data))
    }
  })

  req.on('error', (error) => {
    console.log('❌ Storage request failed:', error.message)
  })

  req.write(testContent)
  req.end()
}

function testFileRetrieval(testPath, testFileName) {
  console.log('\n📥 Testing file retrieval from CDN...')
  
  const fileUrl = `${BUNNY_PULL_ZONE_URL}/${testPath}`
  console.log(`🔗 Testing URL: ${fileUrl}`)

  https.get(fileUrl, (res) => {
    console.log(`📡 CDN Response Status: ${res.statusCode}`)
    
    if (res.statusCode === 200) {
      console.log('✅ File retrieval successful!')
      console.log('🎉 Bunny CDN is properly configured and working!')
      
      let data = ''
      res.on('data', (chunk) => data += chunk)
      res.on('end', () => {
        console.log(`📄 Retrieved content: ${data.substring(0, 50)}...`)
        
        // Clean up test file
        cleanupTestFile(testPath)
      })
    } else {
      console.log('❌ File retrieval failed')
      console.log('This might be due to CDN propagation delay. Try again in a few minutes.')
    }
  }).on('error', (error) => {
    console.log('❌ CDN request failed:', error.message)
  })
}

function cleanupTestFile(testPath) {
  console.log('\n🧹 Cleaning up test file...')
  
  const options = {
    hostname: 'storage.bunnycdn.com',
    port: 443,
    path: `/${BUNNY_STORAGE_ZONE_NAME}/${testPath}`,
    method: 'DELETE',
    headers: {
      'AccessKey': BUNNY_STORAGE_PASSWORD
    }
  }

  const req = https.request(options, (res) => {
    if (res.statusCode === 200) {
      console.log('✅ Test file cleaned up successfully')
    } else {
      console.log('⚠️ Could not clean up test file (this is not critical)')
    }
    
    console.log('\n🎯 Test Summary:')
    console.log('- Environment variables: ✅ Configured')
    console.log('- API connectivity: ✅ Working')
    console.log('- Storage upload: ✅ Working')
    console.log('- CDN delivery: ✅ Working')
    console.log('\n🚀 Your Bunny CDN integration is ready!')
  })

  req.on('error', (error) => {
    console.log('⚠️ Could not clean up test file:', error.message)
  })

  req.end()
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Run the test
if (require.main === module) {
  testBunnyCDN()
}

module.exports = { testBunnyCDN }
