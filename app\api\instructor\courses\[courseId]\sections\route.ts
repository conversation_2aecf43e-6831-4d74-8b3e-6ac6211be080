import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createSectionSchema = z.object({
  title: z.string().min(1, 'Section title is required'),
  description: z.string().optional(),
  order: z.number().int().min(0).optional(),
  isPublished: z.boolean().default(false),
  isFree: z.boolean().default(false)
})

const updateSectionSchema = createSectionSchema.partial()

const reorderSectionsSchema = z.object({
  sections: z.array(z.object({
    id: z.string(),
    order: z.number().int().min(0)
  }))
})

// GET /api/instructor/courses/[courseId]/sections - Get course sections
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get sections with topics
      const sections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { order: 'asc' },
        include: {
          topics: {
            orderBy: { order: 'asc' },
            include: {
              content: {
                orderBy: { order: 'asc' },
                select: {
                  id: true,
                  type: true,
                  title: true,
                  duration: true,
                  order: true
                }
              }
            }
          }
        }
      })

      // Calculate section metrics
      const sectionsWithMetrics = sections.map(section => {
        const totalTopics = section.topics.length
        const totalDuration = section.topics.reduce(
          (sum, topic) => sum + topic.content.reduce(
            (contentSum, content) => contentSum + (content.duration || 0),
            0
          ),
          0
        )
        const totalContent = section.topics.reduce(
          (sum, topic) => sum + topic.content.length,
          0
        )

        return {
          ...section,
          totalTopics,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          totalContent
        }
      })

      return APIResponse.success({
        sections: sectionsWithMetrics
      })

    } catch (error) {
      console.error('Error fetching course sections:', error)
      return APIResponse.error(
        'Failed to fetch sections: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/instructor/courses/[courseId]/sections - Create new section
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: createSectionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const sectionData = validatedBody

      // If no order specified, add to end
      if (sectionData.order === undefined) {
        const lastSection = await prisma.courseSection.findFirst({
          where: { courseId },
          orderBy: { order: 'desc' }
        })
        sectionData.order = (lastSection?.order || 0) + 1
      }

      // Create section
      const section = await prisma.courseSection.create({
        data: {
          ...sectionData,
          courseId
        },
        include: {
          topics: {
            orderBy: { order: 'asc' }
          }
        }
      })

      return APIResponse.success({
        message: 'Section created successfully',
        section
      })

    } catch (error) {
      console.error('Error creating section:', error)
      return APIResponse.error(
        'Failed to create section: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId]/sections - Reorder sections
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: reorderSectionsSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const { sections } = validatedBody

      // Update section orders in a transaction
      await prisma.$transaction(
        sections.map(section =>
          prisma.courseSection.update({
            where: { 
              id: section.id,
              courseId // Ensure section belongs to this course
            },
            data: { order: section.order }
          })
        )
      )

      return APIResponse.success({
        message: 'Sections reordered successfully'
      })

    } catch (error) {
      console.error('Error reordering sections:', error)
      return APIResponse.error(
        'Failed to reorder sections: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
