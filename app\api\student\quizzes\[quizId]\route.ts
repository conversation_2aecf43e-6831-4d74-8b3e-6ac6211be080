import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const startQuizSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

const submitQuizSchema = z.object({
  attemptId: z.string().min(1, 'Attempt ID is required'),
  answers: z.record(z.string(), z.string()), // questionId -> answer
  timeSpent: z.number().int().min(0).default(0) // seconds
})

// GET /api/student/quizzes/[quizId] - Get quiz for student (start screen)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const quizId = request.url.split('/').pop()

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Get quiz with course info
      const quiz = await prisma.courseQuiz.findUnique({
        where: { id: quizId },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructorId: true,
              status: true,
              isPublished: true
            }
          },
          questions: {
            select: {
              id: true,
              type: true,
              points: true
            }
          },
          _count: {
            select: {
              questions: true
            }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      if (!quiz.isPublished) {
        return APIResponse.error('Quiz is not available', 403)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: quiz.course.id
          }
        }
      })

      if (!enrollment || enrollment.status !== 'ACTIVE') {
        return APIResponse.error('You must be enrolled in the course to take this quiz', 403)
      }

      // Get user's previous attempts
      const attempts = await prisma.courseQuizAttempt.findMany({
        where: {
          userId: user.id,
          quizId: quizId
        },
        orderBy: { startedAt: 'desc' },
        select: {
          id: true,
          score: true,
          totalQuestions: true,
          correctAnswers: true,
          timeSpent: true,
          isPassed: true,
          startedAt: true,
          completedAt: true
        }
      })

      // Check if user has exceeded max attempts
      const completedAttempts = attempts.filter(a => a.completedAt !== null)
      const canTakeQuiz = completedAttempts.length < quiz.maxAttempts

      // Calculate quiz statistics
      const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0)
      const bestAttempt = attempts.length > 0 
        ? attempts.reduce((best, current) => 
            current.score > best.score ? current : best
          )
        : null

      return APIResponse.success({
        quiz: {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          instructions: quiz.instructions,
          timeLimit: quiz.timeLimit,
          passingScore: quiz.passingScore,
          maxAttempts: quiz.maxAttempts,
          totalQuestions: quiz._count.questions,
          totalPoints,
          course: {
            id: quiz.course.id,
            title: quiz.course.title
          }
        },
        userProgress: {
          attemptsUsed: completedAttempts.length,
          attemptsRemaining: quiz.maxAttempts - completedAttempts.length,
          canTakeQuiz,
          bestScore: bestAttempt?.score || 0,
          hasPassed: bestAttempt?.isPassed || false,
          attempts: attempts
        }
      })

    } catch (error) {
      console.error('Error fetching quiz:', error)
      return APIResponse.error(
        'Failed to fetch quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/student/quizzes/[quizId] - Start quiz attempt
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: startQuizSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const quizId = request.url.split('/').pop()
      const { courseId } = validatedBody

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Verify quiz exists and is published
      const quiz = await prisma.courseQuiz.findUnique({
        where: { 
          id: quizId,
          courseId
        },
        include: {
          questions: {
            orderBy: { order: 'asc' },
            select: {
              id: true,
              type: true,
              question: true,
              options: true,
              points: true,
              order: true
              // Don't include correctAnswer or explanation
            }
          },
          _count: {
            select: { questions: true }
          }
        }
      })

      if (!quiz || !quiz.isPublished) {
        return APIResponse.error('Quiz not found or not available', 404)
      }

      // Check enrollment
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      if (!enrollment || enrollment.status !== 'ACTIVE') {
        return APIResponse.error('You must be enrolled in the course to take this quiz', 403)
      }

      // Check if user has exceeded max attempts
      const completedAttempts = await prisma.courseQuizAttempt.count({
        where: {
          userId: user.id,
          quizId: quizId,
          completedAt: { not: null }
        }
      })

      if (completedAttempts >= quiz.maxAttempts) {
        return APIResponse.error('You have exceeded the maximum number of attempts for this quiz', 403)
      }

      // Check if user has an ongoing attempt
      const ongoingAttempt = await prisma.courseQuizAttempt.findFirst({
        where: {
          userId: user.id,
          quizId: quizId,
          completedAt: null
        }
      })

      if (ongoingAttempt) {
        // Return existing attempt
        return APIResponse.success({
          message: 'Resuming existing quiz attempt',
          attempt: {
            id: ongoingAttempt.id,
            startedAt: ongoingAttempt.startedAt,
            timeLimit: quiz.timeLimit,
            questions: quiz.questions
          }
        })
      }

      // Create new attempt
      const attempt = await prisma.courseQuizAttempt.create({
        data: {
          userId: user.id,
          quizId: quizId,
          totalQuestions: quiz._count.questions,
          answers: {}
        }
      })

      return APIResponse.success({
        message: 'Quiz attempt started successfully',
        attempt: {
          id: attempt.id,
          startedAt: attempt.startedAt,
          timeLimit: quiz.timeLimit,
          questions: quiz.questions
        }
      })

    } catch (error) {
      console.error('Error starting quiz attempt:', error)
      return APIResponse.error(
        'Failed to start quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/student/quizzes/[quizId] - Submit quiz attempt
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: submitQuizSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const quizId = request.url.split('/').pop()
      const { attemptId, answers, timeSpent } = validatedBody

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Get attempt and verify ownership
      const attempt = await prisma.courseQuizAttempt.findUnique({
        where: { 
          id: attemptId,
          userId: user.id,
          quizId: quizId
        },
        include: {
          quiz: {
            include: {
              questions: {
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!attempt) {
        return APIResponse.error('Quiz attempt not found', 404)
      }

      if (attempt.completedAt) {
        return APIResponse.error('Quiz attempt already completed', 400)
      }

      // Calculate score
      let correctAnswers = 0
      let totalPoints = 0
      let earnedPoints = 0
      const detailedResults: any[] = []

      for (const question of attempt.quiz.questions) {
        const userAnswer = answers[question.id]
        const isCorrect = userAnswer === question.correctAnswer
        
        if (isCorrect) {
          correctAnswers++
          earnedPoints += question.points
        }
        
        totalPoints += question.points

        detailedResults.push({
          questionId: question.id,
          question: question.question,
          userAnswer,
          correctAnswer: question.correctAnswer,
          isCorrect,
          points: question.points,
          earnedPoints: isCorrect ? question.points : 0,
          explanation: question.explanation
        })
      }

      const score = totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0
      const isPassed = score >= attempt.quiz.passingScore

      // Update attempt
      const completedAttempt = await prisma.courseQuizAttempt.update({
        where: { id: attemptId },
        data: {
          answers,
          score,
          correctAnswers,
          timeSpent,
          isPassed,
          completedAt: new Date()
        }
      })

      return APIResponse.success({
        message: 'Quiz submitted successfully',
        result: {
          attemptId: completedAttempt.id,
          score: Math.round(score * 100) / 100,
          correctAnswers,
          totalQuestions: attempt.totalQuestions,
          earnedPoints,
          totalPoints,
          isPassed,
          passingScore: attempt.quiz.passingScore,
          timeSpent,
          completedAt: completedAttempt.completedAt,
          detailedResults
        }
      })

    } catch (error) {
      console.error('Error submitting quiz:', error)
      return APIResponse.error(
        'Failed to submit quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
