import { NextRequest } from 'next/server'
import { createAP<PERSON>Handler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { uploadVideoToBunny, generateVideoThumbnail } from '@/lib/bunny-api'
import { isValidVideoFile, formatFileSize } from '@/lib/bunny-config'
import { z } from 'zod'

const uploadVideoSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  sectionId: z.string().min(1, 'Section ID is required'),
  topicId: z.string().min(1, 'Topic ID is required'),
  title: z.string().optional(),
  description: z.string().optional()
})

// POST /api/instructor/upload/video - Upload video to Bunny CDN
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const formData = await request.formData()
      const file = formData.get('file') as File
      const courseId = formData.get('courseId') as string
      const sectionId = formData.get('sectionId') as string
      const topicId = formData.get('topicId') as string
      const title = formData.get('title') as string || file?.name
      const description = formData.get('description') as string || ''

      // Validate required fields
      if (!file || !courseId || !sectionId || !topicId) {
        return APIResponse.error('File, courseId, sectionId, and topicId are required', 400)
      }

      // Validate file type
      if (!isValidVideoFile(file.name)) {
        return APIResponse.error(
          'Invalid video file format. Please upload MP4, MOV, AVI, MKV, WebM, or M4V files.',
          400
        )
      }

      // Check file size (2GB limit)
      const maxSize = 2 * 1024 * 1024 * 1024 // 2GB
      if (file.size > maxSize) {
        return APIResponse.error(
          `File size too large. Maximum allowed size is ${formatFileSize(maxSize)}.`,
          400
        )
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found or access denied', 404)
      }

      // Verify section belongs to course
      const section = await prisma.courseSection.findUnique({
        where: { 
          id: sectionId,
          courseId
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Verify topic belongs to section
      const topic = await prisma.courseTopic.findUnique({
        where: { 
          id: topicId,
          sectionId
        }
      })

      if (!topic) {
        return APIResponse.error('Topic not found', 404)
      }

      console.log(`🎥 Starting video upload for topic: ${topic.title}`)
      console.log(`📁 File: ${file.name} (${formatFileSize(file.size)})`)

      // Upload video to Bunny CDN
      const uploadResult = await uploadVideoToBunny(
        file,
        courseId,
        sectionId,
        topicId,
        file.name
      )

      if (!uploadResult.success) {
        return APIResponse.error(
          uploadResult.message || 'Failed to upload video',
          500
        )
      }

      // Generate thumbnail (optional, may fail)
      let thumbnailUrl = uploadResult.thumbnailUrl
      try {
        const generatedThumbnail = await generateVideoThumbnail(
          uploadResult.videoUrl?.split('/').pop() || '',
          10 // 10 seconds into video
        )
        if (generatedThumbnail) {
          thumbnailUrl = generatedThumbnail
        }
      } catch (error) {
        console.warn('⚠️ Failed to generate video thumbnail:', error)
      }

      // Create course content record
      const courseContent = await prisma.courseContent.create({
        data: {
          topicId,
          type: 'VIDEO',
          title: title || file.name,
          description,
          videoUrl: uploadResult.videoUrl,
          videoId: uploadResult.videoId,
          thumbnailUrl,
          duration: uploadResult.duration,
          fileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          order: 1 // Default order, can be updated later
        }
      })

      // Update topic duration if this is the main video
      if (uploadResult.duration) {
        await prisma.courseTopic.update({
          where: { id: topicId },
          data: {
            duration: uploadResult.duration,
            type: 'VIDEO'
          }
        })
      }

      console.log('✅ Video upload completed successfully')

      return APIResponse.success({
        message: 'Video uploaded successfully',
        content: {
          id: courseContent.id,
          type: courseContent.type,
          title: courseContent.title,
          description: courseContent.description,
          videoUrl: courseContent.videoUrl,
          thumbnailUrl: courseContent.thumbnailUrl,
          duration: courseContent.duration,
          fileSize: courseContent.fileSize,
          fileName: courseContent.fileName,
          createdAt: courseContent.createdAt
        },
        uploadInfo: {
          originalFileName: file.name,
          fileSize: formatFileSize(file.size),
          duration: uploadResult.duration ? `${Math.round(uploadResult.duration / 60)} minutes` : 'Unknown',
          videoUrl: uploadResult.videoUrl
        }
      })

    } catch (error) {
      console.error('❌ Error uploading video:', error)
      return APIResponse.error(
        'Failed to upload video: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// GET /api/instructor/upload/video - Get upload status/info
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (_request: NextRequest, { user }) => {
    try {
      // Get recent uploads for this instructor
      const recentUploads = await prisma.courseContent.findMany({
        where: {
          type: 'VIDEO',
          topic: {
            section: {
              course: {
                instructorId: user.id
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
        include: {
          topic: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      title: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      const formattedUploads = recentUploads.map(upload => ({
        id: upload.id,
        title: upload.title,
        fileName: upload.fileName,
        fileSize: upload.fileSize ? formatFileSize(upload.fileSize) : 'Unknown',
        duration: upload.duration ? `${Math.round(upload.duration / 60)} minutes` : 'Unknown',
        videoUrl: upload.videoUrl,
        thumbnailUrl: upload.thumbnailUrl,
        createdAt: upload.createdAt,
        course: {
          id: upload.topic.section.course.id,
          title: upload.topic.section.course.title
        },
        section: {
          id: upload.topic.section.id,
          title: upload.topic.section.title
        },
        topic: {
          id: upload.topic.id,
          title: upload.topic.title
        }
      }))

      return APIResponse.success({
        recentUploads: formattedUploads,
        uploadLimits: {
          maxFileSize: formatFileSize(2 * 1024 * 1024 * 1024), // 2GB
          supportedFormats: ['MP4', 'MOV', 'AVI', 'MKV', 'WebM', 'M4V'],
          maxDuration: '4 hours'
        }
      })

    } catch (error) {
      console.error('❌ Error fetching upload info:', error)
      return APIResponse.error(
        'Failed to fetch upload info: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
