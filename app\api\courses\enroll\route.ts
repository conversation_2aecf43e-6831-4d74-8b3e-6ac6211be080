import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const enrollmentSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required'),
  paymentMethod: z.enum(['free', 'stripe', 'razorpay']).optional().default('free'),
  paymentId: z.string().optional(),
  paymentAmount: z.number().optional()
})

// POST /api/courses/enroll - Enroll user in a course
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: enrollmentSchema
  },
  async (_request: NextRequest, { validatedBody, user }) => {
    try {
      const { courseId, paymentMethod, paymentId, paymentAmount } = validatedBody

      // Check if course exists and is published
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          sections: {
            where: { isPublished: true },
            include: {
              topics: {
                where: { isPublished: true },
                select: { id: true }
              }
            }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      if (course.status !== 'PUBLISHED' || !course.isPublished) {
        return APIResponse.error('Course is not available for enrollment', 400)
      }

      // Check if user is already enrolled
      const existingEnrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: courseId
          }
        }
      })

      if (existingEnrollment && existingEnrollment.status === 'ACTIVE') {
        return APIResponse.error('Already enrolled in this course', 400)
      }

      // Calculate total topics for progress tracking
      const totalTopics = course.sections.reduce(
        (sum, section) => sum + section.topics.length,
        0
      )

      // For free courses or when payment is already processed
      if (course.price === 0 || paymentMethod === 'free' || paymentId) {
        // Create or update enrollment
        const enrollment = await prisma.courseEnrollment.upsert({
          where: {
            userId_courseId: {
              userId: user.id,
              courseId: courseId
            }
          },
          update: {
            status: 'ACTIVE',
            paymentMethod,
            paymentId,
            paymentAmount: paymentAmount || course.price,
            enrolledAt: new Date(),
            totalTopics
          },
          create: {
            userId: user.id,
            courseId: courseId,
            status: 'ACTIVE',
            paymentMethod,
            paymentId,
            paymentAmount: paymentAmount || course.price,
            totalTopics
          }
        })

        // Update course enrollment count
        await prisma.course.update({
          where: { id: courseId },
          data: {
            enrollmentCount: {
              increment: 1
            }
          }
        })

        return APIResponse.success({
          message: 'Successfully enrolled in course',
          enrollment: {
            id: enrollment.id,
            courseId: enrollment.courseId,
            status: enrollment.status,
            enrolledAt: enrollment.enrolledAt,
            progress: enrollment.progress
          },
          course: {
            id: course.id,
            title: course.title,
            slug: course.slug,
            instructor: course.instructor.name,
            totalTopics
          }
        })
      } else {
        // For paid courses, return payment information
        return APIResponse.success({
          message: 'Payment required for course enrollment',
          requiresPayment: true,
          course: {
            id: course.id,
            title: course.title,
            price: course.price,
            currency: course.currency
          },
          paymentInfo: {
            amount: course.price,
            currency: course.currency,
            courseId: course.id,
            courseName: course.title
          }
        })
      }

    } catch (error) {
      console.error('Error during course enrollment:', error)
      return APIResponse.error(
        'Failed to enroll in course: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
