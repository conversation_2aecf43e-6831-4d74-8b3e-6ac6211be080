'use client'

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Bell, 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  XCircle,
  BookOpen,
  Calendar,
  MessageSquare,
  Award,
  Zap,
  Settings,
  ExternalLink
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import { getSocketClient } from '@/lib/socket-client'

interface ToastNotification {
  id: string
  type: 'COURSE' | 'ASSIGNMENT' | 'DISCUSSION' | 'SYSTEM' | 'ACHIEVEMENT' | 'QUIZ_AVAILABLE' | 'QUIZ_REMINDER'
  title: string
  message: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  actionUrl?: string
  actionText?: string
  duration?: number
  createdAt: Date
}

interface NotificationToastProviderProps {
  children: React.ReactNode
  maxToasts?: number
  defaultDuration?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
}

export function NotificationToastProvider({
  children,
  maxToasts = 5,
  defaultDuration = 5000,
  position = 'top-right'
}: NotificationToastProviderProps) {
  const { data: session } = useSession()
  const [toasts, setToasts] = useState<ToastNotification[]>([])
  const [soundEnabled, setSoundEnabled] = useState(true)

  // Load sound preference
  useEffect(() => {
    const loadSoundPreference = async () => {
      try {
        const response = await fetch('/api/user/notification-preferences')
        if (response.ok) {
          const data = await response.json()
          setSoundEnabled(data.preferences?.sound?.enabled ?? true)
        }
      } catch (error) {
        console.error('Error loading sound preference:', error)
      }
    }

    if (session?.user?.id) {
      loadSoundPreference()
    }
  }, [session?.user?.id])

  // Set up real-time notification listener
  useEffect(() => {
    if (!session?.user?.id) return

    const socketClient = getSocketClient()

    const handleNewNotification = (notification: any) => {
      // Only show toast for high priority notifications or if user has enabled all notifications
      const shouldShowToast = 
        notification.priority === 'high' || 
        notification.priority === 'urgent' ||
        notification.type === 'QUIZ_AVAILABLE' ||
        notification.type === 'QUIZ_REMINDER'

      if (shouldShowToast) {
        addToast({
          id: notification.id || Math.random().toString(36).substring(7),
          type: notification.type,
          title: notification.title,
          message: notification.message,
          priority: notification.priority,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText || 'View',
          createdAt: new Date(notification.createdAt || Date.now())
        })

        // Play notification sound
        if (soundEnabled && (notification.priority === 'high' || notification.priority === 'urgent')) {
          playNotificationSound(notification.priority)
        }
      }
    }

    socketClient.on('notification:received', handleNewNotification)

    return () => {
      socketClient.off('notification:received', handleNewNotification)
    }
  }, [session?.user?.id, soundEnabled])

  // Add toast
  const addToast = (toast: ToastNotification) => {
    setToasts(prev => {
      const newToasts = [toast, ...prev].slice(0, maxToasts)
      return newToasts
    })

    // Auto-remove toast after duration
    const duration = toast.duration || getDurationByPriority(toast.priority)
    setTimeout(() => {
      removeToast(toast.id)
    }, duration)
  }

  // Remove toast
  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  // Get duration based on priority
  const getDurationByPriority = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 10000 // 10 seconds
      case 'high':
        return 7000  // 7 seconds
      case 'normal':
        return 5000  // 5 seconds
      case 'low':
        return 3000  // 3 seconds
      default:
        return defaultDuration
    }
  }

  // Play notification sound
  const playNotificationSound = (priority: string) => {
    try {
      const audio = new Audio()
      
      // Different sounds for different priorities
      switch (priority) {
        case 'urgent':
          audio.src = '/sounds/urgent-notification.mp3'
          break
        case 'high':
          audio.src = '/sounds/high-notification.mp3'
          break
        default:
          audio.src = '/sounds/notification.mp3'
      }
      
      audio.volume = 0.5
      audio.play().catch(error => {
        console.warn('Could not play notification sound:', error)
      })
    } catch (error) {
      console.warn('Error playing notification sound:', error)
    }
  }

  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'bottom-left':
        return 'bottom-4 left-4'
      default: // top-right
        return 'top-4 right-4'
    }
  }

  return (
    <>
      {children}
      
      {/* Toast Container */}
      <div className={`fixed z-50 ${getPositionClasses()} space-y-2 pointer-events-none`}>
        <AnimatePresence>
          {toasts.map((toast) => (
            <NotificationToast
              key={toast.id}
              toast={toast}
              onRemove={() => removeToast(toast.id)}
            />
          ))}
        </AnimatePresence>
      </div>
    </>
  )
}

// Individual Toast Component
interface NotificationToastProps {
  toast: ToastNotification
  onRemove: () => void
}

function NotificationToast({ toast, onRemove }: NotificationToastProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Handle action click
  const handleActionClick = () => {
    if (toast.actionUrl) {
      window.location.href = toast.actionUrl
    }
    onRemove()
  }

  // Get notification icon and colors
  const getNotificationStyle = () => {
    switch (toast.type) {
      case 'COURSE':
        return {
          icon: <BookOpen className="w-5 h-5" />,
          bgColor: 'from-blue-500/20 to-blue-600/20',
          borderColor: 'border-blue-500/30',
          iconColor: 'text-blue-500'
        }
      case 'ASSIGNMENT':
        return {
          icon: <Calendar className="w-5 h-5" />,
          bgColor: 'from-orange-500/20 to-orange-600/20',
          borderColor: 'border-orange-500/30',
          iconColor: 'text-orange-500'
        }
      case 'DISCUSSION':
        return {
          icon: <MessageSquare className="w-5 h-5" />,
          bgColor: 'from-green-500/20 to-green-600/20',
          borderColor: 'border-green-500/30',
          iconColor: 'text-green-500'
        }
      case 'ACHIEVEMENT':
        return {
          icon: <Award className="w-5 h-5" />,
          bgColor: 'from-yellow-500/20 to-yellow-600/20',
          borderColor: 'border-yellow-500/30',
          iconColor: 'text-yellow-500'
        }
      case 'QUIZ_AVAILABLE':
      case 'QUIZ_REMINDER':
        return {
          icon: <Zap className="w-5 h-5" />,
          bgColor: 'from-purple-500/20 to-purple-600/20',
          borderColor: 'border-purple-500/30',
          iconColor: 'text-purple-500'
        }
      case 'SYSTEM':
        return {
          icon: toast.priority === 'urgent' ? <AlertTriangle className="w-5 h-5" /> : <Info className="w-5 h-5" />,
          bgColor: toast.priority === 'urgent' ? 'from-red-500/20 to-red-600/20' : 'from-gray-500/20 to-gray-600/20',
          borderColor: toast.priority === 'urgent' ? 'border-red-500/30' : 'border-gray-500/30',
          iconColor: toast.priority === 'urgent' ? 'text-red-500' : 'text-gray-500'
        }
      default:
        return {
          icon: <Bell className="w-5 h-5" />,
          bgColor: 'from-violet-500/20 to-violet-600/20',
          borderColor: 'border-violet-500/30',
          iconColor: 'text-violet-500'
        }
    }
  }

  const style = getNotificationStyle()

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.8 }}
      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
      className="pointer-events-auto"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className={`
        relative max-w-sm w-full bg-gradient-to-r ${style.bgColor} backdrop-blur-md 
        border ${style.borderColor} rounded-xl shadow-lg overflow-hidden
        ${toast.priority === 'urgent' ? 'ring-2 ring-red-500/50' : ''}
      `}>
        {/* Priority indicator */}
        {(toast.priority === 'high' || toast.priority === 'urgent') && (
          <div className={`absolute top-0 left-0 right-0 h-1 ${
            toast.priority === 'urgent' ? 'bg-red-500' : 'bg-orange-500'
          }`} />
        )}

        <div className="p-4">
          <div className="flex items-start space-x-3">
            {/* Icon */}
            <div className={`flex-shrink-0 ${style.iconColor}`}>
              {style.icon}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                    {toast.title}
                  </h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                    {toast.message}
                  </p>
                </div>

                {/* Close button */}
                <button
                  onClick={onRemove}
                  className="flex-shrink-0 ml-2 p-1 rounded-full hover:bg-white/10 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              </div>

              {/* Action button */}
              {toast.actionUrl && (
                <motion.button
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  onClick={handleActionClick}
                  className="mt-3 inline-flex items-center px-3 py-1.5 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-sm font-medium text-gray-900 dark:text-white transition-colors"
                >
                  {toast.actionText || 'View'}
                  <ExternalLink className="w-3 h-3 ml-1" />
                </motion.button>
              )}
            </div>
          </div>
        </div>

        {/* Progress bar for auto-dismiss */}
        {!isHovered && (
          <motion.div
            className="absolute bottom-0 left-0 h-1 bg-white/30"
            initial={{ width: '100%' }}
            animate={{ width: '0%' }}
            transition={{ 
              duration: getDurationByPriority(toast.priority) / 1000,
              ease: 'linear'
            }}
          />
        )}
      </div>
    </motion.div>
  )

  function getDurationByPriority(priority: string) {
    switch (priority) {
      case 'urgent':
        return 10000
      case 'high':
        return 7000
      case 'normal':
        return 5000
      case 'low':
        return 3000
      default:
        return 5000
    }
  }
}

// Hook for manually triggering toasts
export function useNotificationToast() {
  const addToast = (toast: Omit<ToastNotification, 'id' | 'createdAt'>) => {
    // This would typically dispatch to a global state or context
    // For now, we'll just log it
    console.log('Manual toast:', toast)
  }

  return { addToast }
}
