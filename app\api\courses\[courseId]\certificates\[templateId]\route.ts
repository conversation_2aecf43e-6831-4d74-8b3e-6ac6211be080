import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCertificateTemplateSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  design: z.object({
    backgroundColor: z.string().optional(),
    primaryColor: z.string().optional(),
    secondaryColor: z.string().optional(),
    fontFamily: z.string().optional(),
    layout: z.enum(['modern', 'classic', 'elegant', 'minimal']).optional(),
    showLogo: z.boolean().optional(),
    showBorder: z.boolean().optional(),
    showSignature: z.boolean().optional()
  }).optional(),
  content: z.object({
    title: z.string().optional(),
    subtitle: z.string().optional(),
    bodyText: z.string().optional(),
    footerText: z.string().optional(),
    instructorTitle: z.string().optional(),
    organizationName: z.string().optional()
  }).optional(),
  requirements: z.object({
    minCompletionPercentage: z.number().min(0).max(100).optional(),
    requireAllQuizzesPassed: z.boolean().optional(),
    minQuizScore: z.number().min(0).max(100).optional(),
    requireAllAssignmentsCompleted: z.boolean().optional()
  }).optional(),
  isActive: z.boolean().optional()
})

// GET /api/courses/[courseId]/certificates/[templateId] - Get template details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const templateId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !templateId) {
        return APIResponse.error('Course ID and Template ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get template
      const template = await prisma.certificateTemplate.findUnique({
        where: { 
          id: templateId,
          courseId
        },
        include: {
          certificates: {
            take: 10,
            orderBy: { issuedAt: 'desc' },
            include: {
              student: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true
                }
              }
            }
          },
          _count: {
            select: {
              certificates: true
            }
          }
        }
      })

      if (!template) {
        return APIResponse.error('Template not found', 404)
      }

      return APIResponse.success({
        template: {
          ...template,
          certificateCount: template._count.certificates,
          recentCertificates: template.certificates
        }
      })

    } catch (error) {
      console.error('Error fetching certificate template:', error)
      return APIResponse.error(
        'Failed to fetch template: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/courses/[courseId]/certificates/[templateId] - Update template
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: updateCertificateTemplateSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const templateId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !templateId) {
        return APIResponse.error('Course ID and Template ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if template exists
      const existingTemplate = await prisma.certificateTemplate.findUnique({
        where: { 
          id: templateId,
          courseId
        }
      })

      if (!existingTemplate) {
        return APIResponse.error('Template not found', 404)
      }

      // If setting as active, deactivate other templates
      if (validatedBody.isActive) {
        await prisma.certificateTemplate.updateMany({
          where: { 
            courseId,
            id: { not: templateId }
          },
          data: { isActive: false }
        })
      }

      // Merge nested objects properly
      const updateData: any = { ...validatedBody }
      
      if (validatedBody.design) {
        updateData.design = {
          ...existingTemplate.design,
          ...validatedBody.design
        }
      }
      
      if (validatedBody.content) {
        updateData.content = {
          ...existingTemplate.content,
          ...validatedBody.content
        }
      }
      
      if (validatedBody.requirements) {
        updateData.requirements = {
          ...existingTemplate.requirements,
          ...validatedBody.requirements
        }
      }

      // Update template
      const updatedTemplate = await prisma.certificateTemplate.update({
        where: { id: templateId },
        data: updateData,
        include: {
          _count: {
            select: {
              certificates: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Template updated successfully',
        template: {
          ...updatedTemplate,
          certificateCount: updatedTemplate._count.certificates
        }
      })

    } catch (error) {
      console.error('Error updating certificate template:', error)
      return APIResponse.error(
        'Failed to update template: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/courses/[courseId]/certificates/[templateId] - Delete template
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const templateId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !templateId) {
        return APIResponse.error('Course ID and Template ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if template exists and get certificate count
      const template = await prisma.certificateTemplate.findUnique({
        where: { 
          id: templateId,
          courseId
        },
        include: {
          _count: {
            select: { certificates: true }
          }
        }
      })

      if (!template) {
        return APIResponse.error('Template not found', 404)
      }

      // Prevent deletion if template has issued certificates
      if (template._count.certificates > 0) {
        return APIResponse.error(
          'Cannot delete template with issued certificates. Deactivate it instead.',
          400
        )
      }

      // Delete template
      await prisma.certificateTemplate.delete({
        where: { id: templateId }
      })

      return APIResponse.success({
        message: 'Template deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting certificate template:', error)
      return APIResponse.error(
        'Failed to delete template: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
