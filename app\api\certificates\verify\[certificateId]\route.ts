import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// GET /api/certificates/verify/[certificateId] - Verify certificate (public endpoint)
export const GET = createAPIHandler(
  {
    requireAuth: false // Public verification endpoint
  },
  async (request: NextRequest) => {
    try {
      const certificateId = request.url.split('/').pop()

      if (!certificateId) {
        return APIResponse.error('Certificate ID is required', 400)
      }

      // Get certificate with related data
      const certificate = await prisma.courseCertificate.findUnique({
        where: { id: certificateId },
        include: {
          student: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true
            }
          },
          course: {
            select: {
              id: true,
              title: true,
              description: true,
              thumbnailImage: true,
              instructor: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  bio: true
                }
              }
            }
          },
          template: {
            select: {
              id: true,
              name: true,
              design: true,
              content: true
            }
          }
        }
      })

      if (!certificate) {
        return APIResponse.error('Certificate not found', 404)
      }

      // Return verification data
      return APIResponse.success({
        certificate: {
          id: certificate.id,
          issuedAt: certificate.issuedAt,
          isValid: true,
          student: {
            name: certificate.student.name,
            // Don't expose email for privacy
          },
          course: {
            title: certificate.course.title,
            description: certificate.course.description,
            thumbnailImage: certificate.course.thumbnailImage,
            instructor: {
              name: certificate.course.instructor.name,
              image: certificate.course.instructor.image
            }
          },
          template: certificate.template,
          certificateData: certificate.certificateData,
          verificationDetails: {
            verifiedAt: new Date().toISOString(),
            status: 'VALID',
            issuer: 'Course Platform',
            blockchain: false // Could be extended for blockchain verification
          }
        }
      })

    } catch (error) {
      console.error('Error verifying certificate:', error)
      return APIResponse.error(
        'Failed to verify certificate: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
