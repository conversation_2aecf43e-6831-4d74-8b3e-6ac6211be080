import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateDiscussionSchema = z.object({
  title: z.string().min(1).optional(),
  content: z.string().min(1).optional(),
  category: z.enum(['GENERAL', 'QUESTION', 'ANNOUNCEMENT', 'ASSIGNMENT', 'TECHNICAL']).optional(),
  isSticky: z.boolean().optional(),
  isPinned: z.boolean().optional(),
  isLocked: z.boolean().optional(),
  tags: z.array(z.string()).optional()
})

// GET /api/courses/[courseId]/discussions/[discussionId] - Get discussion details
export const GET = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Get discussion with replies
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
              role: true
            }
          },
          topic: {
            select: {
              id: true,
              title: true,
              section: {
                select: {
                  id: true,
                  title: true
                }
              }
            }
          },
          replies: {
            where: { isDeleted: false },
            orderBy: { createdAt: 'asc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  role: true
                }
              },
              likes: {
                select: {
                  userId: true
                }
              },
              _count: {
                select: {
                  likes: true
                }
              }
            }
          },
          likes: {
            select: {
              userId: true
            }
          },
          _count: {
            select: {
              replies: true,
              likes: true
            }
          }
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      // Format replies with like status
      const formattedReplies = discussion.replies.map(reply => ({
        id: reply.id,
        content: reply.content,
        createdAt: reply.createdAt,
        updatedAt: reply.updatedAt,
        author: reply.author,
        likeCount: reply._count.likes,
        isLiked: reply.likes.some(like => like.userId === user.id),
        canEdit: reply.author.id === user.id || isInstructor,
        canDelete: reply.author.id === user.id || isInstructor
      }))

      // Check if current user has liked the discussion
      const isLiked = discussion.likes.some(like => like.userId === user.id)

      return APIResponse.success({
        discussion: {
          id: discussion.id,
          title: discussion.title,
          content: discussion.content,
          category: discussion.category,
          isSticky: discussion.isSticky,
          isPinned: discussion.isPinned,
          isLocked: discussion.isLocked,
          tags: discussion.tags,
          createdAt: discussion.createdAt,
          updatedAt: discussion.updatedAt,
          author: discussion.author,
          topic: discussion.topic,
          replyCount: discussion._count.replies,
          likeCount: discussion._count.likes,
          isLiked,
          replies: formattedReplies,
          canEdit: discussion.author.id === user.id || isInstructor,
          canDelete: discussion.author.id === user.id || isInstructor,
          canModerate: isInstructor
        }
      })

    } catch (error) {
      console.error('Error fetching discussion:', error)
      return APIResponse.error(
        'Failed to fetch discussion: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/courses/[courseId]/discussions/[discussionId] - Update discussion
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    validateBody: updateDiscussionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Get discussion and check permissions
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        },
        include: {
          course: {
            select: { instructorId: true }
          }
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      const isInstructor = discussion.course.instructorId === user.id
      const isAuthor = discussion.authorId === user.id

      if (!isAuthor && !isInstructor) {
        return APIResponse.error('Permission denied', 403)
      }

      const updateData = validatedBody

      // Only instructors can modify sticky, pinned, locked status
      if (!isInstructor) {
        delete updateData.isSticky
        delete updateData.isPinned
        delete updateData.isLocked
      }

      // Update discussion
      const updatedDiscussion = await prisma.discussionPost.update({
        where: { id: discussionId },
        data: updateData,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
              role: true
            }
          },
          _count: {
            select: {
              replies: true,
              likes: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Discussion updated successfully',
        discussion: {
          ...updatedDiscussion,
          replyCount: updatedDiscussion._count.replies,
          likeCount: updatedDiscussion._count.likes
        }
      })

    } catch (error) {
      console.error('Error updating discussion:', error)
      return APIResponse.error(
        'Failed to update discussion: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/courses/[courseId]/discussions/[discussionId] - Delete discussion
export const DELETE = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Get discussion and check permissions
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        },
        include: {
          course: {
            select: { instructorId: true }
          }
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      const isInstructor = discussion.course.instructorId === user.id
      const isAuthor = discussion.authorId === user.id

      if (!isAuthor && !isInstructor) {
        return APIResponse.error('Permission denied', 403)
      }

      // Soft delete discussion
      await prisma.discussionPost.update({
        where: { id: discussionId },
        data: { 
          isDeleted: true,
          deletedAt: new Date()
        }
      })

      return APIResponse.success({
        message: 'Discussion deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting discussion:', error)
      return APIResponse.error(
        'Failed to delete discussion: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
