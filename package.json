{"name": "three-three-blog", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:socket": "node server/socket-server.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:socket\"", "build": "next build", "start": "next start", "start:socket": "node server/socket-server.js", "start:full": "concurrently \"npm run start\" \"npm run start:socket\"", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "verify": "tsx scripts/verify-system.ts", "seed:practice": "tsx scripts/seed-practice-sessions.ts", "seed:update-data": "tsx scripts/update-existing-data.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/deepseek": "^0.2.16", "@ai-sdk/google": "^1.2.22", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/mistral": "^1.2.8", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/xai": "^1.2.18", "@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.12.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-pdf/renderer": "^4.3.0", "@types/nodemailer": "^6.4.17", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.4.1", "@vercel/kv": "^3.0.0", "ai": "^4.3.19", "chart.js": "^4.5.0", "chartjs-to-image": "^1.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.14.1", "html2canvas": "^1.4.1", "ioredis": "^5.6.1", "lucide-react": "^0.468.0", "next": "15.1.0", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.4", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.1", "react-pdf": "^10.0.1", "react-type-animation": "^3.2.0", "recharts": "^3.1.0", "remark-gfm": "^4.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@types/jest": "^29.5.12", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "concurrently": "^8.2.2", "eslint": "^9.16.0", "eslint-config-next": "15.1.0", "postcss": "^8.4.49", "prisma": "^6.12.0", "tailwindcss": "^3.4.16", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.7.2"}}