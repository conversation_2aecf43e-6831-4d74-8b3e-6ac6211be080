import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/courses/[courseId]/discussions/[discussionId]/like - Toggle discussion like
export const POST = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Verify discussion exists
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      // Check if user already liked this discussion
      const existingLike = await prisma.discussionLike.findUnique({
        where: {
          userId_discussionId: {
            userId: user.id,
            discussionId
          }
        }
      })

      let isLiked: boolean
      let likeCount: number

      if (existingLike) {
        // Unlike - remove the like
        await prisma.discussionLike.delete({
          where: { id: existingLike.id }
        })
        isLiked = false
      } else {
        // Like - create new like
        await prisma.discussionLike.create({
          data: {
            userId: user.id,
            discussionId
          }
        })
        isLiked = true
      }

      // Get updated like count
      likeCount = await prisma.discussionLike.count({
        where: { discussionId }
      })

      return APIResponse.success({
        message: isLiked ? 'Discussion liked' : 'Discussion unliked',
        isLiked,
        likeCount
      })

    } catch (error) {
      console.error('Error toggling discussion like:', error)
      return APIResponse.error(
        'Failed to toggle like: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
