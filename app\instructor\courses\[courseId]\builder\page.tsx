'use client'

import React, { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Settings, 
  Plus,
  BookOpen,
  Video,
  FileText,
  Users,
  BarChart3,
  Loader2
} from 'lucide-react'
import { toast } from 'sonner'
import { CourseBuilderSidebar } from '@/components/instructor/course-builder-sidebar'
import { CourseStructureEditor } from '@/components/instructor/course-structure-editor'
import { CourseSettingsPanel } from '@/components/instructor/course-settings-panel'
import { CoursePreview } from '@/components/instructor/course-preview'
import { CourseAnalytics } from '@/components/instructor/course-analytics'

interface Course {
  id: string
  title: string
  description?: string
  slug: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' | 'PRIVATE'
  isPublished: boolean
  thumbnailImage?: string
  category?: string
  price: number
  currency: string
  instructor: {
    id: string
    name: string
    image?: string
  }
  sections: any[]
  totalLessons: number
  totalDuration: number
  enrollmentCount: number
  createdAt: string
  updatedAt: string
}

type ActiveTab = 'structure' | 'settings' | 'preview' | 'analytics'

export default function CourseBuilderPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params.courseId as string

  const [course, setCourse] = useState<Course | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState<ActiveTab>('structure')
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Fetch course data
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/instructor/courses/${courseId}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch course')
        }

        const data = await response.json()
        setCourse(data.course)
      } catch (error) {
        console.error('Error fetching course:', error)
        toast.error('Failed to load course')
        router.push('/instructor/courses')
      } finally {
        setIsLoading(false)
      }
    }

    if (courseId) {
      fetchCourse()
    }
  }, [courseId, router])

  // Handle course updates
  const handleCourseUpdate = (updatedCourse: Partial<Course>) => {
    setCourse(prev => prev ? { ...prev, ...updatedCourse } : null)
    setHasUnsavedChanges(true)
  }

  // Save course changes
  const handleSave = async () => {
    if (!course || !hasUnsavedChanges) return

    try {
      setIsSaving(true)
      const response = await fetch(`/api/instructor/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: course.title,
          description: course.description,
          category: course.category,
          price: course.price,
          thumbnailImage: course.thumbnailImage
        })
      })

      if (!response.ok) {
        throw new Error('Failed to save course')
      }

      const data = await response.json()
      setCourse(data.course)
      setHasUnsavedChanges(false)
      toast.success('Course saved successfully!')
    } catch (error) {
      console.error('Error saving course:', error)
      toast.error('Failed to save course')
    } finally {
      setIsSaving(false)
    }
  }

  // Publish/unpublish course
  const handlePublishToggle = async () => {
    if (!course) return

    try {
      const newStatus = course.status === 'PUBLISHED' ? 'DRAFT' : 'PUBLISHED'
      
      const response = await fetch(`/api/instructor/courses/${courseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status: newStatus
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update course status')
      }

      const data = await response.json()
      setCourse(data.course)
      
      toast.success(
        newStatus === 'PUBLISHED' 
          ? 'Course published successfully!' 
          : 'Course unpublished successfully!'
      )
    } catch (error) {
      console.error('Error updating course status:', error)
      toast.error('Failed to update course status')
    }
  }

  // Navigation tabs
  const tabs = [
    {
      id: 'structure' as ActiveTab,
      label: 'Course Structure',
      icon: BookOpen,
      description: 'Organize sections, topics, and content'
    },
    {
      id: 'settings' as ActiveTab,
      label: 'Course Settings',
      icon: Settings,
      description: 'Configure course details and pricing'
    },
    {
      id: 'preview' as ActiveTab,
      label: 'Preview',
      icon: Eye,
      description: 'See how students will view your course'
    },
    {
      id: 'analytics' as ActiveTab,
      label: 'Analytics',
      icon: BarChart3,
      description: 'View course performance and student engagement'
    }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-violet-500 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading course builder...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400">Course not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
      {/* Header */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/instructor/courses')}
                className="p-2 text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  {course.title}
                </h1>
                <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    course.status === 'PUBLISHED' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {course.status}
                  </span>
                  <span>•</span>
                  <span>{course.totalLessons} lessons</span>
                  <span>•</span>
                  <span>{course.enrollmentCount} students</span>
                </div>
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center space-x-3">
              <button
                onClick={handleSave}
                disabled={!hasUnsavedChanges || isSaving}
                className="flex items-center space-x-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSaving ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSaving ? 'Saving...' : 'Save'}</span>
              </button>

              <button
                onClick={handlePublishToggle}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  course.status === 'PUBLISHED'
                    ? 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
                    : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
                }`}
              >
                {course.status === 'PUBLISHED' ? 'Unpublish' : 'Publish'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex space-x-8">
          {/* Sidebar */}
          <div className="w-80 flex-shrink-0">
            <CourseBuilderSidebar
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              course={course}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {activeTab === 'structure' && (
                  <CourseStructureEditor
                    course={course}
                    onCourseUpdate={handleCourseUpdate}
                  />
                )}
                
                {activeTab === 'settings' && (
                  <CourseSettingsPanel
                    course={course}
                    onCourseUpdate={handleCourseUpdate}
                  />
                )}
                
                {activeTab === 'preview' && (
                  <CoursePreview course={course} />
                )}
                
                {activeTab === 'analytics' && (
                  <CourseAnalytics courseId={course.id} />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  )
}
