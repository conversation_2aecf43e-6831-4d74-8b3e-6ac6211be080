import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createReplySchema = z.object({
  content: z.string().min(1, 'Reply content is required'),
  parentReplyId: z.string().optional() // For nested replies
})

const querySchema = commonSchemas.pagination.extend({
  sortBy: z.enum(['oldest', 'newest', 'popular']).default('oldest')
})

// GET /api/courses/[courseId]/discussions/[discussionId]/replies - Get discussion replies
export const GET = createAPIHandler(
  {
    requireAuth: true,
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]
      const { page = 1, limit = 20, sortBy = 'oldest' } = validatedQuery

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Verify discussion exists
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      // Build order by clause
      let orderBy: any = { createdAt: 'asc' }
      
      switch (sortBy) {
        case 'newest':
          orderBy = { createdAt: 'desc' }
          break
        case 'popular':
          orderBy = [
            { likes: { _count: 'desc' } },
            { createdAt: 'asc' }
          ]
          break
        default: // oldest
          orderBy = { createdAt: 'asc' }
      }

      // Get total count
      const total = await prisma.discussionReply.count({
        where: {
          discussionId,
          isDeleted: false
        }
      })

      // Get replies
      const replies = await prisma.discussionReply.findMany({
        where: {
          discussionId,
          isDeleted: false
        },
        orderBy,
        skip: (page - 1) * limit,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
              role: true
            }
          },
          parentReply: {
            select: {
              id: true,
              content: true,
              author: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          childReplies: {
            where: { isDeleted: false },
            take: 3,
            orderBy: { createdAt: 'asc' },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                  role: true
                }
              },
              _count: {
                select: { likes: true }
              }
            }
          },
          likes: {
            select: { userId: true }
          },
          _count: {
            select: {
              likes: true,
              childReplies: true
            }
          }
        }
      })

      // Format replies
      const formattedReplies = replies.map(reply => ({
        id: reply.id,
        content: reply.content,
        createdAt: reply.createdAt,
        updatedAt: reply.updatedAt,
        author: reply.author,
        parentReply: reply.parentReply,
        childReplies: reply.childReplies.map(child => ({
          ...child,
          likeCount: child._count.likes,
          isLiked: false // TODO: Check user likes
        })),
        likeCount: reply._count.likes,
        childReplyCount: reply._count.childReplies,
        isLiked: reply.likes.some(like => like.userId === user.id),
        canEdit: reply.author.id === user.id || isInstructor,
        canDelete: reply.author.id === user.id || isInstructor
      }))

      return APIResponse.success({
        replies: formattedReplies,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching replies:', error)
      return APIResponse.error(
        'Failed to fetch replies: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/courses/[courseId]/discussions/[discussionId]/replies - Create reply
export const POST = createAPIHandler(
  {
    requireAuth: true,
    validateBody: createReplySchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const discussionId = urlParts.slice(-2, -1)[0]
      const courseId = urlParts.slice(-4, -3)[0]

      if (!courseId || !discussionId) {
        return APIResponse.error('Course ID and Discussion ID are required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Verify discussion exists and is not locked
      const discussion = await prisma.discussionPost.findUnique({
        where: { 
          id: discussionId,
          courseId,
          isDeleted: false
        }
      })

      if (!discussion) {
        return APIResponse.error('Discussion not found', 404)
      }

      if (discussion.isLocked && !isInstructor) {
        return APIResponse.error('Discussion is locked', 403)
      }

      const { content, parentReplyId } = validatedBody

      // Verify parent reply exists if provided
      if (parentReplyId) {
        const parentReply = await prisma.discussionReply.findUnique({
          where: { 
            id: parentReplyId,
            discussionId,
            isDeleted: false
          }
        })

        if (!parentReply) {
          return APIResponse.error('Parent reply not found', 404)
        }
      }

      // Create reply
      const reply = await prisma.discussionReply.create({
        data: {
          content,
          discussionId,
          authorId: user.id,
          parentReplyId
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
              role: true
            }
          },
          parentReply: {
            select: {
              id: true,
              content: true,
              author: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          _count: {
            select: {
              likes: true,
              childReplies: true
            }
          }
        }
      })

      // Update discussion's updatedAt timestamp
      await prisma.discussionPost.update({
        where: { id: discussionId },
        data: { updatedAt: new Date() }
      })

      return APIResponse.success({
        message: 'Reply created successfully',
        reply: {
          ...reply,
          likeCount: reply._count.likes,
          childReplyCount: reply._count.childReplies,
          isLiked: false,
          canEdit: true,
          canDelete: true,
          childReplies: []
        }
      })

    } catch (error) {
      console.error('Error creating reply:', error)
      return APIResponse.error(
        'Failed to create reply: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
