import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { generateSecureVideoUrl } from '@/lib/bunny-config'

// GET /api/student/stream/[contentId] - Get secure video streaming URL
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const contentId = request.url.split('/').pop()

      if (!contentId) {
        return APIResponse.error('Content ID is required', 400)
      }

      // Get content with course information
      const content = await prisma.courseContent.findUnique({
        where: { id: contentId },
        include: {
          topic: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      title: true,
                      instructorId: true,
                      status: true,
                      isPublished: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!content) {
        return APIResponse.error('Content not found', 404)
      }

      if (content.type !== 'VIDEO') {
        return APIResponse.error('Content is not a video', 400)
      }

      const course = content.topic.section.course

      // Check if course is published
      if (course.status !== 'PUBLISHED' || !course.isPublished) {
        return APIResponse.error('Course is not available', 403)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      // Allow access if user is enrolled OR if it's a free preview
      const hasAccess = enrollment?.status === 'ACTIVE' || 
                       content.topic.section.isFree || 
                       content.topic.isFree

      if (!hasAccess) {
        return APIResponse.error('Access denied. Please enroll in the course to view this content.', 403)
      }

      // Get user's IP for secure URL generation (optional)
      const userIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'

      // Generate secure video URL (expires in 4 hours)
      const expirationTime = Date.now() + (4 * 60 * 60 * 1000) // 4 hours
      const secureVideoUrl = generateSecureVideoUrl(
        content.videoUrl || '',
        expirationTime,
        userIp
      )

      // Get or create video progress
      let videoProgress = await prisma.videoProgress.findUnique({
        where: {
          userId_contentId: {
            userId: user.id,
            contentId
          }
        }
      })

      if (!videoProgress) {
        videoProgress = await prisma.videoProgress.create({
          data: {
            userId: user.id,
            contentId,
            watchedDuration: 0,
            totalDuration: content.duration || 0,
            lastPosition: 0,
            isCompleted: false
          }
        })
      }

      // Update last accessed time for enrollment
      if (enrollment) {
        await prisma.courseEnrollment.update({
          where: { id: enrollment.id },
          data: { lastAccessedAt: new Date() }
        })
      }

      return APIResponse.success({
        content: {
          id: content.id,
          title: content.title,
          description: content.description,
          duration: content.duration,
          thumbnailUrl: content.thumbnailUrl,
          type: content.type
        },
        streaming: {
          videoUrl: secureVideoUrl,
          expiresAt: new Date(expirationTime).toISOString(),
          qualities: content.qualities || [],
          isSecure: true
        },
        progress: {
          watchedDuration: videoProgress.watchedDuration,
          totalDuration: videoProgress.totalDuration,
          lastPosition: videoProgress.lastPosition,
          isCompleted: videoProgress.isCompleted,
          watchPercentage: videoProgress.totalDuration > 0 
            ? Math.round((videoProgress.watchedDuration / videoProgress.totalDuration) * 100)
            : 0
        },
        course: {
          id: course.id,
          title: course.title
        },
        topic: {
          id: content.topic.id,
          title: content.topic.title
        },
        section: {
          id: content.topic.section.id,
          title: content.topic.section.title
        },
        access: {
          isEnrolled: !!enrollment,
          isFreePreview: content.topic.section.isFree || content.topic.isFree,
          enrollmentStatus: enrollment?.status || null
        }
      })

    } catch (error) {
      console.error('❌ Error getting video stream:', error)
      return APIResponse.error(
        'Failed to get video stream: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/student/stream/[contentId] - Update video watch progress
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const contentId = request.url.split('/').pop()

      if (!contentId) {
        return APIResponse.error('Content ID is required', 400)
      }

      const body = await request.json()
      const { 
        currentTime = 0, 
        duration = 0, 
        watchedDuration = 0,
        isCompleted = false 
      } = body

      // Verify content exists and user has access
      const content = await prisma.courseContent.findUnique({
        where: { id: contentId },
        include: {
          topic: {
            include: {
              section: {
                include: {
                  course: {
                    select: {
                      id: true,
                      status: true,
                      isPublished: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!content || content.type !== 'VIDEO') {
        return APIResponse.error('Video content not found', 404)
      }

      const course = content.topic.section.course

      // Check enrollment
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: course.id
          }
        }
      })

      const hasAccess = enrollment?.status === 'ACTIVE' || 
                       content.topic.section.isFree || 
                       content.topic.isFree

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Calculate completion based on watch percentage
      const watchPercentage = duration > 0 ? (watchedDuration / duration) * 100 : 0
      const autoCompleted = watchPercentage >= 90 // Consider 90%+ as completed

      // Update video progress
      const videoProgress = await prisma.videoProgress.upsert({
        where: {
          userId_contentId: {
            userId: user.id,
            contentId
          }
        },
        update: {
          watchedDuration: Math.max(watchedDuration, 0),
          totalDuration: Math.max(duration, content.duration || 0),
          lastPosition: Math.max(currentTime, 0),
          isCompleted: isCompleted || autoCompleted,
          ...(isCompleted || autoCompleted ? { completedAt: new Date() } : {})
        },
        create: {
          userId: user.id,
          contentId,
          watchedDuration: Math.max(watchedDuration, 0),
          totalDuration: Math.max(duration, content.duration || 0),
          lastPosition: Math.max(currentTime, 0),
          isCompleted: isCompleted || autoCompleted,
          ...(isCompleted || autoCompleted ? { completedAt: new Date() } : {})
        }
      })

      // If video is completed, check if topic should be marked as completed
      if (videoProgress.isCompleted && enrollment) {
        const topicProgress = await prisma.courseProgress.findUnique({
          where: {
            userId_topicId: {
              userId: user.id,
              topicId: content.topicId
            }
          }
        })

        // Mark topic as completed if it's not already
        if (!topicProgress?.isCompleted) {
          await prisma.courseProgress.upsert({
            where: {
              userId_topicId: {
                userId: user.id,
                topicId: content.topicId
              }
            },
            update: {
              isCompleted: true,
              completedAt: new Date()
            },
            create: {
              userId: user.id,
              topicId: content.topicId,
              enrollmentId: enrollment.id,
              isCompleted: true,
              timeSpent: Math.round(watchedDuration),
              completedAt: new Date()
            }
          })
        }
      }

      return APIResponse.success({
        message: 'Progress updated successfully',
        progress: {
          watchedDuration: videoProgress.watchedDuration,
          totalDuration: videoProgress.totalDuration,
          lastPosition: videoProgress.lastPosition,
          isCompleted: videoProgress.isCompleted,
          watchPercentage: Math.round(watchPercentage),
          completedAt: videoProgress.completedAt
        }
      })

    } catch (error) {
      console.error('❌ Error updating video progress:', error)
      return APIResponse.error(
        'Failed to update progress: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
