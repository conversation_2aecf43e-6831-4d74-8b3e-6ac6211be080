'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  Video, 
  FileText,
  ChevronDown,
  ChevronRight,
  Lock,
  Eye,
  Monitor,
  Smartphone,
  Tablet
} from 'lucide-react'
import { formatDuration } from '@/lib/bunny-config'

interface Course {
  id: string
  title: string
  description?: string
  shortDescription?: string
  thumbnailImage?: string
  previewVideo?: string
  category?: string
  level?: string
  price: number
  originalPrice?: number
  currency: string
  rating?: number
  reviewCount: number
  enrollmentCount: number
  totalLessons: number
  totalDuration: number
  tags: string[]
  instructor: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  sections: any[]
}

interface CoursePreviewProps {
  course: Course
}

type ViewMode = 'desktop' | 'tablet' | 'mobile'

export function CoursePreview({ course }: CoursePreviewProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('desktop')
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const viewModes = [
    { id: 'desktop' as ViewMode, icon: Monitor, label: 'Desktop' },
    { id: 'tablet' as ViewMode, icon: Tablet, label: 'Tablet' },
    { id: 'mobile' as ViewMode, icon: Smartphone, label: 'Mobile' }
  ]

  const getViewModeStyles = () => {
    switch (viewMode) {
      case 'mobile':
        return 'max-w-sm mx-auto'
      case 'tablet':
        return 'max-w-2xl mx-auto'
      default:
        return 'max-w-6xl mx-auto'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Course Preview
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            See how your course will appear to students
          </p>
        </div>

        {/* View Mode Selector */}
        <div className="flex items-center space-x-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-lg p-1 border border-gray-200 dark:border-gray-700">
          {viewModes.map((mode) => {
            const Icon = mode.icon
            const isActive = viewMode === mode.id

            return (
              <button
                key={mode.id}
                onClick={() => setViewMode(mode.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-200 ${
                  isActive
                    ? 'bg-violet-600 text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium">{mode.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Preview Container */}
      <div className="bg-gray-100 dark:bg-gray-900 rounded-xl p-6 min-h-[600px]">
        <div className={`transition-all duration-300 ${getViewModeStyles()}`}>
          {/* Course Header */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            {/* Hero Section */}
            <div className="relative">
              {course.thumbnailImage ? (
                <div className="aspect-video bg-gray-200 dark:bg-gray-700">
                  <img
                    src={course.thumbnailImage}
                    alt={course.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="aspect-video bg-gradient-to-br from-violet-500 to-purple-600 flex items-center justify-center">
                  <div className="text-center text-white">
                    <Video className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">Course Thumbnail</p>
                    <p className="text-sm opacity-75">Upload a thumbnail in settings</p>
                  </div>
                </div>
              )}

              {/* Play Button Overlay */}
              {course.previewVideo && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                  <button className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors">
                    <Play className="w-6 h-6 text-gray-900 ml-1" />
                  </button>
                </div>
              )}

              {/* Course Status Badge */}
              <div className="absolute top-4 left-4">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  course.price === 0
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-200'
                }`}>
                  {course.price === 0 ? 'Free' : 'Paid'}
                </span>
              </div>
            </div>

            {/* Course Info */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {course.title}
                  </h1>
                  
                  {course.shortDescription && (
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {course.shortDescription}
                    </p>
                  )}

                  {/* Course Meta */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    {course.rating && (
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="font-medium">{course.rating.toFixed(1)}</span>
                        <span>({course.reviewCount} reviews)</span>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{course.enrollmentCount} students</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(course.totalDuration * 60)}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <BookOpen className="w-4 h-4" />
                      <span>{course.totalLessons} lessons</span>
                    </div>

                    {course.level && (
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                        {course.level}
                      </span>
                    )}
                  </div>
                </div>

                {/* Pricing */}
                <div className="text-right ml-6">
                  {course.price === 0 ? (
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      Free
                    </div>
                  ) : (
                    <div>
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        {course.currency === 'USD' && '$'}
                        {course.currency === 'EUR' && '€'}
                        {course.currency === 'GBP' && '£'}
                        {course.currency === 'INR' && '₹'}
                        {course.price}
                      </div>
                      {course.originalPrice && course.originalPrice > course.price && (
                        <div className="text-sm text-gray-500 dark:text-gray-400 line-through">
                          {course.currency === 'USD' && '$'}
                          {course.currency === 'EUR' && '€'}
                          {course.currency === 'GBP' && '£'}
                          {course.currency === 'INR' && '₹'}
                          {course.originalPrice}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Instructor Info */}
              <div className="flex items-center space-x-3 mb-6">
                {course.instructor.image ? (
                  <img
                    src={course.instructor.image}
                    alt={course.instructor.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {course.instructor.name.charAt(0)}
                    </span>
                  </div>
                )}
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {course.instructor.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Course Instructor
                  </div>
                </div>
              </div>

              {/* Tags */}
              {course.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {course.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-violet-100 dark:bg-violet-900/30 text-violet-700 dark:text-violet-300 rounded-full text-sm"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button className="flex-1 bg-gradient-to-r from-violet-500 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-violet-600 hover:to-purple-700 transition-all duration-200">
                  {course.price === 0 ? 'Enroll for Free' : 'Enroll Now'}
                </button>
                <button className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  Preview
                </button>
              </div>
            </div>
          </div>

          {/* Course Content */}
          <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-6">
              Course Content
            </h2>

            {course.sections.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No Content Yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Add sections and lessons to see the course structure here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {course.sections.map((section, sectionIndex) => (
                  <div
                    key={section.id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                  >
                    <button
                      onClick={() => toggleSection(section.id)}
                      className="w-full p-4 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          {expandedSections.has(section.id) ? (
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="w-4 h-4 text-gray-500" />
                          )}
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              Section {sectionIndex + 1}: {section.title}
                            </div>
                            {section.description && (
                              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                {section.description}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {section.topics?.length || 0} lessons
                        </div>
                      </div>
                    </button>

                    <AnimatePresence>
                      {expandedSections.has(section.id) && (
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: 'auto' }}
                          exit={{ height: 0 }}
                          className="overflow-hidden"
                        >
                          <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                            {section.topics?.length > 0 ? (
                              <div className="space-y-3">
                                {section.topics.map((topic: any, topicIndex: number) => (
                                  <div
                                    key={topic.id}
                                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                  >
                                    {topic.type === 'VIDEO' ? (
                                      <Video className="w-4 h-4 text-violet-500" />
                                    ) : (
                                      <FileText className="w-4 h-4 text-blue-500" />
                                    )}
                                    
                                    <div className="flex-1">
                                      <div className="font-medium text-gray-900 dark:text-white">
                                        {topicIndex + 1}. {topic.title}
                                      </div>
                                      {topic.duration && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                          {formatDuration(topic.duration)}
                                        </div>
                                      )}
                                    </div>

                                    <div className="flex items-center space-x-2">
                                      {topic.isFree ? (
                                        <Eye className="w-4 h-4 text-green-500" />
                                      ) : (
                                        <Lock className="w-4 h-4 text-gray-400" />
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-6">
                                <p className="text-gray-500 dark:text-gray-400">
                                  No lessons in this section yet
                                </p>
                              </div>
                            )}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Course Description */}
          {course.description && (
            <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                About This Course
              </h2>
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {course.description}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
