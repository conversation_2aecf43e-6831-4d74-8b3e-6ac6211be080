'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Star, 
  Clock, 
  Users, 
  BookOpen, 
  TrendingUp, 
  Award, 
  Play,
  Heart,
  Share2,
  MoreHorizontal,
  Grid3X3,
  List,
  Filter,
  ArrowUpDown
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { GlassmorphicCard, InteractiveGlassCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicButton, IconButton } from '@/components/ui/glassmorphic-button'
import { GradientText } from '@/components/ui/animated-background'

interface Course {
  id: string
  title: string
  description: string
  shortDescription: string
  thumbnailImage?: string
  price: number
  originalPrice?: number
  currency: string
  level: string
  category: string
  tags: string[]
  language: string
  estimatedDuration: number
  averageRating: number
  reviewCount: number
  enrollmentCount: number
  isFeatured: boolean
  createdAt: string
  instructor: {
    id: string
    name: string
    image?: string
  }
  trending?: {
    score: number
    recentEnrollments: number
    period: string
  }
  recommendationScore?: number
  recommendationReason?: string
}

interface SearchResultsProps {
  courses: Course[]
  loading: boolean
  query: string
  totalResults?: number
  onCourseSelect: (courseId: string) => void
  onEnroll: (courseId: string) => void
  className?: string
}

export function SearchResults({
  courses,
  loading,
  query,
  totalResults,
  onCourseSelect,
  onEnroll,
  className = ''
}: SearchResultsProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [favorites, setFavorites] = useState<Set<string>>(new Set())

  const toggleFavorite = (courseId: string) => {
    setFavorites(prev => {
      const newSet = new Set(prev)
      if (newSet.has(courseId)) {
        newSet.delete(courseId)
      } else {
        newSet.add(courseId)
      }
      return newSet
    })
  }

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return 'Free'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Searching courses...</p>
          </div>
        </div>
      </div>
    )
  }

  if (courses.length === 0 && query) {
    return (
      <div className={`space-y-6 ${className}`}>
        <GlassmorphicCard className="p-12 text-center">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No courses found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            We couldn't find any courses matching "{query}". Try adjusting your search terms or filters.
          </p>
          <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
            <p>• Check your spelling</p>
            <p>• Try more general keywords</p>
            <p>• Remove some filters</p>
            <p>• Browse our course categories</p>
          </div>
        </GlassmorphicCard>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {query ? (
              <>
                Search results for "<GradientText>{query}</GradientText>"
              </>
            ) : (
              'All Courses'
            )}
          </h2>
          {totalResults && (
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {totalResults.toLocaleString()} courses found
            </p>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <IconButton
              variant={viewMode === 'grid' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="w-4 h-4" />
            </IconButton>
            <IconButton
              variant={viewMode === 'list' ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </IconButton>
          </div>
        </div>
      </div>

      {/* Results Grid/List */}
      <AnimatePresence mode="wait">
        {viewMode === 'grid' ? (
          <motion.div
            key="grid"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {courses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <CourseCard
                  course={course}
                  isFavorite={favorites.has(course.id)}
                  onToggleFavorite={() => toggleFavorite(course.id)}
                  onSelect={() => onCourseSelect(course.id)}
                  onEnroll={() => onEnroll(course.id)}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            key="list"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="space-y-4"
          >
            {courses.map((course, index) => (
              <motion.div
                key={course.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <CourseListItem
                  course={course}
                  isFavorite={favorites.has(course.id)}
                  onToggleFavorite={() => toggleFavorite(course.id)}
                  onSelect={() => onCourseSelect(course.id)}
                  onEnroll={() => onEnroll(course.id)}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Course Card Component
interface CourseCardProps {
  course: Course
  isFavorite: boolean
  onToggleFavorite: () => void
  onSelect: () => void
  onEnroll: () => void
}

function CourseCard({ course, isFavorite, onToggleFavorite, onSelect, onEnroll }: CourseCardProps) {
  return (
    <InteractiveGlassCard
      className="overflow-hidden group"
      onClick={onSelect}
    >
      {/* Course Image */}
      <div className="relative h-48 bg-gradient-to-br from-violet-500 to-purple-600">
        {course.thumbnailImage ? (
          <img
            src={course.thumbnailImage}
            alt={course.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <BookOpen className="w-16 h-16 text-white/50" />
          </div>
        )}
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-wrap gap-2">
          {course.isFeatured && (
            <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full flex items-center">
              <Award className="w-3 h-3 mr-1" />
              Featured
            </span>
          )}
          {course.trending && (
            <span className="px-2 py-1 bg-red-500 text-white text-xs font-medium rounded-full flex items-center">
              <TrendingUp className="w-3 h-3 mr-1" />
              Trending
            </span>
          )}
        </div>

        {/* Actions */}
        <div className="absolute top-3 right-3 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <IconButton
            variant="glass"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              onToggleFavorite()
            }}
          >
            <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-white'}`} />
          </IconButton>
          
          <IconButton
            variant="glass"
            size="sm"
            onClick={(e) => e.stopPropagation()}
          >
            <Share2 className="w-4 h-4 text-white" />
          </IconButton>
        </div>

        {/* Play Button */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
            <Play className="w-6 h-6 text-white ml-1" />
          </div>
        </div>
      </div>

      {/* Course Info */}
      <div className="p-4">
        {/* Title */}
        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors">
          {course.title}
        </h3>

        {/* Instructor */}
        <div className="flex items-center space-x-2 mb-3">
          {course.instructor.image ? (
            <img
              src={course.instructor.image}
              alt={course.instructor.name}
              className="w-6 h-6 rounded-full object-cover"
            />
          ) : (
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <Users className="w-3 h-3 text-gray-600 dark:text-gray-400" />
            </div>
          )}
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {course.instructor.name}
          </span>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center space-x-1">
            <Star className="w-4 h-4 text-yellow-500 fill-current" />
            <span>{course.averageRating.toFixed(1)}</span>
            <span>({course.reviewCount})</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{formatDuration(course.estimatedDuration)}</span>
          </div>
        </div>

        {/* Price and Enroll */}
        <div className="flex items-center justify-between">
          <div>
            <div className="font-bold text-lg text-gray-900 dark:text-white">
              {formatPrice(course.price, course.currency)}
            </div>
            {course.originalPrice && course.originalPrice > course.price && (
              <div className="text-sm text-gray-500 line-through">
                {formatPrice(course.originalPrice, course.currency)}
              </div>
            )}
          </div>

          <GlassmorphicButton
            variant="primary"
            size="sm"
            onClick={(e) => {
              e.stopPropagation()
              onEnroll()
            }}
          >
            Enroll
          </GlassmorphicButton>
        </div>

        {/* Recommendation Reason */}
        {course.recommendationReason && (
          <div className="mt-3 text-xs text-violet-600 dark:text-violet-400 bg-violet-100 dark:bg-violet-900/20 px-2 py-1 rounded-full">
            {course.recommendationReason}
          </div>
        )}
      </div>
    </InteractiveGlassCard>
  )
}

// Course List Item Component (placeholder)
function CourseListItem({ course, isFavorite, onToggleFavorite, onSelect, onEnroll }: CourseCardProps) {
  return (
    <GlassmorphicCard
      className="p-4 hover:shadow-lg transition-all duration-200 cursor-pointer"
      onClick={onSelect}
    >
      <div className="flex items-center space-x-4">
        {/* Thumbnail */}
        <div className="w-24 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex-shrink-0 flex items-center justify-center">
          {course.thumbnailImage ? (
            <img
              src={course.thumbnailImage}
              alt={course.title}
              className="w-full h-full object-cover rounded-lg"
            />
          ) : (
            <BookOpen className="w-8 h-8 text-white" />
          )}
        </div>

        {/* Course Info */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 dark:text-white truncate">
            {course.title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {course.instructor.name}
          </p>
          <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
              <span>{course.averageRating.toFixed(1)} ({course.reviewCount})</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{formatDuration(course.estimatedDuration)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4" />
              <span>{course.enrollmentCount} students</span>
            </div>
          </div>
        </div>

        {/* Price and Actions */}
        <div className="flex items-center space-x-4">
          <div className="text-right">
            <div className="font-bold text-lg text-gray-900 dark:text-white">
              {formatPrice(course.price, course.currency)}
            </div>
            {course.originalPrice && course.originalPrice > course.price && (
              <div className="text-sm text-gray-500 line-through">
                {formatPrice(course.originalPrice, course.currency)}
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <IconButton
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onToggleFavorite()
              }}
            >
              <Heart className={`w-4 h-4 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
            </IconButton>

            <GlassmorphicButton
              variant="primary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onEnroll()
              }}
            >
              Enroll
            </GlassmorphicButton>
          </div>
        </div>
      </div>
    </GlassmorphicCard>
  )
}

// Helper function to format duration
function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
  }
  return `${mins}m`
}

// Helper function to format price
function formatPrice(price: number, currency: string) {
  if (price === 0) return 'Free'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(price)
}
