'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Award, 
  Download, 
  Share2, 
  Eye, 
  Calendar,
  User,
  BookOpen,
  ExternalLink,
  Search,
  Filter,
  Trophy,
  Star,
  Copy,
  Check
} from 'lucide-react'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

interface Certificate {
  id: string
  certificateId: string
  issuedAt: string
  pdfUrl?: string
  verificationUrl: string
  course: {
    id: string
    title: string
    thumbnailImage?: string
    instructor: {
      id: string
      name: string
      image?: string
    }
  }
  template: {
    id: string
    name: string
    design: {
      backgroundColor: string
      primaryColor: string
      secondaryColor: string
      layout: string
    }
    content: {
      title: string
    }
  }
  certificateData: {
    studentName: string
    courseName: string
    completionDate: string
  }
}

interface CertificateGalleryProps {
  className?: string
}

export function CertificateGallery({ className = '' }: CertificateGalleryProps) {
  const [certificates, setCertificates] = useState<Certificate[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCertificate, setSelectedCertificate] = useState<Certificate | null>(null)
  const [showShareModal, setShowShareModal] = useState(false)
  const [copiedUrl, setCopiedUrl] = useState('')

  // Fetch certificates
  useEffect(() => {
    const fetchCertificates = async () => {
      try {
        setIsLoading(true)
        const response = await fetch('/api/student/certificates')
        
        if (!response.ok) {
          throw new Error('Failed to fetch certificates')
        }

        const data = await response.json()
        setCertificates(data.certificates)
      } catch (error) {
        console.error('Error fetching certificates:', error)
        toast.error('Failed to load certificates')
      } finally {
        setIsLoading(false)
      }
    }

    fetchCertificates()
  }, [])

  // Filter certificates based on search
  const filteredCertificates = certificates.filter(cert =>
    cert.course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cert.course.instructor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    cert.template.content.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Handle certificate download
  const handleDownload = async (certificate: Certificate) => {
    try {
      if (certificate.pdfUrl) {
        // Create a temporary link to download the PDF
        const link = document.createElement('a')
        link.href = certificate.pdfUrl
        link.download = `${certificate.course.title} - Certificate.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        toast.success('Certificate downloaded successfully!')
      } else {
        toast.error('Certificate PDF not available')
      }
    } catch (error) {
      console.error('Error downloading certificate:', error)
      toast.error('Failed to download certificate')
    }
  }

  // Handle share
  const handleShare = (certificate: Certificate) => {
    setSelectedCertificate(certificate)
    setShowShareModal(true)
  }

  // Copy verification URL
  const copyVerificationUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url)
      setCopiedUrl(url)
      toast.success('Verification URL copied to clipboard!')
      
      setTimeout(() => setCopiedUrl(''), 2000)
    } catch (error) {
      console.error('Error copying URL:', error)
      toast.error('Failed to copy URL')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading certificates...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            My Certificates
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            View and manage your earned certificates
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Trophy className="w-4 h-4 text-yellow-500" />
            <span>{certificates.length} certificates earned</span>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search certificates by course or instructor..."
          className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>

      {/* Certificates Grid */}
      {filteredCertificates.length === 0 ? (
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
          {certificates.length === 0 ? (
            <>
              <Award className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Certificates Yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Complete courses to earn your first certificate
              </p>
            </>
          ) : (
            <>
              <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Matching Certificates
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search terms
              </p>
            </>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {filteredCertificates.map((certificate, index) => (
              <motion.div
                key={certificate.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <CertificateCard
                  certificate={certificate}
                  onDownload={() => handleDownload(certificate)}
                  onShare={() => handleShare(certificate)}
                  onView={() => setSelectedCertificate(certificate)}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Certificate Detail Modal */}
      <AnimatePresence>
        {selectedCertificate && !showShareModal && (
          <CertificateDetailModal
            certificate={selectedCertificate}
            onClose={() => setSelectedCertificate(null)}
            onDownload={() => handleDownload(selectedCertificate)}
            onShare={() => handleShare(selectedCertificate)}
          />
        )}
      </AnimatePresence>

      {/* Share Modal */}
      <AnimatePresence>
        {showShareModal && selectedCertificate && (
          <ShareCertificateModal
            certificate={selectedCertificate}
            onClose={() => {
              setShowShareModal(false)
              setSelectedCertificate(null)
            }}
            onCopyUrl={copyVerificationUrl}
            copiedUrl={copiedUrl}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Certificate Card Component
interface CertificateCardProps {
  certificate: Certificate
  onDownload: () => void
  onShare: () => void
  onView: () => void
}

function CertificateCard({ certificate, onDownload, onShare, onView }: CertificateCardProps) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-200 group">
      {/* Certificate Preview */}
      <div 
        className="h-48 relative cursor-pointer"
        style={{ 
          background: `linear-gradient(135deg, ${certificate.template.design.primaryColor}20, ${certificate.template.design.secondaryColor}20)`
        }}
        onClick={onView}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <Award 
              className="w-12 h-12 mx-auto mb-3"
              style={{ color: certificate.template.design.primaryColor }}
            />
            <h3 
              className="font-bold text-lg"
              style={{ color: certificate.template.design.primaryColor }}
            >
              {certificate.template.content.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {certificate.course.title}
            </p>
          </div>
        </div>

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
          <button
            onClick={(e) => {
              e.stopPropagation()
              onView()
            }}
            className="flex items-center space-x-2 px-4 py-2 bg-white/90 text-gray-900 rounded-lg hover:bg-white transition-colors"
          >
            <Eye className="w-4 h-4" />
            <span>View Certificate</span>
          </button>
        </div>
      </div>

      {/* Certificate Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h4 className="font-semibold text-gray-900 dark:text-white truncate">
              {certificate.course.title}
            </h4>
            <div className="flex items-center space-x-2 mt-1 text-sm text-gray-600 dark:text-gray-400">
              <User className="w-3 h-3" />
              <span>{certificate.course.instructor.name}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
          <div className="flex items-center space-x-1">
            <Calendar className="w-3 h-3" />
            <span>{formatDistanceToNow(new Date(certificate.issuedAt), { addSuffix: true })}</span>
          </div>
          <span className="text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 px-2 py-1 rounded-full">
            Verified
          </span>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <button
            onClick={onDownload}
            className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-violet-100 text-violet-700 hover:bg-violet-200 dark:bg-violet-900/20 dark:text-violet-300 dark:hover:bg-violet-900/30 rounded-lg transition-colors"
          >
            <Download className="w-4 h-4" />
            <span>Download</span>
          </button>

          <button
            onClick={onShare}
            className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors"
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </button>
        </div>
      </div>
    </div>
  )
}

// Certificate Detail Modal (placeholder)
function CertificateDetailModal({ certificate, onClose, onDownload, onShare }: any) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Certificate Details
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-6">
          {/* Certificate Preview */}
          <div 
            className="h-64 rounded-lg flex items-center justify-center"
            style={{ 
              background: `linear-gradient(135deg, ${certificate.template.design.primaryColor}20, ${certificate.template.design.secondaryColor}20)`
            }}
          >
            <div className="text-center">
              <Award 
                className="w-16 h-16 mx-auto mb-4"
                style={{ color: certificate.template.design.primaryColor }}
              />
              <h2 
                className="text-2xl font-bold mb-2"
                style={{ color: certificate.template.design.primaryColor }}
              >
                {certificate.template.content.title}
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-400">
                {certificate.certificateData.studentName}
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                has completed {certificate.course.title}
              </p>
            </div>
          </div>

          {/* Certificate Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Course Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Course:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{certificate.course.title}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Instructor:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{certificate.course.instructor.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Completed:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {new Date(certificate.certificateData.completionDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Certificate Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Certificate ID:</span>
                  <span className="font-mono text-xs text-gray-900 dark:text-white">{certificate.certificateId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Issued:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {new Date(certificate.issuedAt).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className="text-green-600 dark:text-green-400 font-medium">Verified</span>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-4">
            <button
              onClick={onDownload}
              className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
            >
              <Download className="w-4 h-4" />
              <span>Download PDF</span>
            </button>

            <button
              onClick={onShare}
              className="flex items-center space-x-2 px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <Share2 className="w-4 h-4" />
              <span>Share Certificate</span>
            </button>

            <button
              onClick={() => window.open(certificate.verificationUrl, '_blank')}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <ExternalLink className="w-4 h-4" />
              <span>Verify Online</span>
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}

// Share Certificate Modal (placeholder)
function ShareCertificateModal({ certificate, onClose, onCopyUrl, copiedUrl }: any) {
  const verificationUrl = `${window.location.origin}${certificate.verificationUrl}`

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Share Certificate
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            ×
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Verification URL
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={verificationUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
              />
              <button
                onClick={() => onCopyUrl(verificationUrl)}
                className="p-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
              >
                {copiedUrl === verificationUrl ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <Copy className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          <div className="text-sm text-gray-600 dark:text-gray-400">
            Share this URL to allow others to verify your certificate online.
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
