import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse, commonSchemas } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createQuizSchema = z.object({
  title: z.string().min(1, 'Quiz title is required'),
  description: z.string().optional(),
  instructions: z.string().optional(),
  timeLimit: z.number().int().min(1).optional(), // minutes
  passingScore: z.number().int().min(0).max(100).default(70),
  maxAttempts: z.number().int().min(1).default(3),
  isPublished: z.boolean().default(false),
  order: z.number().int().min(0).optional()
})

const querySchema = commonSchemas.pagination.extend({
  published: z.enum(['true', 'false']).optional()
})

// GET /api/instructor/courses/[courseId]/quizzes - Get course quizzes
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateQuery: querySchema
  },
  async (request: NextRequest, { validatedQuery, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]
      const { page = 1, limit = 20, published } = validatedQuery

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Build where clause
      const where: any = {
        courseId
      }

      if (published !== undefined) {
        where.isPublished = published === 'true'
      }

      // Get total count
      const total = await prisma.courseQuiz.count({ where })

      // Get quizzes
      const quizzes = await prisma.courseQuiz.findMany({
        where,
        orderBy: [{ order: 'asc' }, { createdAt: 'desc' }],
        skip: (page - 1) * limit,
        take: limit,
        include: {
          questions: {
            select: {
              id: true,
              type: true,
              points: true
            }
          },
          attempts: {
            select: {
              id: true,
              score: true,
              isPassed: true,
              completedAt: true
            }
          },
          _count: {
            select: {
              questions: true,
              attempts: true
            }
          }
        }
      })

      // Format quizzes with calculated fields
      const formattedQuizzes = quizzes.map(quiz => {
        const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0)
        const averageScore = quiz.attempts.length > 0
          ? quiz.attempts.reduce((sum, a) => sum + a.score, 0) / quiz.attempts.length
          : 0
        const passRate = quiz.attempts.length > 0
          ? (quiz.attempts.filter(a => a.isPassed).length / quiz.attempts.length) * 100
          : 0

        return {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          instructions: quiz.instructions,
          timeLimit: quiz.timeLimit,
          passingScore: quiz.passingScore,
          maxAttempts: quiz.maxAttempts,
          isPublished: quiz.isPublished,
          order: quiz.order,
          createdAt: quiz.createdAt,
          updatedAt: quiz.updatedAt,
          // Calculated fields
          totalQuestions: quiz._count.questions,
          totalPoints,
          totalAttempts: quiz._count.attempts,
          averageScore: Math.round(averageScore * 100) / 100,
          passRate: Math.round(passRate * 100) / 100,
          // Question breakdown
          questionTypes: quiz.questions.reduce((acc, q) => {
            acc[q.type] = (acc[q.type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        }
      })

      return APIResponse.success({
        quizzes: formattedQuizzes,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      })

    } catch (error) {
      console.error('Error fetching course quizzes:', error)
      return APIResponse.error(
        'Failed to fetch quizzes: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/instructor/courses/[courseId]/quizzes - Create new quiz
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: createQuizSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const quizData = validatedBody

      // If no order specified, add to end
      if (quizData.order === undefined) {
        const lastQuiz = await prisma.courseQuiz.findFirst({
          where: { courseId },
          orderBy: { order: 'desc' }
        })
        quizData.order = (lastQuiz?.order || 0) + 1
      }

      // Create quiz
      const quiz = await prisma.courseQuiz.create({
        data: {
          ...quizData,
          courseId
        },
        include: {
          _count: {
            select: {
              questions: true,
              attempts: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Quiz created successfully',
        quiz: {
          ...quiz,
          totalQuestions: quiz._count.questions,
          totalAttempts: quiz._count.attempts,
          averageScore: 0,
          passRate: 0,
          questionTypes: {}
        }
      })

    } catch (error) {
      console.error('Error creating quiz:', error)
      return APIResponse.error(
        'Failed to create quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId]/quizzes - Reorder quizzes
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: z.object({
      quizzes: z.array(z.object({
        id: z.string(),
        order: z.number().int().min(0)
      }))
    })
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      const { quizzes } = validatedBody

      // Update quiz orders in a transaction
      await prisma.$transaction(
        quizzes.map(quiz =>
          prisma.courseQuiz.update({
            where: { 
              id: quiz.id,
              courseId // Ensure quiz belongs to this course
            },
            data: { order: quiz.order }
          })
        )
      )

      return APIResponse.success({
        message: 'Quizzes reordered successfully'
      })

    } catch (error) {
      console.error('Error reordering quizzes:', error)
      return APIResponse.error(
        'Failed to reorder quizzes: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
