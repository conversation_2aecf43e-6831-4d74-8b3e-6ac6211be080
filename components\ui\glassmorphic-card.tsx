'use client'

import React, { forwardRef } from 'react'
import { motion, HTMLMotionProps } from 'framer-motion'
import { cn } from '@/lib/utils'

interface GlassmorphicCardProps extends HTMLMotionProps<'div'> {
  variant?: 'light' | 'medium' | 'heavy' | 'dark-light' | 'dark-medium' | 'dark-heavy'
  hover?: boolean
  glow?: boolean
  gradient?: boolean
  children: React.ReactNode
  className?: string
}

const glassVariants = {
  light: {
    background: 'rgba(255, 255, 255, 0.05)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(8px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
  },
  medium: {
    background: 'rgba(255, 255, 255, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.15)',
    backdropFilter: 'blur(16px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)'
  },
  heavy: {
    background: 'rgba(255, 255, 255, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    backdropFilter: 'blur(24px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.2)'
  },
  'dark-light': {
    background: 'rgba(0, 0, 0, 0.05)',
    border: '1px solid rgba(255, 255, 255, 0.05)',
    backdropFilter: 'blur(8px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
  },
  'dark-medium': {
    background: 'rgba(0, 0, 0, 0.1)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(16px)',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4)'
  },
  'dark-heavy': {
    background: 'rgba(0, 0, 0, 0.15)',
    border: '1px solid rgba(255, 255, 255, 0.15)',
    backdropFilter: 'blur(24px)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.5)'
  }
}

export const GlassmorphicCard = forwardRef<HTMLDivElement, GlassmorphicCardProps>(
  ({ 
    variant = 'medium', 
    hover = true, 
    glow = false, 
    gradient = false,
    children, 
    className, 
    ...props 
  }, ref) => {
    const glassStyle = glassVariants[variant]
    
    return (
      <motion.div
        ref={ref}
        className={cn(
          'relative rounded-2xl overflow-hidden',
          hover && 'transition-all duration-300 ease-out',
          glow && 'before:absolute before:inset-0 before:rounded-2xl before:p-[1px] before:bg-gradient-to-r before:from-violet-500/20 before:to-purple-500/20 before:-z-10',
          gradient && 'bg-gradient-to-br from-white/10 to-white/5',
          className
        )}
        style={{
          ...glassStyle,
          ...(gradient && {
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))'
          })
        }}
        whileHover={hover ? {
          y: -4,
          scale: 1.02,
          boxShadow: variant.includes('dark') 
            ? '0 16px 48px rgba(0, 0, 0, 0.6)' 
            : '0 16px 48px rgba(0, 0, 0, 0.25)'
        } : undefined}
        whileTap={hover ? { scale: 0.98 } : undefined}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30
        }}
        {...props}
      >
        {/* Gradient overlay for enhanced glass effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none" />
        
        {/* Glow effect */}
        {glow && (
          <div className="absolute -inset-0.5 bg-gradient-to-r from-violet-500 to-purple-500 rounded-2xl blur opacity-20 group-hover:opacity-40 transition duration-300" />
        )}
        
        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>
      </motion.div>
    )
  }
)

GlassmorphicCard.displayName = 'GlassmorphicCard'

// Specialized card variants
export const GlassCard = ({ children, className, ...props }: Omit<GlassmorphicCardProps, 'variant'>) => (
  <GlassmorphicCard variant="medium" className={className} {...props}>
    {children}
  </GlassmorphicCard>
)

export const GlassCardLight = ({ children, className, ...props }: Omit<GlassmorphicCardProps, 'variant'>) => (
  <GlassmorphicCard variant="light" className={className} {...props}>
    {children}
  </GlassmorphicCard>
)

export const GlassCardHeavy = ({ children, className, ...props }: Omit<GlassmorphicCardProps, 'variant'>) => (
  <GlassmorphicCard variant="heavy" className={className} {...props}>
    {children}
  </GlassmorphicCard>
)

export const GlassCardDark = ({ children, className, ...props }: Omit<GlassmorphicCardProps, 'variant'>) => (
  <GlassmorphicCard variant="dark-medium" className={className} {...props}>
    {children}
  </GlassmorphicCard>
)

// Interactive glass card with enhanced effects
export const InteractiveGlassCard = ({ 
  children, 
  className, 
  onClick,
  ...props 
}: GlassmorphicCardProps & { onClick?: () => void }) => (
  <GlassmorphicCard
    variant="medium"
    hover={true}
    glow={true}
    className={cn(
      'cursor-pointer group',
      'hover:shadow-2xl hover:shadow-violet-500/25',
      'active:scale-95',
      'transition-all duration-300 ease-out',
      className
    )}
    onClick={onClick}
    whileHover={{
      y: -6,
      scale: 1.03,
      boxShadow: '0 20px 60px rgba(99, 102, 241, 0.3)'
    }}
    whileTap={{ scale: 0.97 }}
    {...props}
  >
    {/* Enhanced glow effect */}
    <div className="absolute -inset-1 bg-gradient-to-r from-violet-500 via-purple-500 to-pink-500 rounded-2xl blur-sm opacity-0 group-hover:opacity-30 transition duration-500" />
    
    {/* Shimmer effect */}
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
    
    <div className="relative z-10">
      {children}
    </div>
  </GlassmorphicCard>
)

// Floating glass card with advanced animations
export const FloatingGlassCard = ({ 
  children, 
  className,
  delay = 0,
  ...props 
}: GlassmorphicCardProps & { delay?: number }) => (
  <motion.div
    initial={{ opacity: 0, y: 20, scale: 0.9 }}
    animate={{ opacity: 1, y: 0, scale: 1 }}
    transition={{
      type: 'spring',
      stiffness: 200,
      damping: 20,
      delay
    }}
  >
    <GlassmorphicCard
      variant="medium"
      hover={true}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      whileHover={{
        y: -8,
        rotateX: 5,
        rotateY: 5,
        scale: 1.05
      }}
      style={{
        transformStyle: 'preserve-3d',
        perspective: 1000
      }}
      {...props}
    >
      {/* Floating animation */}
      <motion.div
        animate={{
          y: [0, -10, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
        className="relative z-10"
      >
        {children}
      </motion.div>
      
      {/* Ambient light effect */}
      <div className="absolute inset-0 bg-gradient-radial from-violet-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition duration-500" />
    </GlassmorphicCard>
  </motion.div>
)

// Glass navigation card
export const GlassNavCard = ({ 
  children, 
  className, 
  active = false,
  ...props 
}: GlassmorphicCardProps & { active?: boolean }) => (
  <GlassmorphicCard
    variant={active ? 'heavy' : 'light'}
    hover={true}
    className={cn(
      'transition-all duration-300 ease-out',
      active && 'bg-gradient-to-r from-violet-500/20 to-purple-500/20',
      active && 'border-violet-500/30',
      active && 'shadow-lg shadow-violet-500/25',
      !active && 'hover:bg-white/10',
      className
    )}
    whileHover={!active ? {
      scale: 1.05,
      backgroundColor: 'rgba(255, 255, 255, 0.15)'
    } : undefined}
    {...props}
  >
    {active && (
      <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-purple-500/10 rounded-2xl" />
    )}
    <div className="relative z-10">
      {children}
    </div>
  </GlassmorphicCard>
)

// Glass modal/dialog
export const GlassModal = ({ 
  children, 
  className,
  isOpen = false,
  onClose,
  ...props 
}: GlassmorphicCardProps & { 
  isOpen?: boolean
  onClose?: () => void 
}) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: isOpen ? 1 : 0 }}
    className={cn(
      'fixed inset-0 z-50 flex items-center justify-center p-4',
      'bg-black/50 backdrop-blur-sm',
      !isOpen && 'pointer-events-none'
    )}
    onClick={onClose}
  >
    <GlassmorphicCard
      variant="heavy"
      className={cn(
        'max-w-lg w-full max-h-[90vh] overflow-y-auto',
        className
      )}
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ 
        scale: isOpen ? 1 : 0.9, 
        opacity: isOpen ? 1 : 0 
      }}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30
      }}
      onClick={(e) => e.stopPropagation()}
      {...props}
    >
      {children}
    </GlassmorphicCard>
  </motion.div>
)
