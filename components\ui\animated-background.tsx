'use client'

import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface AnimatedBackgroundProps {
  variant?: 'gradient' | 'particles' | 'waves' | 'mesh' | 'aurora'
  intensity?: 'low' | 'medium' | 'high'
  speed?: 'slow' | 'medium' | 'fast'
  className?: string
  children?: React.ReactNode
}

export function AnimatedBackground({
  variant = 'gradient',
  intensity = 'medium',
  speed = 'medium',
  className,
  children
}: AnimatedBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    if (variant === 'particles' && canvasRef.current) {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      const resizeCanvas = () => {
        canvas.width = window.innerWidth
        canvas.height = window.innerHeight
      }

      resizeCanvas()
      window.addEventListener('resize', resizeCanvas)

      // Particle system
      const particles: Array<{
        x: number
        y: number
        vx: number
        vy: number
        size: number
        opacity: number
        color: string
      }> = []

      const particleCount = intensity === 'low' ? 50 : intensity === 'medium' ? 100 : 150
      const speedMultiplier = speed === 'slow' ? 0.5 : speed === 'medium' ? 1 : 2

      // Initialize particles
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * speedMultiplier,
          vy: (Math.random() - 0.5) * speedMultiplier,
          size: Math.random() * 3 + 1,
          opacity: Math.random() * 0.5 + 0.1,
          color: `hsl(${Math.random() * 60 + 240}, 70%, 60%)`
        })
      }

      const animate = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        particles.forEach((particle, index) => {
          // Update position
          particle.x += particle.vx
          particle.y += particle.vy

          // Wrap around edges
          if (particle.x < 0) particle.x = canvas.width
          if (particle.x > canvas.width) particle.x = 0
          if (particle.y < 0) particle.y = canvas.height
          if (particle.y > canvas.height) particle.y = 0

          // Draw particle
          ctx.beginPath()
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2)
          ctx.fillStyle = particle.color
          ctx.globalAlpha = particle.opacity
          ctx.fill()

          // Draw connections
          particles.slice(index + 1).forEach(otherParticle => {
            const dx = particle.x - otherParticle.x
            const dy = particle.y - otherParticle.y
            const distance = Math.sqrt(dx * dx + dy * dy)

            if (distance < 100) {
              ctx.beginPath()
              ctx.moveTo(particle.x, particle.y)
              ctx.lineTo(otherParticle.x, otherParticle.y)
              ctx.strokeStyle = particle.color
              ctx.globalAlpha = (100 - distance) / 100 * 0.2
              ctx.lineWidth = 1
              ctx.stroke()
            }
          })
        })

        requestAnimationFrame(animate)
      }

      animate()

      return () => {
        window.removeEventListener('resize', resizeCanvas)
      }
    }
  }, [variant, intensity, speed])

  if (variant === 'gradient') {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        {/* Animated gradient background */}
        <div className="absolute inset-0">
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-violet-500/20 via-purple-500/20 to-pink-500/20"
            animate={{
              background: [
                'linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2))',
                'linear-gradient(225deg, rgba(236, 72, 153, 0.2), rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2))',
                'linear-gradient(315deg, rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2), rgba(139, 92, 246, 0.2))',
                'linear-gradient(45deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.2), rgba(236, 72, 153, 0.2))'
              ]
            }}
            transition={{
              duration: speed === 'slow' ? 20 : speed === 'medium' ? 15 : 10,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
          
          {/* Floating orbs */}
          {Array.from({ length: intensity === 'low' ? 3 : intensity === 'medium' ? 5 : 8 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-gradient-to-r from-violet-500/30 to-purple-500/30 blur-xl"
              style={{
                width: Math.random() * 200 + 100,
                height: Math.random() * 200 + 100,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                x: [0, Math.random() * 100 - 50, 0],
                y: [0, Math.random() * 100 - 50, 0],
                scale: [1, Math.random() * 0.5 + 0.8, 1],
                opacity: [0.3, 0.6, 0.3]
              }}
              transition={{
                duration: Math.random() * 10 + 10,
                repeat: Infinity,
                ease: 'easeInOut',
                delay: Math.random() * 5
              }}
            />
          ))}
        </div>
        
        {children && <div className="relative z-10">{children}</div>}
      </div>
    )
  }

  if (variant === 'particles') {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <canvas
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none"
          style={{ background: 'transparent' }}
        />
        {children && <div className="relative z-10">{children}</div>}
      </div>
    )
  }

  if (variant === 'waves') {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <div className="absolute inset-0">
          {Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0 opacity-20"
              style={{
                background: `radial-gradient(ellipse at center, transparent 0%, rgba(139, 92, 246, 0.${3 + i}) 50%, transparent 100%)`
              }}
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360]
              }}
              transition={{
                duration: speed === 'slow' ? 20 : speed === 'medium' ? 15 : 10,
                repeat: Infinity,
                ease: 'linear',
                delay: i * 2
              }}
            />
          ))}
        </div>
        {children && <div className="relative z-10">{children}</div>}
      </div>
    )
  }

  if (variant === 'mesh') {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <div className="absolute inset-0">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="meshGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="rgba(139, 92, 246, 0.3)" />
                <stop offset="50%" stopColor="rgba(168, 85, 247, 0.3)" />
                <stop offset="100%" stopColor="rgba(236, 72, 153, 0.3)" />
              </linearGradient>
            </defs>
            
            {/* Animated mesh pattern */}
            {Array.from({ length: 10 }).map((_, i) => (
              <motion.path
                key={i}
                d={`M${i * 10},0 Q${i * 10 + 5},50 ${i * 10},100`}
                stroke="url(#meshGradient)"
                strokeWidth="0.5"
                fill="none"
                opacity="0.6"
                animate={{
                  d: [
                    `M${i * 10},0 Q${i * 10 + 5},50 ${i * 10},100`,
                    `M${i * 10},0 Q${i * 10 - 5},50 ${i * 10},100`,
                    `M${i * 10},0 Q${i * 10 + 5},50 ${i * 10},100`
                  ]
                }}
                transition={{
                  duration: speed === 'slow' ? 8 : speed === 'medium' ? 6 : 4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: i * 0.2
                }}
              />
            ))}
          </svg>
        </div>
        {children && <div className="relative z-10">{children}</div>}
      </div>
    )
  }

  if (variant === 'aurora') {
    return (
      <div className={cn('relative overflow-hidden', className)}>
        <div className="absolute inset-0">
          {/* Aurora layers */}
          {Array.from({ length: intensity === 'low' ? 2 : intensity === 'medium' ? 3 : 4 }).map((_, i) => (
            <motion.div
              key={i}
              className="absolute inset-0"
              style={{
                background: `linear-gradient(${45 + i * 30}deg, 
                  transparent 0%, 
                  rgba(139, 92, 246, ${0.1 + i * 0.05}) 30%, 
                  rgba(168, 85, 247, ${0.15 + i * 0.05}) 50%, 
                  rgba(236, 72, 153, ${0.1 + i * 0.05}) 70%, 
                  transparent 100%)`
              }}
              animate={{
                background: [
                  `linear-gradient(${45 + i * 30}deg, transparent 0%, rgba(139, 92, 246, ${0.1 + i * 0.05}) 30%, rgba(168, 85, 247, ${0.15 + i * 0.05}) 50%, rgba(236, 72, 153, ${0.1 + i * 0.05}) 70%, transparent 100%)`,
                  `linear-gradient(${135 + i * 30}deg, transparent 0%, rgba(236, 72, 153, ${0.1 + i * 0.05}) 30%, rgba(139, 92, 246, ${0.15 + i * 0.05}) 50%, rgba(168, 85, 247, ${0.1 + i * 0.05}) 70%, transparent 100%)`,
                  `linear-gradient(${225 + i * 30}deg, transparent 0%, rgba(168, 85, 247, ${0.1 + i * 0.05}) 30%, rgba(236, 72, 153, ${0.15 + i * 0.05}) 50%, rgba(139, 92, 246, ${0.1 + i * 0.05}) 70%, transparent 100%)`,
                  `linear-gradient(${315 + i * 30}deg, transparent 0%, rgba(139, 92, 246, ${0.1 + i * 0.05}) 30%, rgba(168, 85, 247, ${0.15 + i * 0.05}) 50%, rgba(236, 72, 153, ${0.1 + i * 0.05}) 70%, transparent 100%)`
                ]
              }}
              transition={{
                duration: speed === 'slow' ? 25 : speed === 'medium' ? 20 : 15,
                repeat: Infinity,
                ease: 'linear',
                delay: i * 3
              }}
            />
          ))}
          
          {/* Shimmer effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: speed === 'slow' ? 4 : speed === 'medium' ? 3 : 2,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
        </div>
        {children && <div className="relative z-10">{children}</div>}
      </div>
    )
  }

  return (
    <div className={cn('relative', className)}>
      {children}
    </div>
  )
}

// Floating Elements Component
export function FloatingElements({
  count = 6,
  className
}: {
  count?: number
  className?: string
}) {
  return (
    <div className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}>
      {Array.from({ length: count }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-2 h-2 bg-violet-500/30 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, Math.random() * 20 - 10, 0],
            opacity: [0.3, 0.8, 0.3],
            scale: [1, 1.5, 1]
          }}
          transition={{
            duration: Math.random() * 3 + 4,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: Math.random() * 2
          }}
        />
      ))}
    </div>
  )
}

// Gradient Text Component
export function GradientText({
  children,
  className,
  gradient = 'from-violet-500 to-purple-600'
}: {
  children: React.ReactNode
  className?: string
  gradient?: string
}) {
  return (
    <span className={cn(
      'bg-gradient-to-r bg-clip-text text-transparent',
      gradient,
      className
    )}>
      {children}
    </span>
  )
}
