'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence, Reorder } from 'framer-motion'
import { 
  Plus, 
  GripVertical, 
  Edit3, 
  Trash2, 
  Save, 
  Eye, 
  EyeOff,
  Clock,
  Users,
  BarChart3,
  CheckCircle,
  XCircle,
  HelpCircle,
  FileText,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'

interface Quiz {
  id: string
  title: string
  description?: string
  instructions?: string
  timeLimit?: number
  passingScore: number
  maxAttempts: number
  isPublished: boolean
  order: number
  totalQuestions: number
  totalPoints: number
  totalAttempts: number
  averageScore: number
  passRate: number
  questionTypes: Record<string, number>
}

interface Question {
  id: string
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY'
  question: string
  options?: string[]
  correctAnswer: string
  explanation?: string
  points: number
  order: number
}

interface QuizBuilderProps {
  courseId: string
  onQuizUpdate?: (quiz: Quiz) => void
}

export function QuizBuilder({ courseId, onQuizUpdate }: QuizBuilderProps) {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null)
  const [questions, setQuestions] = useState<Question[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isCreatingQuiz, setIsCreatingQuiz] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null)

  // Fetch quizzes
  useEffect(() => {
    const fetchQuizzes = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/instructor/courses/${courseId}/quizzes`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch quizzes')
        }

        const data = await response.json()
        setQuizzes(data.quizzes)
      } catch (error) {
        console.error('Error fetching quizzes:', error)
        toast.error('Failed to load quizzes')
      } finally {
        setIsLoading(false)
      }
    }

    if (courseId) {
      fetchQuizzes()
    }
  }, [courseId])

  // Fetch questions when quiz is selected
  useEffect(() => {
    const fetchQuestions = async () => {
      if (!selectedQuiz) {
        setQuestions([])
        return
      }

      try {
        const response = await fetch(`/api/instructor/courses/${courseId}/quizzes/${selectedQuiz.id}/questions`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch questions')
        }

        const data = await response.json()
        setQuestions(data.questions)
      } catch (error) {
        console.error('Error fetching questions:', error)
        toast.error('Failed to load questions')
      }
    }

    fetchQuestions()
  }, [selectedQuiz, courseId])

  // Create new quiz
  const createQuiz = async () => {
    try {
      setIsCreatingQuiz(true)
      const response = await fetch(`/api/instructor/courses/${courseId}/quizzes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: 'New Quiz',
          description: '',
          passingScore: 70,
          maxAttempts: 3,
          isPublished: false
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create quiz')
      }

      const data = await response.json()
      const newQuiz = data.quiz

      setQuizzes(prev => [...prev, newQuiz])
      setSelectedQuiz(newQuiz)
      toast.success('Quiz created successfully!')
    } catch (error) {
      console.error('Error creating quiz:', error)
      toast.error('Failed to create quiz')
    } finally {
      setIsCreatingQuiz(false)
    }
  }

  // Update quiz
  const updateQuiz = async (quizId: string, updates: Partial<Quiz>) => {
    try {
      const response = await fetch(`/api/instructor/courses/${courseId}/quizzes/${quizId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error('Failed to update quiz')
      }

      const data = await response.json()
      const updatedQuiz = data.quiz

      setQuizzes(prev => prev.map(q => q.id === quizId ? updatedQuiz : q))
      if (selectedQuiz?.id === quizId) {
        setSelectedQuiz(updatedQuiz)
      }
      
      onQuizUpdate?.(updatedQuiz)
      toast.success('Quiz updated successfully!')
    } catch (error) {
      console.error('Error updating quiz:', error)
      toast.error('Failed to update quiz')
    }
  }

  // Delete quiz
  const deleteQuiz = async (quizId: string) => {
    if (!confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/instructor/courses/${courseId}/quizzes/${quizId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete quiz')
      }

      setQuizzes(prev => prev.filter(q => q.id !== quizId))
      if (selectedQuiz?.id === quizId) {
        setSelectedQuiz(null)
      }
      
      toast.success('Quiz deleted successfully!')
    } catch (error) {
      console.error('Error deleting quiz:', error)
      toast.error('Failed to delete quiz')
    }
  }

  // Add question
  const addQuestion = async (type: Question['type']) => {
    if (!selectedQuiz) return

    try {
      const questionData = {
        type,
        question: 'New question',
        correctAnswer: type === 'TRUE_FALSE' ? 'True' : 'Answer',
        points: 1,
        ...(type === 'MULTIPLE_CHOICE' && {
          options: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
          correctAnswer: 'Option 1'
        }),
        ...(type === 'TRUE_FALSE' && {
          options: ['True', 'False']
        })
      }

      const response = await fetch(`/api/instructor/courses/${courseId}/quizzes/${selectedQuiz.id}/questions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(questionData)
      })

      if (!response.ok) {
        throw new Error('Failed to create question')
      }

      const data = await response.json()
      const newQuestion = data.question

      setQuestions(prev => [...prev, newQuestion])
      setEditingQuestion(newQuestion.id)
      toast.success('Question added successfully!')
    } catch (error) {
      console.error('Error adding question:', error)
      toast.error('Failed to add question')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading quizzes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Quiz & Assessment Builder
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Create and manage quizzes to test student knowledge
          </p>
        </div>

        <button
          onClick={createQuiz}
          disabled={isCreatingQuiz}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
        >
          <Plus className="w-4 h-4" />
          <span>{isCreatingQuiz ? 'Creating...' : 'Create Quiz'}</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quiz List */}
        <div className="lg:col-span-1">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Course Quizzes ({quizzes.length})
              </h3>
            </div>

            <div className="p-4">
              {quizzes.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    No Quizzes Yet
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Create your first quiz to start testing student knowledge
                  </p>
                  <button
                    onClick={createQuiz}
                    disabled={isCreatingQuiz}
                    className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 disabled:opacity-50 transition-colors"
                  >
                    Create First Quiz
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  {quizzes.map((quiz) => (
                    <motion.button
                      key={quiz.id}
                      onClick={() => setSelectedQuiz(quiz)}
                      className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                        selectedQuiz?.id === quiz.id
                          ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{quiz.title}</div>
                          <div className={`text-sm mt-1 flex items-center space-x-3 ${
                            selectedQuiz?.id === quiz.id 
                              ? 'text-white/80' 
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span>{quiz.totalQuestions} questions</span>
                            <span>•</span>
                            <span>{quiz.totalAttempts} attempts</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 ml-2">
                          {quiz.isPublished ? (
                            <Eye className="w-4 h-4 text-green-500" />
                          ) : (
                            <EyeOff className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quiz Editor */}
        <div className="lg:col-span-2">
          {selectedQuiz ? (
            <QuizEditor
              quiz={selectedQuiz}
              questions={questions}
              courseId={courseId}
              onQuizUpdate={(updates) => updateQuiz(selectedQuiz.id, updates)}
              onQuizDelete={() => deleteQuiz(selectedQuiz.id)}
              onAddQuestion={addQuestion}
              editingQuestion={editingQuestion}
              setEditingQuestion={setEditingQuestion}
              setQuestions={setQuestions}
            />
          ) : (
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
              <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Select a Quiz to Edit
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Choose a quiz from the list to start editing questions and settings
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Quiz Editor Component (to be implemented separately due to length)
interface QuizEditorProps {
  quiz: Quiz
  questions: Question[]
  courseId: string
  onQuizUpdate: (updates: Partial<Quiz>) => void
  onQuizDelete: () => void
  onAddQuestion: (type: Question['type']) => void
  editingQuestion: string | null
  setEditingQuestion: (id: string | null) => void
  setQuestions: React.Dispatch<React.SetStateAction<Question[]>>
}

function QuizEditor({ 
  quiz, 
  questions, 
  courseId,
  onQuizUpdate, 
  onQuizDelete, 
  onAddQuestion,
  editingQuestion,
  setEditingQuestion,
  setQuestions
}: QuizEditorProps) {
  const [activeTab, setActiveTab] = useState<'questions' | 'settings' | 'analytics'>('questions')

  const questionTypes = [
    { type: 'MULTIPLE_CHOICE' as const, label: 'Multiple Choice', icon: CheckCircle },
    { type: 'TRUE_FALSE' as const, label: 'True/False', icon: XCircle },
    { type: 'SHORT_ANSWER' as const, label: 'Short Answer', icon: FileText },
    { type: 'ESSAY' as const, label: 'Essay', icon: HelpCircle }
  ]

  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700">
      {/* Quiz Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              {quiz.title}
            </h3>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <FileText className="w-4 h-4" />
                <span>{quiz.totalQuestions} questions</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>{quiz.totalAttempts} attempts</span>
              </div>
              <div className="flex items-center space-x-1">
                <BarChart3 className="w-4 h-4" />
                <span>{quiz.averageScore}% avg score</span>
              </div>
              {quiz.timeLimit && (
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{quiz.timeLimit} min limit</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => onQuizUpdate({ isPublished: !quiz.isPublished })}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                quiz.isPublished
                  ? 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
              }`}
            >
              {quiz.isPublished ? 'Unpublish' : 'Publish'}
            </button>

            <button
              onClick={onQuizDelete}
              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mt-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {[
            { id: 'questions', label: 'Questions' },
            { id: 'settings', label: 'Settings' },
            { id: 'analytics', label: 'Analytics' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'questions' && (
            <motion.div
              key="questions"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Add Question Buttons */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {questionTypes.map((type) => {
                  const Icon = type.icon
                  return (
                    <button
                      key={type.type}
                      onClick={() => onAddQuestion(type.type)}
                      className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg hover:border-violet-400 hover:bg-violet-50 dark:hover:bg-violet-900/20 transition-colors"
                    >
                      <Icon className="w-6 h-6 text-violet-500 mb-2" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {type.label}
                      </span>
                    </button>
                  )
                })}
              </div>

              {/* Questions List */}
              {questions.length === 0 ? (
                <div className="text-center py-12">
                  <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    No Questions Yet
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    Add your first question using the buttons above
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {questions.map((question, index) => (
                    <QuestionCard
                      key={question.id}
                      question={question}
                      index={index}
                      isEditing={editingQuestion === question.id}
                      onEdit={() => setEditingQuestion(question.id)}
                      onSave={() => setEditingQuestion(null)}
                      onCancel={() => setEditingQuestion(null)}
                      courseId={courseId}
                      quizId={quiz.id}
                      setQuestions={setQuestions}
                    />
                  ))}
                </div>
              )}
            </motion.div>
          )}

          {activeTab === 'settings' && (
            <motion.div
              key="settings"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <QuizSettings quiz={quiz} onUpdate={onQuizUpdate} />
            </motion.div>
          )}

          {activeTab === 'analytics' && (
            <motion.div
              key="analytics"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <QuizAnalytics quiz={quiz} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// Placeholder components (to be implemented)
function QuestionCard({ question, index, isEditing, onEdit, onSave, onCancel }: any) {
  return (
    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="font-medium text-gray-900 dark:text-white">
            {index + 1}. {question.question}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {question.type} • {question.points} points
          </div>
        </div>
        <button
          onClick={onEdit}
          className="p-2 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
        >
          <Edit3 className="w-4 h-4" />
        </button>
      </div>
    </div>
  )
}

interface QuizSettingsProps {
  quiz: Quiz
  onUpdate: (updates: Partial<Quiz>) => void
}

function QuizSettings({ quiz, onUpdate }: QuizSettingsProps) {
  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-gray-900 dark:text-white">Quiz Settings</h4>
      <p className="text-gray-600 dark:text-gray-400">Configure quiz behavior and requirements</p>
      {/* Settings form to be implemented */}
    </div>
  )
}

interface QuizAnalyticsProps {
  quiz: Quiz
}

function QuizAnalytics({ quiz }: QuizAnalyticsProps) {
  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-gray-900 dark:text-white">Quiz Analytics</h4>
      <p className="text-gray-600 dark:text-gray-400">View detailed quiz performance metrics</p>
      {/* Analytics dashboard to be implemented */}
    </div>
  )
}
