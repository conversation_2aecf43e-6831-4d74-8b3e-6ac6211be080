import { NextRequest } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateQuizSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  instructions: z.string().optional(),
  timeLimit: z.number().int().min(1).optional(),
  passingScore: z.number().int().min(0).max(100).optional(),
  maxAttempts: z.number().int().min(1).optional(),
  isPublished: z.boolean().optional(),
  order: z.number().int().min(0).optional()
})

// GET /api/instructor/courses/[courseId]/quizzes/[quizId] - Get quiz details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get quiz with full details
      const quiz = await prisma.courseQuiz.findUnique({
        where: { 
          id: quizId,
          courseId
        },
        include: {
          questions: {
            orderBy: { order: 'asc' },
            select: {
              id: true,
              type: true,
              question: true,
              options: true,
              correctAnswer: true,
              explanation: true,
              points: true,
              order: true,
              createdAt: true,
              updatedAt: true
            }
          },
          attempts: {
            take: 10,
            orderBy: { startedAt: 'desc' },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  image: true
                }
              }
            }
          },
          _count: {
            select: {
              questions: true,
              attempts: true
            }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      // Calculate quiz statistics
      const totalPoints = quiz.questions.reduce((sum, q) => sum + q.points, 0)
      const averageScore = quiz.attempts.length > 0
        ? quiz.attempts.reduce((sum, a) => sum + a.score, 0) / quiz.attempts.length
        : 0
      const passRate = quiz.attempts.length > 0
        ? (quiz.attempts.filter(a => a.isPassed).length / quiz.attempts.length) * 100
        : 0

      // Question type breakdown
      const questionTypes = quiz.questions.reduce((acc, q) => {
        acc[q.type] = (acc[q.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      // Recent attempts with user info
      const recentAttempts = quiz.attempts.map(attempt => ({
        id: attempt.id,
        score: attempt.score,
        totalQuestions: attempt.totalQuestions,
        correctAnswers: attempt.correctAnswers,
        timeSpent: attempt.timeSpent,
        isPassed: attempt.isPassed,
        startedAt: attempt.startedAt,
        completedAt: attempt.completedAt,
        user: attempt.user
      }))

      return APIResponse.success({
        quiz: {
          ...quiz,
          // Remove sensitive data from questions for instructor view
          questions: quiz.questions.map(q => ({
            ...q,
            // Keep correct answers visible for instructors
          })),
          // Statistics
          totalQuestions: quiz._count.questions,
          totalPoints,
          totalAttempts: quiz._count.attempts,
          averageScore: Math.round(averageScore * 100) / 100,
          passRate: Math.round(passRate * 100) / 100,
          questionTypes,
          recentAttempts
        }
      })

    } catch (error) {
      console.error('Error fetching quiz:', error)
      return APIResponse.error(
        'Failed to fetch quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId]/quizzes/[quizId] - Update quiz
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: updateQuizSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if quiz exists
      const existingQuiz = await prisma.courseQuiz.findUnique({
        where: { 
          id: quizId,
          courseId
        }
      })

      if (!existingQuiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      // Update quiz
      const updatedQuiz = await prisma.courseQuiz.update({
        where: { id: quizId },
        data: validatedBody,
        include: {
          _count: {
            select: {
              questions: true,
              attempts: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Quiz updated successfully',
        quiz: {
          ...updatedQuiz,
          totalQuestions: updatedQuiz._count.questions,
          totalAttempts: updatedQuiz._count.attempts
        }
      })

    } catch (error) {
      console.error('Error updating quiz:', error)
      return APIResponse.error(
        'Failed to update quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/instructor/courses/[courseId]/quizzes/[quizId] - Delete quiz
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const quizId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !quizId) {
        return APIResponse.error('Course ID and Quiz ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if quiz exists and get attempt count
      const quiz = await prisma.courseQuiz.findUnique({
        where: { 
          id: quizId,
          courseId
        },
        include: {
          _count: {
            select: { attempts: true }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      // Prevent deletion if quiz has attempts
      if (quiz._count.attempts > 0) {
        return APIResponse.error(
          'Cannot delete quiz with student attempts. Unpublish it instead.',
          400
        )
      }

      // Delete quiz (cascade will handle questions)
      await prisma.courseQuiz.delete({
        where: { id: quizId }
      })

      // Reorder remaining quizzes
      const remainingQuizzes = await prisma.courseQuiz.findMany({
        where: { courseId },
        orderBy: { order: 'asc' }
      })

      await prisma.$transaction(
        remainingQuizzes.map((quiz, index) =>
          prisma.courseQuiz.update({
            where: { id: quiz.id },
            data: { order: index + 1 }
          })
        )
      )

      return APIResponse.success({
        message: 'Quiz deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting quiz:', error)
      return APIResponse.error(
        'Failed to delete quiz: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
