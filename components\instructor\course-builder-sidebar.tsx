'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { LucideIcon, CheckCircle, AlertCircle, Clock } from 'lucide-react'

interface Tab {
  id: string
  label: string
  icon: LucideIcon
  description: string
}

interface Course {
  id: string
  title: string
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED' | 'PRIVATE'
  sections: any[]
  totalLessons: number
  totalDuration: number
  enrollmentCount: number
}

interface CourseBuilderSidebarProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  course: Course
}

export function CourseBuilderSidebar({
  tabs,
  activeTab,
  onTabChange,
  course
}: CourseBuilderSidebarProps) {
  
  // Calculate course completion status
  const getCourseCompletionStatus = () => {
    const hasContent = course.sections.length > 0
    const hasTitle = course.title.trim().length > 0
    const hasDescription = course.sections.some(section => 
      section.topics && section.topics.length > 0
    )
    
    const completionItems = [
      { label: 'Course title', completed: hasTitle },
      { label: 'Course content', completed: hasContent },
      { label: 'Course lessons', completed: hasDescription },
      { label: 'Course published', completed: course.status === 'PUBLISHED' }
    ]

    const completedCount = completionItems.filter(item => item.completed).length
    const completionPercentage = (completedCount / completionItems.length) * 100

    return {
      items: completionItems,
      percentage: completionPercentage,
      isComplete: completionPercentage === 100
    }
  }

  const completionStatus = getCourseCompletionStatus()

  return (
    <div className="space-y-6">
      {/* Course Overview Card */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              Course Overview
            </h3>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className={`font-medium ${
                  course.status === 'PUBLISHED' 
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-yellow-600 dark:text-yellow-400'
                }`}>
                  {course.status}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Sections:</span>
                <span className="font-medium">{course.sections.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Lessons:</span>
                <span className="font-medium">{course.totalLessons}</span>
              </div>
              <div className="flex justify-between">
                <span>Students:</span>
                <span className="font-medium">{course.enrollmentCount}</span>
              </div>
            </div>
          </div>

          {/* Completion Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Course Completion
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(completionStatus.percentage)}%
              </span>
            </div>
            
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3">
              <motion.div
                className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${completionStatus.percentage}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>

            <div className="space-y-2">
              {completionStatus.items.map((item, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm">
                  {item.completed ? (
                    <CheckCircle className="w-4 h-4 text-green-500" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-gray-400" />
                  )}
                  <span className={`${
                    item.completed 
                      ? 'text-gray-900 dark:text-white' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {item.label}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            Course Builder
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Build and manage your course content
          </p>
        </div>

        <div className="p-2">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const isActive = activeTab === tab.id

            return (
              <motion.button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`w-full text-left p-3 rounded-lg transition-all duration-200 relative overflow-hidden ${
                  isActive
                    ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white shadow-lg'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                {isActive && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-violet-500 to-purple-600"
                    layoutId="activeTab"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
                
                <div className="relative flex items-start space-x-3">
                  <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{tab.label}</div>
                    <div className={`text-sm mt-1 ${
                      isActive 
                        ? 'text-white/80' 
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {tab.description}
                    </div>
                  </div>
                </div>
              </motion.button>
            )
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <h4 className="font-medium text-gray-900 dark:text-white mb-3">
          Quick Actions
        </h4>
        
        <div className="space-y-2">
          <button className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
            Preview as Student
          </button>
          <button className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
            Duplicate Course
          </button>
          <button className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
            Export Course Data
          </button>
          <button className="w-full text-left p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors">
            Course Settings
          </button>
        </div>
      </div>

      {/* Help & Tips */}
      <div className="bg-gradient-to-br from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-xl p-4 border border-violet-200 dark:border-violet-800">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
            <Clock className="w-4 h-4 text-white" />
          </div>
          <div>
            <h4 className="font-medium text-violet-900 dark:text-violet-100 mb-1">
              Course Building Tips
            </h4>
            <p className="text-sm text-violet-700 dark:text-violet-300">
              Start with a clear course structure, then add engaging content. 
              Preview your course regularly to ensure a great student experience.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
