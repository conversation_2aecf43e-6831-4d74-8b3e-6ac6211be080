'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Activity, 
  Database, 
  Server, 
  Zap, 
  Clock, 
  Users, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON>hart,
  <PERSON>Chart
} from 'lucide-react'
import { GlassmorphicCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicButton } from '@/components/ui/glassmorphic-button'

interface PerformanceMetrics {
  server: {
    uptime: number
    cpuUsage: number
    memoryUsage: number
    diskUsage: number
    responseTime: number
  }
  database: {
    connectionCount: number
    queryTime: number
    slowQueries: number
    cacheHitRate: number
  }
  application: {
    activeUsers: number
    requestsPerMinute: number
    errorRate: number
    averageLoadTime: number
  }
  cache: {
    hitRate: number
    missRate: number
    keyCount: number
    memoryUsage: string
  }
}

interface PerformanceAlert {
  id: string
  type: 'warning' | 'error' | 'info'
  title: string
  message: string
  timestamp: Date
  resolved: boolean
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(30000) // 30 seconds

  // Fetch performance metrics
  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/admin/performance-metrics')
      if (response.ok) {
        const data = await response.json()
        setMetrics(data.metrics)
        setAlerts(data.alerts || [])
      }
    } catch (error) {
      console.error('Error fetching performance metrics:', error)
    } finally {
      setLoading(false)
    }
  }

  // Auto-refresh metrics
  useEffect(() => {
    fetchMetrics()

    if (autoRefresh) {
      const interval = setInterval(fetchMetrics, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // Get status color based on value and thresholds
  const getStatusColor = (value: number, thresholds: { warning: number; error: number }) => {
    if (value >= thresholds.error) return 'text-red-500'
    if (value >= thresholds.warning) return 'text-yellow-500'
    return 'text-green-500'
  }

  // Get status icon
  const getStatusIcon = (value: number, thresholds: { warning: number; error: number }) => {
    if (value >= thresholds.error) return <AlertTriangle className="w-5 h-5 text-red-500" />
    if (value >= thresholds.warning) return <AlertTriangle className="w-5 h-5 text-yellow-500" />
    return <CheckCircle className="w-5 h-5 text-green-500" />
  }

  if (loading) {
    return (
      <GlassmorphicCard className="p-6">
        <div className="flex items-center justify-center py-8">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </GlassmorphicCard>
    )
  }

  if (!metrics) {
    return (
      <GlassmorphicCard className="p-6">
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Unable to Load Metrics
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Failed to fetch performance metrics
          </p>
          <GlassmorphicButton onClick={fetchMetrics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </GlassmorphicButton>
        </div>
      </GlassmorphicCard>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Performance Monitor
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time system performance metrics and alerts
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Auto-refresh</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
            </label>
          </div>

          <GlassmorphicButton onClick={fetchMetrics}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </GlassmorphicButton>
        </div>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <GlassmorphicCard className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            Active Alerts ({alerts.filter(a => !a.resolved).length})
          </h3>
          
          <div className="space-y-3">
            {alerts.filter(a => !a.resolved).map((alert) => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`p-4 rounded-lg border-l-4 ${
                  alert.type === 'error' ? 'border-red-500 bg-red-50 dark:bg-red-900/20' :
                  alert.type === 'warning' ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' :
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {alert.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {alert.message}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                      {alert.timestamp.toLocaleString()}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </GlassmorphicCard>
      )}

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Server Metrics */}
        <GlassmorphicCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Server className="w-5 h-5 text-blue-500 mr-2" />
              Server
            </h3>
            {getStatusIcon(metrics.server.cpuUsage, { warning: 70, error: 90 })}
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">CPU Usage</span>
                <span className={getStatusColor(metrics.server.cpuUsage, { warning: 70, error: 90 })}>
                  {metrics.server.cpuUsage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    metrics.server.cpuUsage >= 90 ? 'bg-red-500' :
                    metrics.server.cpuUsage >= 70 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${metrics.server.cpuUsage}%` }}
                />
              </div>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Memory</span>
                <span className={getStatusColor(metrics.server.memoryUsage, { warning: 80, error: 95 })}>
                  {metrics.server.memoryUsage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${
                    metrics.server.memoryUsage >= 95 ? 'bg-red-500' :
                    metrics.server.memoryUsage >= 80 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${metrics.server.memoryUsage}%` }}
                />
              </div>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Uptime</span>
              <span className="text-gray-900 dark:text-white">
                {Math.floor(metrics.server.uptime / 3600)}h {Math.floor((metrics.server.uptime % 3600) / 60)}m
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Response Time</span>
              <span className={getStatusColor(metrics.server.responseTime, { warning: 500, error: 1000 })}>
                {metrics.server.responseTime}ms
              </span>
            </div>
          </div>
        </GlassmorphicCard>

        {/* Database Metrics */}
        <GlassmorphicCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Database className="w-5 h-5 text-green-500 mr-2" />
              Database
            </h3>
            {getStatusIcon(metrics.database.queryTime, { warning: 100, error: 500 })}
          </div>

          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Connections</span>
              <span className="text-gray-900 dark:text-white">
                {metrics.database.connectionCount}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Avg Query Time</span>
              <span className={getStatusColor(metrics.database.queryTime, { warning: 100, error: 500 })}>
                {metrics.database.queryTime}ms
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Slow Queries</span>
              <span className={getStatusColor(metrics.database.slowQueries, { warning: 5, error: 20 })}>
                {metrics.database.slowQueries}
              </span>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Cache Hit Rate</span>
                <span className={getStatusColor(100 - metrics.database.cacheHitRate, { warning: 20, error: 40 })}>
                  {metrics.database.cacheHitRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-green-500"
                  style={{ width: `${metrics.database.cacheHitRate}%` }}
                />
              </div>
            </div>
          </div>
        </GlassmorphicCard>

        {/* Application Metrics */}
        <GlassmorphicCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Activity className="w-5 h-5 text-purple-500 mr-2" />
              Application
            </h3>
            {getStatusIcon(metrics.application.errorRate, { warning: 1, error: 5 })}
          </div>

          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Active Users</span>
              <span className="text-gray-900 dark:text-white">
                {metrics.application.activeUsers.toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Requests/min</span>
              <span className="text-gray-900 dark:text-white">
                {metrics.application.requestsPerMinute.toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Error Rate</span>
              <span className={getStatusColor(metrics.application.errorRate, { warning: 1, error: 5 })}>
                {metrics.application.errorRate}%
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Avg Load Time</span>
              <span className={getStatusColor(metrics.application.averageLoadTime, { warning: 2000, error: 5000 })}>
                {metrics.application.averageLoadTime}ms
              </span>
            </div>
          </div>
        </GlassmorphicCard>

        {/* Cache Metrics */}
        <GlassmorphicCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <Zap className="w-5 h-5 text-orange-500 mr-2" />
              Cache
            </h3>
            {getStatusIcon(metrics.cache.missRate, { warning: 20, error: 40 })}
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Hit Rate</span>
                <span className="text-green-500">
                  {metrics.cache.hitRate}%
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-green-500"
                  style={{ width: `${metrics.cache.hitRate}%` }}
                />
              </div>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Keys</span>
              <span className="text-gray-900 dark:text-white">
                {metrics.cache.keyCount.toLocaleString()}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Memory Usage</span>
              <span className="text-gray-900 dark:text-white">
                {metrics.cache.memoryUsage}
              </span>
            </div>
          </div>
        </GlassmorphicCard>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <GlassmorphicCard className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <LineChart className="w-5 h-5 text-blue-500 mr-2" />
            Response Time Trend
          </h3>
          <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
            Chart placeholder - integrate with charting library
          </div>
        </GlassmorphicCard>

        <GlassmorphicCard className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 text-green-500 mr-2" />
            Resource Usage
          </h3>
          <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
            Chart placeholder - integrate with charting library
          </div>
        </GlassmorphicCard>
      </div>
    </div>
  )
}
