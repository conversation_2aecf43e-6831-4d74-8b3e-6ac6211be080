'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  X, 
  Star, 
  Clock, 
  DollarSign, 
  User, 
  Tag, 
  TrendingUp,
  Sparkles,
  BookOpen,
  ChevronDown,
  SlidersHorizontal
} from 'lucide-react'
import { toast } from 'sonner'
// import { useDebounce } from '@/hooks/use-debounce'

// Simple debounce hook implementation
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}
import { GlassmorphicCard, GlassCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicInput, GlassmorphicSelect } from '@/components/ui/glassmorphic-forms'
import { GlassmorphicButton, IconButton } from '@/components/ui/glassmorphic-button'

interface SearchFilters {
  q: string
  category: string
  level: string
  duration: string
  price: string
  rating: number
  instructor: string
  tags: string
  sortBy: string
  featured: boolean
  language: string
}

interface Course {
  id: string
  title: string
  description: string
  shortDescription: string
  thumbnailImage?: string
  price: number
  originalPrice?: number
  currency: string
  level: string
  category: string
  tags: string[]
  language: string
  estimatedDuration: number
  averageRating: number
  reviewCount: number
  enrollmentCount: number
  isFeatured: boolean
  instructor: {
    id: string
    name: string
    image?: string
  }
  trending?: {
    score: number
    recentEnrollments: number
    period: string
  }
  recommendationScore?: number
  recommendationReason?: string
}

interface SearchSuggestion {
  type: 'course' | 'instructor' | 'tag'
  id?: string
  title?: string
  name?: string
  image?: string
}

interface AdvancedSearchProps {
  onResults: (courses: Course[], filters: SearchFilters) => void
  onLoading: (loading: boolean) => void
  initialQuery?: string
  className?: string
}

export function AdvancedSearch({ 
  onResults, 
  onLoading, 
  initialQuery = '', 
  className = '' 
}: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    q: initialQuery,
    category: '',
    level: '',
    duration: '',
    price: '',
    rating: 0,
    instructor: '',
    tags: '',
    sortBy: 'relevance',
    featured: false,
    language: ''
  })

  const [showFilters, setShowFilters] = useState(false)
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isSearching, setIsSearching] = useState(false)

  const debouncedQuery = useDebounce(filters.q, 300)
  const debouncedSuggestionQuery = useDebounce(filters.q, 150)

  // Categories and options
  const categories = [
    'Programming', 'Design', 'Business', 'Marketing', 'Data Science',
    'Photography', 'Music', 'Health & Fitness', 'Language', 'Personal Development'
  ]

  const levels = [
    { value: 'BEGINNER', label: 'Beginner' },
    { value: 'INTERMEDIATE', label: 'Intermediate' },
    { value: 'ADVANCED', label: 'Advanced' }
  ]

  const durations = [
    { value: 'SHORT', label: 'Short (< 2 hours)' },
    { value: 'MEDIUM', label: 'Medium (2-10 hours)' },
    { value: 'LONG', label: 'Long (> 10 hours)' }
  ]

  const priceOptions = [
    { value: 'FREE', label: 'Free' },
    { value: 'PAID', label: 'Paid' }
  ]

  const sortOptions = [
    { value: 'relevance', label: 'Most Relevant' },
    { value: 'popularity', label: 'Most Popular' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'newest', label: 'Newest' },
    { value: 'price_low', label: 'Price: Low to High' },
    { value: 'price_high', label: 'Price: High to Low' }
  ]

  // Fetch search suggestions
  const fetchSuggestions = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSuggestions([])
      return
    }

    try {
      const response = await fetch('/api/search/courses/suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, limit: 8 })
      })

      if (response.ok) {
        const data = await response.json()
        const allSuggestions = [
          ...data.courses,
          ...data.instructors,
          ...data.tags
        ]
        setSuggestions(allSuggestions)
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
    }
  }, [])

  // Perform search
  const performSearch = useCallback(async (searchFilters: SearchFilters) => {
    setIsSearching(true)
    onLoading(true)

    try {
      const params = new URLSearchParams()
      
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (value && value !== '' && value !== 0 && value !== false) {
          params.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/search/courses?${params}`)
      
      if (!response.ok) {
        throw new Error('Search failed')
      }

      const data = await response.json()
      onResults(data.courses, searchFilters)
    } catch (error) {
      console.error('Search error:', error)
      toast.error('Search failed. Please try again.')
      onResults([], searchFilters)
    } finally {
      setIsSearching(false)
      onLoading(false)
    }
  }, [onResults, onLoading])

  // Effect for search suggestions
  useEffect(() => {
    fetchSuggestions(debouncedSuggestionQuery)
  }, [debouncedSuggestionQuery, fetchSuggestions])

  // Effect for search execution
  useEffect(() => {
    if (debouncedQuery || Object.values(filters).some(v => v && v !== '' && v !== 0 && v !== false)) {
      performSearch(filters)
    }
  }, [debouncedQuery, filters, performSearch])

  // Handle filter changes
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setShowSuggestions(false)
  }

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'course') {
      updateFilter('q', suggestion.title || '')
    } else if (suggestion.type === 'instructor') {
      updateFilter('instructor', suggestion.name || '')
      updateFilter('q', '')
    } else if (suggestion.type === 'tag') {
      updateFilter('tags', suggestion.name || '')
      updateFilter('q', '')
    }
    setShowSuggestions(false)
  }

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      q: '',
      category: '',
      level: '',
      duration: '',
      price: '',
      rating: 0,
      instructor: '',
      tags: '',
      sortBy: 'relevance',
      featured: false,
      language: ''
    })
  }

  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'q' && key !== 'sortBy' && value && value !== '' && value !== 0 && value !== false
  ).length

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Bar */}
      <GlassCard className="p-4">
        <div className="relative">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={filters.q}
              onChange={(e) => {
                updateFilter('q', e.target.value)
                setShowSuggestions(true)
              }}
              onFocus={() => setShowSuggestions(true)}
              placeholder="Search for courses, instructors, or topics..."
              className="w-full pl-10 pr-12 py-4 bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500 transition-all duration-200"
            />
            
            {isSearching && (
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <div className="w-5 h-5 border-2 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* Search Suggestions */}
          <AnimatePresence>
            {showSuggestions && suggestions.length > 0 && filters.q.length > 1 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full left-0 right-0 mt-2 z-50"
              >
                <GlassmorphicCard variant="heavy" className="py-2 max-h-80 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <motion.button
                      key={`${suggestion.type}-${suggestion.id || suggestion.name}-${index}`}
                      className="w-full flex items-center space-x-3 px-4 py-3 hover:bg-white/10 dark:hover:bg-white/5 transition-colors text-left"
                      onClick={() => handleSuggestionSelect(suggestion)}
                      whileHover={{ x: 4 }}
                    >
                      <div className="flex-shrink-0">
                        {suggestion.type === 'course' && <BookOpen className="w-4 h-4 text-blue-500" />}
                        {suggestion.type === 'instructor' && <User className="w-4 h-4 text-green-500" />}
                        {suggestion.type === 'tag' && <Tag className="w-4 h-4 text-purple-500" />}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 dark:text-white truncate">
                          {suggestion.title || suggestion.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                          {suggestion.type}
                        </div>
                      </div>

                      {suggestion.image && (
                        <img
                          src={suggestion.image}
                          alt=""
                          className="w-8 h-8 rounded-full object-cover"
                        />
                      )}
                    </motion.button>
                  ))}
                </GlassmorphicCard>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Search Controls */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-3">
            <GlassmorphicButton
              variant="glass"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <span className="absolute -top-2 -right-2 w-5 h-5 bg-violet-500 text-white text-xs rounded-full flex items-center justify-center">
                  {activeFilterCount}
                </span>
              )}
            </GlassmorphicButton>

            {activeFilterCount > 0 && (
              <GlassmorphicButton
                variant="ghost"
                size="sm"
                onClick={clearFilters}
              >
                <X className="w-4 h-4 mr-2" />
                Clear
              </GlassmorphicButton>
            )}
          </div>

          <GlassmorphicSelect
            options={sortOptions}
            value={filters.sortBy}
            onChange={(value) => updateFilter('sortBy', value)}
            className="w-48"
            variant="filled"
          />
        </div>
      </GlassCard>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            <GlassCard className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Category */}
                <GlassmorphicSelect
                  label="Category"
                  options={[
                    { value: '', label: 'All Categories' },
                    ...categories.map(cat => ({ value: cat, label: cat }))
                  ]}
                  value={filters.category}
                  onChange={(value) => updateFilter('category', value)}
                />

                {/* Level */}
                <GlassmorphicSelect
                  label="Difficulty Level"
                  options={[
                    { value: '', label: 'All Levels' },
                    ...levels
                  ]}
                  value={filters.level}
                  onChange={(value) => updateFilter('level', value)}
                />

                {/* Duration */}
                <GlassmorphicSelect
                  label="Course Duration"
                  options={[
                    { value: '', label: 'Any Duration' },
                    ...durations
                  ]}
                  value={filters.duration}
                  onChange={(value) => updateFilter('duration', value)}
                />

                {/* Price */}
                <GlassmorphicSelect
                  label="Price"
                  options={[
                    { value: '', label: 'All Prices' },
                    ...priceOptions
                  ]}
                  value={filters.price}
                  onChange={(value) => updateFilter('price', value)}
                />

                {/* Rating */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Minimum Rating
                  </label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        onClick={() => updateFilter('rating', rating === filters.rating ? 0 : rating)}
                        className={`p-1 transition-colors ${
                          rating <= filters.rating
                            ? 'text-yellow-500'
                            : 'text-gray-300 dark:text-gray-600 hover:text-yellow-400'
                        }`}
                      >
                        <Star className="w-5 h-5 fill-current" />
                      </button>
                    ))}
                    <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                      {filters.rating > 0 ? `${filters.rating}+ stars` : 'Any rating'}
                    </span>
                  </div>
                </div>

                {/* Instructor */}
                <GlassmorphicInput
                  label="Instructor"
                  value={filters.instructor}
                  onChange={(e) => updateFilter('instructor', e.target.value)}
                  placeholder="Search by instructor name"
                  leftIcon={<User className="w-4 h-4" />}
                />

                {/* Tags */}
                <GlassmorphicInput
                  label="Tags"
                  value={filters.tags}
                  onChange={(e) => updateFilter('tags', e.target.value)}
                  placeholder="e.g. javascript, react, beginner"
                  leftIcon={<Tag className="w-4 h-4" />}
                  hint="Separate multiple tags with commas"
                />

                {/* Language */}
                <GlassmorphicInput
                  label="Language"
                  value={filters.language}
                  onChange={(e) => updateFilter('language', e.target.value)}
                  placeholder="e.g. English, Spanish"
                />

                {/* Featured Toggle */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Course Type
                  </label>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.featured}
                      onChange={(e) => updateFilter('featured', e.target.checked)}
                      className="w-4 h-4 text-violet-600 bg-white/10 border-white/20 rounded focus:ring-violet-500 focus:ring-2"
                    />
                    <span className="text-sm text-gray-700 dark:text-gray-300 flex items-center">
                      <Sparkles className="w-4 h-4 mr-1 text-yellow-500" />
                      Featured courses only
                    </span>
                  </label>
                </div>
              </div>
            </GlassCard>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
