// 2025 Design System with Glassmorphism
// Advanced design tokens and utilities for modern UI

export const designTokens = {
  // Color Palette - 2025 Trends
  colors: {
    // Primary Brand Colors
    primary: {
      50: '#f0f4ff',
      100: '#e0e7ff',
      200: '#c7d2fe',
      300: '#a5b4fc',
      400: '#818cf8',
      500: '#6366f1', // Main brand color
      600: '#4f46e5',
      700: '#4338ca',
      800: '#3730a3',
      900: '#312e81',
      950: '#1e1b4b'
    },
    
    // Secondary Colors
    secondary: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
      950: '#3b0764'
    },
    
    // Accent Colors
    accent: {
      cyan: '#06b6d4',
      emerald: '#10b981',
      amber: '#f59e0b',
      rose: '#f43f5e',
      indigo: '#6366f1',
      purple: '#8b5cf6'
    },
    
    // Neutral Colors
    neutral: {
      0: '#ffffff',
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617'
    },
    
    // Semantic Colors
    semantic: {
      success: {
        light: '#dcfce7',
        main: '#16a34a',
        dark: '#15803d'
      },
      warning: {
        light: '#fef3c7',
        main: '#d97706',
        dark: '#b45309'
      },
      error: {
        light: '#fee2e2',
        main: '#dc2626',
        dark: '#b91c1c'
      },
      info: {
        light: '#dbeafe',
        main: '#2563eb',
        dark: '#1d4ed8'
      }
    }
  },
  
  // Glassmorphism Effects
  glass: {
    // Background blur intensities
    blur: {
      none: 'blur(0)',
      sm: 'blur(4px)',
      md: 'blur(8px)',
      lg: 'blur(16px)',
      xl: 'blur(24px)',
      '2xl': 'blur(40px)',
      '3xl': 'blur(64px)'
    },
    
    // Glass surface variations
    surfaces: {
      // Ultra-light glass
      light: {
        background: 'rgba(255, 255, 255, 0.05)',
        border: 'rgba(255, 255, 255, 0.1)',
        backdrop: 'blur(8px)',
        shadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
      },
      
      // Standard glass
      medium: {
        background: 'rgba(255, 255, 255, 0.1)',
        border: 'rgba(255, 255, 255, 0.15)',
        backdrop: 'blur(16px)',
        shadow: '0 8px 32px rgba(0, 0, 0, 0.15)'
      },
      
      // Heavy glass
      heavy: {
        background: 'rgba(255, 255, 255, 0.15)',
        border: 'rgba(255, 255, 255, 0.2)',
        backdrop: 'blur(24px)',
        shadow: '0 12px 40px rgba(0, 0, 0, 0.2)'
      },
      
      // Dark glass variants
      darkLight: {
        background: 'rgba(0, 0, 0, 0.05)',
        border: 'rgba(255, 255, 255, 0.05)',
        backdrop: 'blur(8px)',
        shadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
      },
      
      darkMedium: {
        background: 'rgba(0, 0, 0, 0.1)',
        border: 'rgba(255, 255, 255, 0.1)',
        backdrop: 'blur(16px)',
        shadow: '0 8px 32px rgba(0, 0, 0, 0.4)'
      },
      
      darkHeavy: {
        background: 'rgba(0, 0, 0, 0.15)',
        border: 'rgba(255, 255, 255, 0.15)',
        backdrop: 'blur(24px)',
        shadow: '0 12px 40px rgba(0, 0, 0, 0.5)'
      }
    }
  },
  
  // Typography Scale
  typography: {
    fontFamilies: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      serif: ['Crimson Pro', 'Georgia', 'serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      display: ['Cal Sans', 'Inter', 'system-ui', 'sans-serif']
    },
    
    fontSizes: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      base: '1rem',     // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem', // 36px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
      '7xl': '4.5rem',  // 72px
      '8xl': '6rem',    // 96px
      '9xl': '8rem'     // 128px
    },
    
    fontWeights: {
      thin: 100,
      extralight: 200,
      light: 300,
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
      extrabold: 800,
      black: 900
    },
    
    lineHeights: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    }
  },
  
  // Spacing Scale
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',  // 2px
    1: '0.25rem',     // 4px
    1.5: '0.375rem',  // 6px
    2: '0.5rem',      // 8px
    2.5: '0.625rem',  // 10px
    3: '0.75rem',     // 12px
    3.5: '0.875rem',  // 14px
    4: '1rem',        // 16px
    5: '1.25rem',     // 20px
    6: '1.5rem',      // 24px
    7: '1.75rem',     // 28px
    8: '2rem',        // 32px
    9: '2.25rem',     // 36px
    10: '2.5rem',     // 40px
    11: '2.75rem',    // 44px
    12: '3rem',       // 48px
    14: '3.5rem',     // 56px
    16: '4rem',       // 64px
    20: '5rem',       // 80px
    24: '6rem',       // 96px
    28: '7rem',       // 112px
    32: '8rem',       // 128px
    36: '9rem',       // 144px
    40: '10rem',      // 160px
    44: '11rem',      // 176px
    48: '12rem',      // 192px
    52: '13rem',      // 208px
    56: '14rem',      // 224px
    60: '15rem',      // 240px
    64: '16rem',      // 256px
    72: '18rem',      // 288px
    80: '20rem',      // 320px
    96: '24rem'       // 384px
  },
  
  // Border Radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px'
  },
  
  // Shadows
  shadows: {
    // Standard shadows
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    
    // Glassmorphism shadows
    glass: {
      sm: '0 4px 16px rgba(0, 0, 0, 0.1)',
      md: '0 8px 32px rgba(0, 0, 0, 0.15)',
      lg: '0 12px 40px rgba(0, 0, 0, 0.2)',
      xl: '0 16px 48px rgba(0, 0, 0, 0.25)'
    },
    
    // Colored shadows
    colored: {
      primary: '0 8px 32px rgba(99, 102, 241, 0.3)',
      secondary: '0 8px 32px rgba(168, 85, 247, 0.3)',
      success: '0 8px 32px rgba(34, 197, 94, 0.3)',
      warning: '0 8px 32px rgba(245, 158, 11, 0.3)',
      error: '0 8px 32px rgba(239, 68, 68, 0.3)'
    }
  },
  
  // Animation & Transitions
  animations: {
    durations: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
      slower: '500ms',
      slowest: '1000ms'
    },
    
    easings: {
      linear: 'linear',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)'
    }
  }
}

// Utility functions for applying design tokens
export const glassEffect = (variant: keyof typeof designTokens.glass.surfaces = 'medium') => {
  const surface = designTokens.glass.surfaces[variant]
  return {
    background: surface.background,
    backdropFilter: surface.backdrop,
    border: `1px solid ${surface.border}`,
    boxShadow: surface.shadow
  }
}

export const gradientText = (from: string, to: string) => ({
  background: `linear-gradient(135deg, ${from}, ${to})`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text'
})

export const gradientBackground = (from: string, to: string, direction = '135deg') => ({
  background: `linear-gradient(${direction}, ${from}, ${to})`
})

export const morphicCard = (variant: keyof typeof designTokens.glass.surfaces = 'medium') => {
  const surface = designTokens.glass.surfaces[variant]
  return {
    ...glassEffect(variant),
    borderRadius: designTokens.borderRadius['2xl'],
    transition: `all ${designTokens.animations.durations.normal} ${designTokens.animations.easings.easeOut}`,
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: designTokens.shadows.glass.lg
    }
  }
}

// Component variants
export const componentVariants = {
  button: {
    primary: {
      background: gradientBackground(designTokens.colors.primary[500], designTokens.colors.primary[600]),
      color: designTokens.colors.neutral[0],
      boxShadow: designTokens.shadows.colored.primary,
      '&:hover': {
        background: gradientBackground(designTokens.colors.primary[600], designTokens.colors.primary[700]),
        transform: 'translateY(-1px)',
        boxShadow: designTokens.shadows.colored.primary
      }
    },
    
    glass: {
      ...glassEffect('medium'),
      color: designTokens.colors.neutral[900],
      '&:hover': {
        ...glassEffect('heavy'),
        transform: 'translateY(-1px)'
      }
    },
    
    ghost: {
      background: 'transparent',
      border: `1px solid ${designTokens.colors.neutral[200]}`,
      color: designTokens.colors.neutral[700],
      '&:hover': {
        background: designTokens.colors.neutral[50],
        borderColor: designTokens.colors.neutral[300]
      }
    }
  },
  
  card: {
    glass: morphicCard('medium'),
    elevated: {
      background: designTokens.colors.neutral[0],
      borderRadius: designTokens.borderRadius['2xl'],
      boxShadow: designTokens.shadows.lg,
      border: `1px solid ${designTokens.colors.neutral[200]}`,
      transition: `all ${designTokens.animations.durations.normal} ${designTokens.animations.easings.easeOut}`,
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: designTokens.shadows.xl
      }
    }
  }
}

// CSS-in-JS helper for styled-components or emotion
export const css = (styles: any) => styles

// Tailwind CSS custom utilities (for tailwind.config.js)
export const tailwindUtilities = {
  '.glass-light': glassEffect('light'),
  '.glass-medium': glassEffect('medium'),
  '.glass-heavy': glassEffect('heavy'),
  '.glass-dark-light': glassEffect('darkLight'),
  '.glass-dark-medium': glassEffect('darkMedium'),
  '.glass-dark-heavy': glassEffect('darkHeavy'),
  
  '.gradient-primary': gradientBackground(designTokens.colors.primary[500], designTokens.colors.primary[600]),
  '.gradient-secondary': gradientBackground(designTokens.colors.secondary[500], designTokens.colors.secondary[600]),
  
  '.text-gradient-primary': gradientText(designTokens.colors.primary[500], designTokens.colors.primary[600]),
  '.text-gradient-secondary': gradientText(designTokens.colors.secondary[500], designTokens.colors.secondary[600]),
  
  '.morphic-card': morphicCard('medium'),
  '.morphic-card-light': morphicCard('light'),
  '.morphic-card-heavy': morphicCard('heavy')
}
