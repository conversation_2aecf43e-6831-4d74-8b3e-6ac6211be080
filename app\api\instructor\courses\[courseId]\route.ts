import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCourseSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  level: z.enum(['Beginner', 'Intermediate', 'Advanced']).optional(),
  language: z.string().optional(),
  price: z.number().min(0).optional(),
  originalPrice: z.number().optional(),
  currency: z.string().optional(),
  thumbnailImage: z.string().url().optional(),
  previewVideo: z.string().url().optional(),
  tags: z.array(z.string()).optional(),
  keywords: z.array(z.string()).optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  allowComments: z.boolean().optional(),
  allowDownloads: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED', 'PRIVATE']).optional()
})

// GET /api/instructor/courses/[courseId] - Get course details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const courseId = request.url.split('/').pop()

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Get course with full details
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id // Ensure instructor owns the course
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true,
              bio: true,
              expertise: true,
              isVerified: true
            }
          },
          sections: {
            orderBy: { order: 'asc' },
            include: {
              topics: {
                orderBy: { order: 'asc' },
                include: {
                  content: {
                    orderBy: { order: 'asc' }
                  }
                }
              }
            }
          },
          quizzes: {
            orderBy: { order: 'asc' },
            include: {
              questions: {
                orderBy: { order: 'asc' }
              }
            }
          },
          reviews: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true
                }
              }
            }
          },
          _count: {
            select: {
              enrollments: true,
              reviews: true,
              sections: true
            }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Calculate additional metrics
      const totalTopics = course.sections.reduce(
        (sum, section) => sum + section.topics.length, 
        0
      )
      
      const totalDuration = course.sections.reduce(
        (sum, section) => sum + section.topics.reduce(
          (topicSum, topic) => topicSum + topic.content.reduce(
            (contentSum, content) => contentSum + (content.duration || 0),
            0
          ),
          0
        ),
        0
      )

      const totalContent = course.sections.reduce(
        (sum, section) => sum + section.topics.reduce(
          (topicSum, topic) => topicSum + topic.content.length,
          0
        ),
        0
      )

      return APIResponse.success({
        course: {
          ...course,
          totalLessons: totalTopics,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          totalContent,
          sectionsCount: course._count.sections,
          enrollmentsCount: course._count.enrollments,
          reviewsCount: course._count.reviews
        }
      })

    } catch (error) {
      console.error('Error fetching course:', error)
      return APIResponse.error(
        'Failed to fetch course: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId] - Update course
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: updateCourseSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').pop()

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if course exists and belongs to instructor
      const existingCourse = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!existingCourse) {
        return APIResponse.error('Course not found', 404)
      }

      const updateData = { ...validatedBody }

      // If title is being updated, regenerate slug
      if (updateData.title && updateData.title !== existingCourse.title) {
        const baseSlug = updateData.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')

        let slug = baseSlug
        let counter = 1
        
        while (await prisma.course.findFirst({ 
          where: { 
            slug,
            id: { not: courseId }
          } 
        })) {
          slug = `${baseSlug}-${counter}`
          counter++
        }

        updateData.slug = slug
      }

      // If publishing, set publishedAt
      if (updateData.status === 'PUBLISHED' && !existingCourse.publishedAt) {
        updateData.publishedAt = new Date()
        updateData.isPublished = true
      } else if (updateData.status === 'PUBLISHED') {
        updateData.isPublished = true
      } else if (updateData.status && updateData.status !== 'PUBLISHED') {
        updateData.isPublished = false
      }

      // Update course
      const updatedCourse = await prisma.course.update({
        where: { id: courseId },
        data: updateData,
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Course updated successfully',
        course: updatedCourse
      })

    } catch (error) {
      console.error('Error updating course:', error)
      return APIResponse.error(
        'Failed to update course: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/instructor/courses/[courseId] - Delete course
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const courseId = request.url.split('/').pop()

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if course exists and belongs to instructor
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        },
        include: {
          _count: {
            select: { enrollments: true }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Prevent deletion if course has enrollments
      if (course._count.enrollments > 0) {
        return APIResponse.error(
          'Cannot delete course with active enrollments. Archive it instead.',
          400
        )
      }

      // Delete course (cascade will handle related records)
      await prisma.course.delete({
        where: { id: courseId }
      })

      return APIResponse.success({
        message: 'Course deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting course:', error)
      return APIResponse.error(
        'Failed to delete course: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
