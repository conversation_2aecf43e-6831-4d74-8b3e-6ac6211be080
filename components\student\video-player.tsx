'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize, 
  Settings, 
  SkipBack, 
  SkipForward,
  Loader2
} from 'lucide-react'
import { formatDuration } from '@/lib/bunny-config'

interface VideoPlayerProps {
  contentId: string
  videoUrl: string
  thumbnailUrl?: string
  title: string
  duration?: number
  initialProgress?: {
    lastPosition: number
    watchedDuration: number
    isCompleted: boolean
  }
  onProgressUpdate?: (progress: {
    currentTime: number
    duration: number
    watchedDuration: number
    isCompleted: boolean
  }) => void
  className?: string
}

export function VideoPlayer({
  contentId,
  videoUrl,
  thumbnailUrl,
  title,
  duration = 0,
  initialProgress,
  onProgressUpdate,
  className = ''
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const progressUpdateRef = useRef<NodeJS.Timeout>()
  
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(initialProgress?.lastPosition || 0)
  const [videoDuration, setVideoDuration] = useState(duration)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [playbackRate, setPlaybackRate] = useState(1)
  const [watchedDuration, setWatchedDuration] = useState(initialProgress?.watchedDuration || 0)
  const [showSettings, setShowSettings] = useState(false)

  // Auto-hide controls
  useEffect(() => {
    let timeout: NodeJS.Timeout
    
    if (showControls && isPlaying) {
      timeout = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    }

    return () => clearTimeout(timeout)
  }, [showControls, isPlaying])

  // Progress tracking
  useEffect(() => {
    if (isPlaying && videoRef.current) {
      progressUpdateRef.current = setInterval(() => {
        const video = videoRef.current
        if (!video) return

        const currentTime = video.currentTime
        const duration = video.duration || videoDuration
        const newWatchedDuration = watchedDuration + 1 // Add 1 second

        setCurrentTime(currentTime)
        setWatchedDuration(newWatchedDuration)

        // Check if video is completed (90% watched)
        const watchPercentage = duration > 0 ? (newWatchedDuration / duration) * 100 : 0
        const isCompleted = watchPercentage >= 90

        // Send progress update
        onProgressUpdate?.({
          currentTime,
          duration,
          watchedDuration: newWatchedDuration,
          isCompleted
        })

      }, 1000) // Update every second
    } else {
      if (progressUpdateRef.current) {
        clearInterval(progressUpdateRef.current)
      }
    }

    return () => {
      if (progressUpdateRef.current) {
        clearInterval(progressUpdateRef.current)
      }
    }
  }, [isPlaying, watchedDuration, videoDuration, onProgressUpdate])

  const handlePlay = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.play()
      setIsPlaying(true)
    }
  }, [])

  const handlePause = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
      setIsPlaying(false)
    }
  }, [])

  const handleTogglePlay = useCallback(() => {
    if (isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }, [isPlaying, handlePlay, handlePause])

  const handleSeek = useCallback((time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      setCurrentTime(time)
    }
  }, [])

  const handleVolumeChange = useCallback((newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume
      setVolume(newVolume)
      setIsMuted(newVolume === 0)
    }
  }, [])

  const handleToggleMute = useCallback(() => {
    if (videoRef.current) {
      const newMuted = !isMuted
      videoRef.current.muted = newMuted
      setIsMuted(newMuted)
    }
  }, [isMuted])

  const handlePlaybackRateChange = useCallback((rate: number) => {
    if (videoRef.current) {
      videoRef.current.playbackRate = rate
      setPlaybackRate(rate)
      setShowSettings(false)
    }
  }, [])

  const handleSkip = useCallback((seconds: number) => {
    if (videoRef.current) {
      const newTime = Math.max(0, Math.min(videoDuration, currentTime + seconds))
      handleSeek(newTime)
    }
  }, [currentTime, videoDuration, handleSeek])

  const handleFullscreen = useCallback(() => {
    if (!document.fullscreenElement && containerRef.current) {
      containerRef.current.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  const handleVideoLoad = useCallback(() => {
    if (videoRef.current) {
      const duration = videoRef.current.duration
      setVideoDuration(duration)
      setIsLoading(false)

      // Resume from last position
      if (initialProgress?.lastPosition) {
        videoRef.current.currentTime = initialProgress.lastPosition
      }
    }
  }, [initialProgress])

  const handleMouseMove = useCallback(() => {
    setShowControls(true)
  }, [])

  const progressPercentage = videoDuration > 0 ? (currentTime / videoDuration) * 100 : 0
  const watchedPercentage = videoDuration > 0 ? (watchedDuration / videoDuration) * 100 : 0

  return (
    <div
      ref={containerRef}
      className={`relative bg-black rounded-lg overflow-hidden group ${className}`}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        src={videoUrl}
        poster={thumbnailUrl}
        className="w-full h-full object-contain"
        onLoadedMetadata={handleVideoLoad}
        onTimeUpdate={() => setCurrentTime(videoRef.current?.currentTime || 0)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        onWaiting={() => setIsLoading(true)}
        onCanPlay={() => setIsLoading(false)}
        onClick={handleTogglePlay}
      />

      {/* Loading Spinner */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-black/50"
          >
            <Loader2 className="w-12 h-12 text-white animate-spin" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Play Button Overlay */}
      <AnimatePresence>
        {!isPlaying && !isLoading && (
          <motion.button
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            onClick={handleTogglePlay}
            className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/50 transition-colors"
          >
            <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
              <Play className="w-8 h-8 text-white ml-1" />
            </div>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Controls */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4"
          >
            {/* Progress Bar */}
            <div className="mb-4">
              <div className="relative h-1 bg-white/20 rounded-full cursor-pointer group/progress">
                {/* Watched Progress (Background) */}
                <div
                  className="absolute top-0 left-0 h-full bg-white/40 rounded-full"
                  style={{ width: `${Math.min(watchedPercentage, 100)}%` }}
                />
                
                {/* Current Progress */}
                <div
                  className="absolute top-0 left-0 h-full bg-violet-500 rounded-full"
                  style={{ width: `${progressPercentage}%` }}
                />
                
                {/* Progress Handle */}
                <div
                  className="absolute top-1/2 -translate-y-1/2 w-3 h-3 bg-violet-500 rounded-full opacity-0 group-hover/progress:opacity-100 transition-opacity"
                  style={{ left: `${progressPercentage}%`, marginLeft: '-6px' }}
                />
                
                {/* Click Handler */}
                <div
                  className="absolute inset-0 cursor-pointer"
                  onClick={(e) => {
                    const rect = e.currentTarget.getBoundingClientRect()
                    const clickX = e.clientX - rect.left
                    const percentage = clickX / rect.width
                    const newTime = percentage * videoDuration
                    handleSeek(newTime)
                  }}
                />
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Play/Pause */}
                <button
                  onClick={handleTogglePlay}
                  className="p-2 text-white hover:text-violet-400 transition-colors"
                >
                  {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>

                {/* Skip Buttons */}
                <button
                  onClick={() => handleSkip(-10)}
                  className="p-2 text-white hover:text-violet-400 transition-colors"
                >
                  <SkipBack className="w-5 h-5" />
                </button>
                
                <button
                  onClick={() => handleSkip(10)}
                  className="p-2 text-white hover:text-violet-400 transition-colors"
                >
                  <SkipForward className="w-5 h-5" />
                </button>

                {/* Volume */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleToggleMute}
                    className="p-2 text-white hover:text-violet-400 transition-colors"
                  >
                    {isMuted || volume === 0 ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                  </button>
                  
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={isMuted ? 0 : volume}
                    onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                    className="w-20 h-1 bg-white/20 rounded-full appearance-none cursor-pointer slider"
                  />
                </div>

                {/* Time Display */}
                <div className="text-white text-sm">
                  {formatDuration(currentTime)} / {formatDuration(videoDuration)}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Settings */}
                <div className="relative">
                  <button
                    onClick={() => setShowSettings(!showSettings)}
                    className="p-2 text-white hover:text-violet-400 transition-colors"
                  >
                    <Settings className="w-5 h-5" />
                  </button>

                  <AnimatePresence>
                    {showSettings && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        className="absolute bottom-full right-0 mb-2 bg-black/90 backdrop-blur-sm rounded-lg p-2 min-w-[120px]"
                      >
                        <div className="text-white text-sm font-medium mb-2">Playback Speed</div>
                        {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                          <button
                            key={rate}
                            onClick={() => handlePlaybackRateChange(rate)}
                            className={`block w-full text-left px-2 py-1 text-sm rounded hover:bg-white/10 transition-colors ${
                              playbackRate === rate ? 'text-violet-400' : 'text-white'
                            }`}
                          >
                            {rate}x
                          </button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Fullscreen */}
                <button
                  onClick={handleFullscreen}
                  className="p-2 text-white hover:text-violet-400 transition-colors"
                >
                  <Maximize className="w-5 h-5" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Video Title */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/80 to-transparent p-4"
          >
            <h3 className="text-white font-medium">{title}</h3>
          </motion.div>
        )}
      </AnimatePresence>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #8b5cf6;
          cursor: pointer;
        }

        .slider::-moz-range-thumb {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #8b5cf6;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  )
}
