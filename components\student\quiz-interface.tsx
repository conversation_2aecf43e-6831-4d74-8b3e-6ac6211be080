'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Flag,
  Eye,
  RotateCcw,
  Trophy,
  Target,
  BookOpen,
  Timer
} from 'lucide-react'
import { toast } from 'sonner'

interface Quiz {
  id: string
  title: string
  description?: string
  instructions?: string
  timeLimit?: number
  passingScore: number
  maxAttempts: number
  totalQuestions: number
  totalPoints: number
  course: {
    id: string
    title: string
  }
}

interface Question {
  id: string
  type: 'MULTIPLE_CHOICE' | 'TRUE_FALSE' | 'SHORT_ANSWER' | 'ESSAY'
  question: string
  options?: string[]
  points: number
  order: number
}

interface UserProgress {
  attemptsUsed: number
  attemptsRemaining: number
  canTakeQuiz: boolean
  bestScore: number
  hasPassed: boolean
  attempts: any[]
}

interface QuizInterfaceProps {
  quizId: string
  courseId: string
  onComplete?: (result: any) => void
}

type QuizState = 'start' | 'taking' | 'completed' | 'results'

export function QuizInterface({ quizId, courseId, onComplete }: QuizInterfaceProps) {
  const [quizState, setQuizState] = useState<QuizState>('start')
  const [quiz, setQuiz] = useState<Quiz | null>(null)
  const [userProgress, setUserProgress] = useState<UserProgress | null>(null)
  const [questions, setQuestions] = useState<Question[]>([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [timeRemaining, setTimeRemaining] = useState<number | null>(null)
  const [attemptId, setAttemptId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [quizResult, setQuizResult] = useState<any>(null)

  // Fetch quiz data
  useEffect(() => {
    const fetchQuiz = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/student/quizzes/${quizId}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch quiz')
        }

        const data = await response.json()
        setQuiz(data.quiz)
        setUserProgress(data.userProgress)
      } catch (error) {
        console.error('Error fetching quiz:', error)
        toast.error('Failed to load quiz')
      } finally {
        setIsLoading(false)
      }
    }

    if (quizId) {
      fetchQuiz()
    }
  }, [quizId])

  // Timer countdown
  useEffect(() => {
    if (quizState === 'taking' && timeRemaining !== null && timeRemaining > 0) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev === null || prev <= 1) {
            // Auto-submit when time runs out
            handleSubmitQuiz()
            return 0
          }
          return prev - 1
        })
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [quizState, timeRemaining])

  // Start quiz attempt
  const startQuiz = async () => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ courseId })
      })

      if (!response.ok) {
        throw new Error('Failed to start quiz')
      }

      const data = await response.json()
      setAttemptId(data.attempt.id)
      setQuestions(data.attempt.questions)
      setTimeRemaining(data.attempt.timeLimit ? data.attempt.timeLimit * 60 : null)
      setQuizState('taking')
      
      toast.success('Quiz started! Good luck!')
    } catch (error) {
      console.error('Error starting quiz:', error)
      toast.error('Failed to start quiz')
    }
  }

  // Submit quiz
  const handleSubmitQuiz = async () => {
    if (!attemptId || isSubmitting) return

    try {
      setIsSubmitting(true)
      const startTime = timeRemaining !== null ? (quiz?.timeLimit || 0) * 60 : 0
      const timeSpent = startTime - (timeRemaining || 0)

      const response = await fetch(`/api/student/quizzes/${quizId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          attemptId,
          answers,
          timeSpent
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit quiz')
      }

      const data = await response.json()
      setQuizResult(data.result)
      setQuizState('results')
      
      onComplete?.(data.result)
      toast.success('Quiz submitted successfully!')
    } catch (error) {
      console.error('Error submitting quiz:', error)
      toast.error('Failed to submit quiz')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle answer selection
  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }))
  }

  // Navigation
  const goToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index)
    }
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading quiz...</p>
        </div>
      </div>
    )
  }

  if (!quiz || !userProgress) {
    return (
      <div className="text-center py-12">
        <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Quiz Not Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          The quiz you're looking for doesn't exist or is not available.
        </p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <AnimatePresence mode="wait">
        {/* Quiz Start Screen */}
        {quizState === 'start' && (
          <motion.div
            key="start"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-8"
          >
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {quiz.title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {quiz.course.title}
              </p>
            </div>

            {quiz.description && (
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  About This Quiz
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {quiz.description}
                </p>
              </div>
            )}

            {quiz.instructions && (
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Instructions
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {quiz.instructions}
                </p>
              </div>
            )}

            {/* Quiz Info */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <BookOpen className="w-6 h-6 text-violet-500 mx-auto mb-2" />
                <div className="font-semibold text-gray-900 dark:text-white">
                  {quiz.totalQuestions}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Questions
                </div>
              </div>

              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Target className="w-6 h-6 text-green-500 mx-auto mb-2" />
                <div className="font-semibold text-gray-900 dark:text-white">
                  {quiz.totalPoints}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Total Points
                </div>
              </div>

              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Trophy className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
                <div className="font-semibold text-gray-900 dark:text-white">
                  {quiz.passingScore}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Passing Score
                </div>
              </div>

              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                {quiz.timeLimit ? (
                  <>
                    <Timer className="w-6 h-6 text-red-500 mx-auto mb-2" />
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {quiz.timeLimit}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Minutes
                    </div>
                  </>
                ) : (
                  <>
                    <Clock className="w-6 h-6 text-blue-500 mx-auto mb-2" />
                    <div className="font-semibold text-gray-900 dark:text-white">
                      No Limit
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Time
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Attempt Info */}
            <div className="bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-violet-900 dark:text-violet-100">
                    Attempts: {userProgress.attemptsUsed} / {quiz.maxAttempts}
                  </div>
                  <div className="text-sm text-violet-700 dark:text-violet-300">
                    {userProgress.attemptsRemaining} attempts remaining
                  </div>
                </div>
                {userProgress.bestScore > 0 && (
                  <div className="text-right">
                    <div className="font-medium text-violet-900 dark:text-violet-100">
                      Best Score: {userProgress.bestScore}%
                    </div>
                    <div className={`text-sm ${
                      userProgress.hasPassed 
                        ? 'text-green-600 dark:text-green-400' 
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {userProgress.hasPassed ? 'Passed' : 'Not Passed'}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center space-x-4">
              {userProgress.canTakeQuiz ? (
                <button
                  onClick={startQuiz}
                  className="px-8 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg font-medium hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
                >
                  Start Quiz
                </button>
              ) : (
                <div className="text-center">
                  <div className="px-8 py-3 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-lg font-medium">
                    No Attempts Remaining
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    You have used all {quiz.maxAttempts} attempts for this quiz
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Quiz Taking Interface */}
        {quizState === 'taking' && questions.length > 0 && (
          <motion.div
            key="taking"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            {/* Quiz Header */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="font-semibold text-gray-900 dark:text-white">
                    {quiz.title}
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Question {currentQuestionIndex + 1} of {questions.length}
                  </p>
                </div>

                {timeRemaining !== null && (
                  <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
                    timeRemaining < 300 // Less than 5 minutes
                      ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                      : 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                  }`}>
                    <Clock className="w-4 h-4" />
                    <span className="font-mono font-medium">
                      {formatTime(timeRemaining)}
                    </span>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              <div className="mt-4">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span>Progress</span>
                  <span>{Math.round(((currentQuestionIndex + 1) / questions.length) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>
            </div>

            {/* Current Question */}
            <QuestionCard
              question={questions[currentQuestionIndex]}
              questionNumber={currentQuestionIndex + 1}
              answer={answers[questions[currentQuestionIndex].id] || ''}
              onAnswerChange={(answer) => handleAnswerChange(questions[currentQuestionIndex].id, answer)}
            />

            {/* Navigation */}
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <button
                  onClick={() => goToQuestion(currentQuestionIndex - 1)}
                  disabled={currentQuestionIndex === 0}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ArrowLeft className="w-4 h-4" />
                  <span>Previous</span>
                </button>

                <div className="flex items-center space-x-2">
                  {/* Question indicators */}
                  <div className="flex space-x-1">
                    {questions.slice(0, 10).map((_, index) => (
                      <button
                        key={index}
                        onClick={() => goToQuestion(index)}
                        className={`w-8 h-8 rounded-full text-xs font-medium transition-colors ${
                          index === currentQuestionIndex
                            ? 'bg-violet-600 text-white'
                            : answers[questions[index].id]
                            ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        {index + 1}
                      </button>
                    ))}
                    {questions.length > 10 && (
                      <span className="text-gray-400">...</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {currentQuestionIndex === questions.length - 1 ? (
                    <button
                      onClick={handleSubmitQuiz}
                      disabled={isSubmitting}
                      className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <Flag className="w-4 h-4" />
                      <span>{isSubmitting ? 'Submitting...' : 'Submit Quiz'}</span>
                    </button>
                  ) : (
                    <button
                      onClick={() => goToQuestion(currentQuestionIndex + 1)}
                      className="flex items-center space-x-2 px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
                    >
                      <span>Next</span>
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Quiz Results */}
        {quizState === 'results' && quizResult && (
          <motion.div
            key="results"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <QuizResults result={quizResult} quiz={quiz} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Question Card Component (to be implemented separately)
interface QuestionCardProps {
  question: Question
  questionNumber: number
  answer: string
  onAnswerChange: (answer: string) => void
}

function QuestionCard({ question, questionNumber, answer, onAnswerChange }: QuestionCardProps) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      <div className="mb-6">
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Question {questionNumber}
          </h3>
          <span className="px-2 py-1 bg-violet-100 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300 rounded text-sm font-medium">
            {question.points} {question.points === 1 ? 'point' : 'points'}
          </span>
        </div>
        
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {question.question}
        </p>
      </div>

      {/* Answer Options */}
      <div className="space-y-3">
        {question.type === 'MULTIPLE_CHOICE' && question.options && (
          <div className="space-y-2">
            {question.options.map((option, index) => (
              <label
                key={index}
                className={`flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                  answer === option
                    ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={answer === option}
                  onChange={(e) => onAnswerChange(e.target.value)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                  answer === option
                    ? 'border-violet-500 bg-violet-500'
                    : 'border-gray-300 dark:border-gray-600'
                }`}>
                  {answer === option && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <span className="text-gray-700 dark:text-gray-300">{option}</span>
              </label>
            ))}
          </div>
        )}

        {question.type === 'TRUE_FALSE' && (
          <div className="grid grid-cols-2 gap-4">
            {['True', 'False'].map((option) => (
              <label
                key={option}
                className={`flex items-center justify-center p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                  answer === option
                    ? 'border-violet-500 bg-violet-50 dark:bg-violet-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={option}
                  checked={answer === option}
                  onChange={(e) => onAnswerChange(e.target.value)}
                  className="sr-only"
                />
                <div className="flex items-center space-x-2">
                  {option === 'True' ? (
                    <CheckCircle className={`w-5 h-5 ${
                      answer === option ? 'text-green-500' : 'text-gray-400'
                    }`} />
                  ) : (
                    <XCircle className={`w-5 h-5 ${
                      answer === option ? 'text-red-500' : 'text-gray-400'
                    }`} />
                  )}
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {option}
                  </span>
                </div>
              </label>
            ))}
          </div>
        )}

        {(question.type === 'SHORT_ANSWER' || question.type === 'ESSAY') && (
          <textarea
            value={answer}
            onChange={(e) => onAnswerChange(e.target.value)}
            rows={question.type === 'ESSAY' ? 6 : 3}
            className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
            placeholder={`Enter your ${question.type === 'ESSAY' ? 'essay' : 'answer'}...`}
          />
        )}
      </div>
    </div>
  )
}

// Quiz Results Component (placeholder)
function QuizResults({ result, quiz }: { result: any; quiz: Quiz }) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-8 text-center">
      <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
        result.isPassed 
          ? 'bg-green-100 dark:bg-green-900/20' 
          : 'bg-red-100 dark:bg-red-900/20'
      }`}>
        {result.isPassed ? (
          <Trophy className="w-8 h-8 text-green-600 dark:text-green-400" />
        ) : (
          <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
        )}
      </div>

      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
        {result.isPassed ? 'Congratulations!' : 'Quiz Completed'}
      </h2>

      <p className={`text-lg mb-6 ${
        result.isPassed 
          ? 'text-green-600 dark:text-green-400' 
          : 'text-red-600 dark:text-red-400'
      }`}>
        {result.isPassed 
          ? `You passed with ${result.score}%!` 
          : `You scored ${result.score}%. Passing score is ${quiz.passingScore}%.`
        }
      </p>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="font-semibold text-gray-900 dark:text-white text-xl">
            {result.score}%
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Final Score
          </div>
        </div>

        <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="font-semibold text-gray-900 dark:text-white text-xl">
            {result.correctAnswers}/{result.totalQuestions}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Correct Answers
          </div>
        </div>

        <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="font-semibold text-gray-900 dark:text-white text-xl">
            {result.earnedPoints}/{result.totalPoints}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Points Earned
          </div>
        </div>

        <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="font-semibold text-gray-900 dark:text-white text-xl">
            {Math.round(result.timeSpent / 60)}m
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Time Spent
          </div>
        </div>
      </div>

      <div className="flex justify-center space-x-4">
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
        >
          Back to Course
        </button>
        
        <button className="px-6 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors">
          View Detailed Results
        </button>
      </div>
    </div>
  )
}
