'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import {
  ArrowLeft,
  BookOpen,
  MessageSquare,
  Award,
  BarChart3,
  Users,
  Clock,
  Target,
  Trophy,
  Star
} from 'lucide-react'
import { toast } from 'sonner'

// Import our new components
import { EnhancedCoursePlayer } from '@/components/student/enhanced-course-player'
import { QuizInterface } from '@/components/student/quiz-interface'
import { DiscussionForum } from '@/components/course/discussion-forum'
import { DiscussionView } from '@/components/course/discussion-view'
import { CertificateGallery } from '@/components/student/certificate-gallery'

interface Course {
  id: string
  title: string
  description: string
  thumbnailImage?: string
  instructor: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  sections: Array<{
    id: string
    title: string
    description?: string
    order: number
    topics: Array<{
      id: string
      title: string
      description?: string
      type: 'VIDEO' | 'DOCUMENT' | 'QUIZ' | 'ASSIGNMENT'
      duration?: number
      order: number
      isCompleted: boolean
      isCurrent: boolean
      content?: {
        videoUrl?: string
        documentUrl?: string
        quizId?: string
      }
    }>
  }>
  progress: {
    completedTopics: number
    totalTopics: number
    completionPercentage: number
    timeSpent: number
    currentTopicId?: string
  }
  stats: {
    totalQuizzes: number
    completedQuizzes: number
    averageQuizScore: number
    discussionPosts: number
    certificates: number
  }
  enrollment: {
    enrolledAt: string
    status: 'ACTIVE' | 'COMPLETED' | 'PAUSED'
  }
}

type ViewMode = 'player' | 'quiz' | 'discussions' | 'discussion-detail' | 'certificates' | 'analytics'

export default function StudentCoursePage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params.courseId as string

  const [course, setCourse] = useState<Course | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<ViewMode>('player')
  const [selectedQuizId, setSelectedQuizId] = useState<string | null>(null)
  const [selectedDiscussionId, setSelectedDiscussionId] = useState<string | null>(null)

  // Fetch course data
  useEffect(() => {
    const fetchCourse = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/student/courses/${courseId}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch course')
        }

        const data = await response.json()
        setCourse(data.course)
      } catch (error) {
        console.error('Error fetching course:', error)
        toast.error('Failed to load course')
        router.push('/student/dashboard')
      } finally {
        setIsLoading(false)
      }
    }

    if (courseId) {
      fetchCourse()
    }
  }, [courseId, router])

  // Handle topic completion
  const handleTopicComplete = async (topicId: string) => {
    try {
      const response = await fetch(`/api/student/courses/${courseId}/topics/${topicId}/complete`, {
        method: 'POST'
      })

      if (!response.ok) {
        throw new Error('Failed to mark topic as complete')
      }

      // Update local state
      setCourse(prev => {
        if (!prev) return null
        
        return {
          ...prev,
          sections: prev.sections.map(section => ({
            ...section,
            topics: section.topics.map(topic => 
              topic.id === topicId 
                ? { ...topic, isCompleted: true }
                : topic
            )
          })),
          progress: {
            ...prev.progress,
            completedTopics: prev.progress.completedTopics + 1,
            completionPercentage: ((prev.progress.completedTopics + 1) / prev.progress.totalTopics) * 100
          }
        }
      })

      toast.success('Topic marked as complete!')
    } catch (error) {
      console.error('Error completing topic:', error)
      toast.error('Failed to mark topic as complete')
    }
  }

  // Handle quiz start
  const handleQuizStart = (quizId: string) => {
    setSelectedQuizId(quizId)
    setViewMode('quiz')
  }

  // Handle quiz completion
  const handleQuizComplete = (result: any) => {
    toast.success(`Quiz completed! Score: ${result.score}%`)
    setViewMode('player')
    setSelectedQuizId(null)
    
    // Update course stats
    setCourse(prev => {
      if (!prev) return null
      
      return {
        ...prev,
        stats: {
          ...prev.stats,
          completedQuizzes: prev.stats.completedQuizzes + 1,
          averageQuizScore: ((prev.stats.averageQuizScore * (prev.stats.completedQuizzes - 1)) + result.score) / prev.stats.completedQuizzes
        }
      }
    })
  }

  // Handle discussion navigation
  const handleDiscussionOpen = () => {
    setViewMode('discussions')
  }

  const handleDiscussionSelect = (discussionId: string) => {
    setSelectedDiscussionId(discussionId)
    setViewMode('discussion-detail')
  }

  const handleDiscussionBack = () => {
    setSelectedDiscussionId(null)
    setViewMode('discussions')
  }

  // Handle certificate view
  const handleCertificateView = () => {
    setViewMode('certificates')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading course...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Course Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The course you're looking for doesn't exist or you don't have access to it.
          </p>
          <button
            onClick={() => router.push('/student/dashboard')}
            className="px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/student/dashboard')}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>

              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                  {course.title}
                </h1>
                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{course.instructor.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="w-3 h-3" />
                    <span>{Math.round(course.progress.completionPercentage)}% complete</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{Math.round(course.progress.timeSpent / 60)}h spent</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Tabs */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              {[
                { id: 'player', label: 'Learn', icon: BookOpen },
                { id: 'discussions', label: 'Discuss', icon: MessageSquare },
                { id: 'certificates', label: 'Certificates', icon: Award },
                { id: 'analytics', label: 'Progress', icon: BarChart3 }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setViewMode(tab.id as ViewMode)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                      viewMode === tab.id || 
                      (viewMode === 'discussion-detail' && tab.id === 'discussions') ||
                      (viewMode === 'quiz' && tab.id === 'player')
                        ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <AnimatePresence mode="wait">
        {(viewMode === 'player' || viewMode === 'quiz') && (
          <motion.div
            key="player"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="h-[calc(100vh-80px)]"
          >
            {viewMode === 'quiz' && selectedQuizId ? (
              <div className="p-6">
                <QuizInterface
                  quizId={selectedQuizId}
                  courseId={courseId}
                  onComplete={handleQuizComplete}
                />
              </div>
            ) : (
              <EnhancedCoursePlayer
                course={course}
                onTopicComplete={handleTopicComplete}
                onQuizStart={handleQuizStart}
                onDiscussionOpen={handleDiscussionOpen}
                onCertificateView={handleCertificateView}
              />
            )}
          </motion.div>
        )}

        {viewMode === 'discussions' && (
          <motion.div
            key="discussions"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="p-6"
          >
            <DiscussionForum
              courseId={courseId}
              userRole="STUDENT"
            />
          </motion.div>
        )}

        {viewMode === 'discussion-detail' && selectedDiscussionId && (
          <motion.div
            key="discussion-detail"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="p-6"
          >
            <DiscussionView
              discussionId={selectedDiscussionId}
              courseId={courseId}
              userRole="STUDENT"
              onBack={handleDiscussionBack}
            />
          </motion.div>
        )}

        {viewMode === 'certificates' && (
          <motion.div
            key="certificates"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="p-6"
          >
            <CertificateGallery />
          </motion.div>
        )}

        {viewMode === 'analytics' && (
          <motion.div
            key="analytics"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="p-6"
          >
            <CourseAnalytics course={course} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Course Analytics Component (placeholder)
function CourseAnalytics({ course }: { course: Course }) {
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Learning Analytics
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Track your progress and performance in this course
        </p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {Math.round(course.progress.completionPercentage)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Course Progress
              </div>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${course.progress.completionPercentage}%` }}
            />
          </div>
        </div>

        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {course.progress.completedTopics}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Topics Completed
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {course.stats.averageQuizScore}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Quiz Average
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 text-white" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {course.stats.certificates}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Certificates
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Analytics */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Detailed Progress
        </h3>
        
        <div className="space-y-4">
          {course.sections.map((section) => {
            const completedTopics = section.topics.filter(t => t.isCompleted).length
            const completionRate = (completedTopics / section.topics.length) * 100
            
            return (
              <div key={section.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {section.title}
                  </h4>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {completedTopics}/{section.topics.length} topics
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${completionRate}%` }}
                  />
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
