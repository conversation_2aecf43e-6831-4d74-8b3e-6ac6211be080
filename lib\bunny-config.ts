/**
 * Bunny CDN Configuration
 * 
 * This module provides configuration and utilities for integrating with Bunny CDN
 * for video storage, streaming, and content delivery.
 */

export interface BunnyConfig {
  apiKey: string
  storageZoneName: string
  storagePassword: string
  pullZoneUrl: string
  storageUrl: string
  streamApiKey?: string
  libraryId?: string
}

export interface BunnyVideoUploadResponse {
  success: boolean
  message: string
  videoId?: string
  videoUrl?: string
  thumbnailUrl?: string
  duration?: number
}

export interface BunnyVideoInfo {
  videoId: string
  title: string
  duration: number
  status: 'uploaded' | 'processing' | 'ready' | 'error'
  thumbnailUrl: string
  videoUrl: string
  qualities: VideoQuality[]
  fileSize: number
  createdAt: string
}

export interface VideoQuality {
  quality: '360p' | '720p' | '1080p' | '4k'
  url: string
  fileSize: number
  bitrate: number
}

/**
 * Get Bunny CDN configuration from environment variables
 */
export function getBunnyConfig(): BunnyConfig {
  const apiKey = process.env.BUNNY_API_KEY
  const storageZoneName = process.env.BUNNY_STORAGE_ZONE_NAME
  const storagePassword = process.env.BUNNY_STORAGE_PASSWORD
  const pullZoneUrl = process.env.BUNNY_PULL_ZONE_URL
  const streamApiKey = process.env.BUNNY_STREAM_API_KEY
  const libraryId = process.env.BUNNY_LIBRARY_ID

  if (!apiKey || !storageZoneName || !storagePassword || !pullZoneUrl) {
    throw new Error(
      'Missing required Bunny CDN configuration. Please set BUNNY_API_KEY, BUNNY_STORAGE_ZONE_NAME, BUNNY_STORAGE_PASSWORD, and BUNNY_PULL_ZONE_URL environment variables.'
    )
  }

  return {
    apiKey,
    storageZoneName,
    storagePassword,
    pullZoneUrl: pullZoneUrl.endsWith('/') ? pullZoneUrl.slice(0, -1) : pullZoneUrl,
    storageUrl: `https://storage.bunnycdn.com/${storageZoneName}`,
    streamApiKey,
    libraryId
  }
}

/**
 * Get Bunny CDN headers for API requests
 */
export function getBunnyHeaders(useStreamApi = false): Record<string, string> {
  const config = getBunnyConfig()
  const apiKey = useStreamApi ? config.streamApiKey : config.apiKey

  if (!apiKey) {
    throw new Error(`Missing ${useStreamApi ? 'Stream' : 'Storage'} API key for Bunny CDN`)
  }

  return {
    'AccessKey': apiKey,
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

/**
 * Get Bunny storage headers for file uploads
 */
export function getBunnyStorageHeaders(): Record<string, string> {
  const config = getBunnyConfig()

  return {
    'AccessKey': config.storagePassword,
    'Content-Type': 'application/octet-stream'
  }
}

/**
 * Generate secure video URL with token authentication
 */
export function generateSecureVideoUrl(
  videoPath: string, 
  expirationTime?: number,
  userIp?: string
): string {
  const config = getBunnyConfig()
  const baseUrl = config.pullZoneUrl
  
  // For basic implementation, return direct URL
  // In production, implement token-based authentication
  return `${baseUrl}/${videoPath}`
}

/**
 * Get video thumbnail URL
 */
export function getVideoThumbnailUrl(videoId: string, timestamp = 0): string {
  const config = getBunnyConfig()
  return `${config.pullZoneUrl}/thumbnails/${videoId}_${timestamp}.jpg`
}

/**
 * Validate video file type
 */
export function isValidVideoFile(fileName: string): boolean {
  const validExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return validExtensions.includes(extension)
}

/**
 * Get video file path for storage
 */
export function getVideoStoragePath(courseId: string, sectionId: string, topicId: string, fileName: string): string {
  const timestamp = Date.now()
  const extension = fileName.substring(fileName.lastIndexOf('.'))
  return `courses/${courseId}/sections/${sectionId}/topics/${topicId}/${timestamp}${extension}`
}

/**
 * Get document file path for storage
 */
export function getDocumentStoragePath(courseId: string, fileName: string): string {
  const timestamp = Date.now()
  const extension = fileName.substring(fileName.lastIndexOf('.'))
  const sanitizedName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
  return `courses/${courseId}/documents/${timestamp}_${sanitizedName}`
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Format video duration for display
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * Validate Bunny CDN response
 */
export function isBunnyApiError(response: any): boolean {
  return response && (response.HttpCode >= 400 || response.Message?.includes('error'))
}

/**
 * Extract error message from Bunny CDN response
 */
export function getBunnyErrorMessage(response: any): string {
  if (response?.Message) {
    return response.Message
  }
  if (response?.ErrorKey) {
    return response.ErrorKey
  }
  return 'Unknown Bunny CDN error occurred'
}
