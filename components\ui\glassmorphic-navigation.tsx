'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { ChevronDown, Menu, X } from 'lucide-react'
import { GlassmorphicCard } from './glassmorphic-card'
import { IconButton } from './glassmorphic-button'

interface NavigationItem {
  id: string
  label: string
  href?: string
  icon?: React.ReactNode
  badge?: string | number
  children?: NavigationItem[]
  onClick?: () => void
}

interface GlassmorphicNavigationProps {
  items: NavigationItem[]
  activeId?: string
  onItemClick?: (item: NavigationItem) => void
  className?: string
  variant?: 'horizontal' | 'vertical' | 'floating'
  position?: 'top' | 'bottom' | 'left' | 'right'
  collapsed?: boolean
  onToggleCollapse?: () => void
}

export function GlassmorphicNavigation({
  items,
  activeId,
  onItemClick,
  className,
  variant = 'horizontal',
  position = 'top',
  collapsed = false,
  onToggleCollapse
}: GlassmorphicNavigationProps) {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [isMobile, setIsMobile] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const handleItemClick = (item: NavigationItem) => {
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id)
    } else {
      onItemClick?.(item)
      item.onClick?.()
      if (isMobile) {
        setMobileMenuOpen(false)
      }
    }
  }

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const isActive = activeId === item.id
    const isExpanded = expandedItems.has(item.id)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div key={item.id} className="relative">
        <motion.button
          className={cn(
            'w-full flex items-center justify-between p-3 rounded-xl transition-all duration-200',
            'hover:bg-white/10 dark:hover:bg-white/5',
            isActive && 'bg-gradient-to-r from-violet-500/20 to-purple-500/20',
            isActive && 'border border-violet-500/30',
            isActive && 'shadow-lg shadow-violet-500/25',
            level > 0 && 'ml-4 text-sm',
            collapsed && variant === 'vertical' && 'justify-center px-2'
          )}
          onClick={() => handleItemClick(item)}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <div className="flex items-center space-x-3">
            {item.icon && (
              <div className={cn(
                'flex-shrink-0',
                isActive ? 'text-violet-400' : 'text-gray-600 dark:text-gray-400'
              )}>
                {item.icon}
              </div>
            )}
            
            {(!collapsed || variant !== 'vertical') && (
              <span className={cn(
                'font-medium truncate',
                isActive ? 'text-violet-400' : 'text-gray-700 dark:text-gray-300'
              )}>
                {item.label}
              </span>
            )}
            
            {item.badge && (!collapsed || variant !== 'vertical') && (
              <span className="ml-auto px-2 py-1 bg-violet-500 text-white text-xs rounded-full">
                {item.badge}
              </span>
            )}
          </div>

          {hasChildren && (!collapsed || variant !== 'vertical') && (
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="w-4 h-4 text-gray-500" />
            </motion.div>
          )}
        </motion.button>

        {/* Submenu */}
        <AnimatePresence>
          {hasChildren && isExpanded && (!collapsed || variant !== 'vertical') && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="py-2 space-y-1">
                {item.children?.map(child => renderNavigationItem(child, level + 1))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  if (variant === 'floating') {
    return (
      <GlassmorphicCard
        variant="heavy"
        className={cn(
          'fixed z-50 p-2',
          position === 'top' && 'top-4 left-1/2 -translate-x-1/2',
          position === 'bottom' && 'bottom-4 left-1/2 -translate-x-1/2',
          position === 'left' && 'left-4 top-1/2 -translate-y-1/2',
          position === 'right' && 'right-4 top-1/2 -translate-y-1/2',
          className
        )}
      >
        <div className={cn(
          'flex',
          (position === 'top' || position === 'bottom') && 'flex-row space-x-2',
          (position === 'left' || position === 'right') && 'flex-col space-y-2'
        )}>
          {items.map(item => (
            <motion.button
              key={item.id}
              className={cn(
                'p-3 rounded-xl transition-all duration-200',
                'hover:bg-white/10 dark:hover:bg-white/5',
                activeId === item.id && 'bg-gradient-to-r from-violet-500/20 to-purple-500/20',
                activeId === item.id && 'text-violet-400'
              )}
              onClick={() => handleItemClick(item)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              title={item.label}
            >
              {item.icon}
            </motion.button>
          ))}
        </div>
      </GlassmorphicCard>
    )
  }

  if (variant === 'horizontal') {
    return (
      <GlassmorphicCard
        variant="medium"
        className={cn(
          'sticky top-0 z-40 px-6 py-4',
          className
        )}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            {items.map(item => (
              <div key={item.id} className="relative group">
                <motion.button
                  className={cn(
                    'flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200',
                    'hover:bg-white/10 dark:hover:bg-white/5',
                    activeId === item.id && 'bg-gradient-to-r from-violet-500/20 to-purple-500/20',
                    activeId === item.id && 'text-violet-400'
                  )}
                  onClick={() => handleItemClick(item)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {item.icon}
                  <span className="font-medium">{item.label}</span>
                  {item.badge && (
                    <span className="px-2 py-1 bg-violet-500 text-white text-xs rounded-full">
                      {item.badge}
                    </span>
                  )}
                  {item.children && item.children.length > 0 && (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </motion.button>

                {/* Dropdown Menu */}
                {item.children && item.children.length > 0 && (
                  <div className="absolute top-full left-0 mt-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <GlassmorphicCard variant="heavy" className="min-w-48 p-2">
                      {item.children.map(child => (
                        <motion.button
                          key={child.id}
                          className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-white/10 dark:hover:bg-white/5 transition-colors"
                          onClick={() => handleItemClick(child)}
                          whileHover={{ x: 4 }}
                        >
                          {child.icon}
                          <span>{child.label}</span>
                          {child.badge && (
                            <span className="ml-auto px-2 py-1 bg-violet-500 text-white text-xs rounded-full">
                              {child.badge}
                            </span>
                          )}
                        </motion.button>
                      ))}
                    </GlassmorphicCard>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile Menu Toggle */}
          {isMobile && (
            <IconButton
              variant="glass"
              size="sm"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
            </IconButton>
          )}
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobile && mobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-white/10"
            >
              <div className="space-y-2">
                {items.map(item => renderNavigationItem(item))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </GlassmorphicCard>
    )
  }

  // Vertical navigation
  return (
    <GlassmorphicCard
      variant="medium"
      className={cn(
        'h-full flex flex-col',
        collapsed ? 'w-16' : 'w-64',
        'transition-all duration-300 ease-out',
        className
      )}
    >
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <h2 className="font-semibold text-gray-900 dark:text-white">
              Navigation
            </h2>
          )}
          
          {onToggleCollapse && (
            <IconButton
              variant="glass"
              size="sm"
              onClick={onToggleCollapse}
              className="ml-auto"
            >
              <Menu className="w-4 h-4" />
            </IconButton>
          )}
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 p-4 space-y-2 overflow-y-auto">
        {items.map(item => renderNavigationItem(item))}
      </div>

      {/* Footer */}
      {!collapsed && (
        <div className="p-4 border-t border-white/10">
          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            © 2025 Course Platform
          </div>
        </div>
      )}
    </GlassmorphicCard>
  )
}

// Breadcrumb Navigation
export function GlassmorphicBreadcrumb({
  items,
  className
}: {
  items: Array<{ label: string; href?: string; onClick?: () => void }>
  className?: string
}) {
  return (
    <GlassmorphicCard
      variant="light"
      className={cn('px-4 py-2', className)}
    >
      <nav className="flex items-center space-x-2 text-sm">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <span className="text-gray-400">/</span>
            )}
            
            {item.href || item.onClick ? (
              <motion.button
                className="text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 transition-colors"
                onClick={item.onClick}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {item.label}
              </motion.button>
            ) : (
              <span className="text-gray-600 dark:text-gray-400 font-medium">
                {item.label}
              </span>
            )}
          </React.Fragment>
        ))}
      </nav>
    </GlassmorphicCard>
  )
}

// Tab Navigation
export function GlassmorphicTabs({
  tabs,
  activeTab,
  onTabChange,
  className
}: {
  tabs: Array<{ id: string; label: string; icon?: React.ReactNode; badge?: string | number }>
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}) {
  return (
    <GlassmorphicCard
      variant="light"
      className={cn('p-1', className)}
    >
      <div className="flex space-x-1">
        {tabs.map(tab => (
          <motion.button
            key={tab.id}
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200',
              'font-medium text-sm',
              activeTab === tab.id
                ? 'bg-white/20 text-violet-600 dark:text-violet-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/10'
            )}
            onClick={() => onTabChange(tab.id)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {tab.icon}
            <span>{tab.label}</span>
            {tab.badge && (
              <span className="px-2 py-1 bg-violet-500 text-white text-xs rounded-full">
                {tab.badge}
              </span>
            )}
          </motion.button>
        ))}
      </div>
    </GlassmorphicCard>
  )
}
