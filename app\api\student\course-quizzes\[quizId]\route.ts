import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const startQuizSchema = z.object({
  courseId: z.string().min(1, 'Course ID is required')
})

const submitQuizSchema = z.object({
  attemptId: z.string().min(1, 'Attempt ID is required'),
  answers: z.record(z.string(), z.string()), // questionId -> answer
  timeSpent: z.number().int().min(0).default(0) // seconds
})

// GET /api/student/course-quizzes/[quizId] - Get course quiz for student (start screen)
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user, params }) => {
    try {
      const resolvedParams = await params
      const quizId = resolvedParams?.quizId as string

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Get quiz with course info
      const quiz = await prisma.courseQuiz.findUnique({
        where: { id: quizId },
        include: {
          course: {
            select: {
              id: true,
              title: true,
              instructorId: true,
              status: true,
              isPublished: true
            }
          },
          questions: {
            select: {
              id: true,
              type: true,
              points: true
            }
          },
          _count: {
            select: {
              questions: true
            }
          }
        }
      })

      if (!quiz) {
        return APIResponse.error('Quiz not found', 404)
      }

      if (!quiz.isPublished) {
        return APIResponse.error('Quiz is not available', 403)
      }

      if (!quiz.course.isPublished) {
        return APIResponse.error('Course is not available', 403)
      }

      // Check if user is enrolled in the course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId: quiz.course.id
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('You must be enrolled in the course to access this quiz', 403)
      }

      // Get user's previous attempts
      const attempts = await prisma.courseQuizAttempt.findMany({
        where: {
          userId: user.id,
          quizId: quiz.id
        },
        orderBy: { startedAt: 'desc' },
        take: 5
      })

      // Check if user has reached max attempts
      if (quiz.maxAttempts > 0 && attempts.length >= quiz.maxAttempts) {
        const hasIncompleteAttempt = attempts.some(attempt => !attempt.isCompleted)
        if (!hasIncompleteAttempt) {
          return APIResponse.error('Maximum attempts reached for this quiz', 403)
        }
      }

      // Calculate total points
      const totalPoints = quiz.questions.reduce((sum, q) => sum + (q.points || 1), 0)

      return APIResponse.success({
        quiz: {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          instructions: quiz.instructions,
          timeLimit: quiz.timeLimit,
          maxAttempts: quiz.maxAttempts,
          passingScore: quiz.passingScore,
          questionCount: quiz._count.questions,
          totalPoints,
          course: {
            id: quiz.course.id,
            title: quiz.course.title
          }
        },
        attempts: attempts.map(attempt => ({
          id: attempt.id,
          score: attempt.score,
          startedAt: attempt.startedAt.toISOString(),
          completedAt: attempt.completedAt?.toISOString(),
          isCompleted: attempt.isCompleted
        })),
        canAttempt: quiz.maxAttempts === 0 || attempts.length < quiz.maxAttempts || attempts.some(a => !a.isCompleted),
        hasIncompleteAttempt: attempts.some(attempt => !attempt.isCompleted)
      })

    } catch (error) {
      console.error('Error fetching course quiz:', error)
      return APIResponse.error('Failed to fetch course quiz', 500)
    }
  }
)

// POST /api/student/course-quizzes/[quizId] - Start a new course quiz attempt
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: startQuizSchema
  },
  async (request: NextRequest, { validatedBody, user, params }) => {
    try {
      const resolvedParams = await params
      const quizId = resolvedParams?.quizId as string
      const { courseId } = validatedBody

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Verify quiz exists and is published
      const quiz = await prisma.courseQuiz.findUnique({
        where: { 
          id: quizId,
          courseId
        },
        include: {
          questions: {
            orderBy: { order: 'asc' },
            select: {
              id: true,
              type: true,
              question: true,
              options: true,
              points: true,
              order: true
              // Don't include correctAnswer or explanation
            }
          },
          _count: {
            select: { questions: true }
          }
        }
      })

      if (!quiz || !quiz.isPublished) {
        return APIResponse.error('Quiz not found or not available', 404)
      }

      // Check enrollment
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('You must be enrolled in the course', 403)
      }

      // Check for existing incomplete attempt
      const existingAttempt = await prisma.courseQuizAttempt.findFirst({
        where: {
          userId: user.id,
          quizId: quiz.id,
          isCompleted: false
        }
      })

      if (existingAttempt) {
        // Return existing attempt with questions
        return APIResponse.success({
          attemptId: existingAttempt.id,
          quiz: {
            id: quiz.id,
            title: quiz.title,
            description: quiz.description,
            instructions: quiz.instructions,
            timeLimit: quiz.timeLimit,
            questionCount: quiz._count.questions,
            questions: quiz.questions
          },
          startedAt: existingAttempt.startedAt.toISOString(),
          answers: existingAttempt.answers || {}
        })
      }

      // Check max attempts
      const attemptCount = await prisma.courseQuizAttempt.count({
        where: {
          userId: user.id,
          quizId: quiz.id
        }
      })

      if (quiz.maxAttempts > 0 && attemptCount >= quiz.maxAttempts) {
        return APIResponse.error('Maximum attempts reached', 403)
      }

      // Create new attempt
      const newAttempt = await prisma.courseQuizAttempt.create({
        data: {
          userId: user.id,
          quizId: quiz.id,
          startedAt: new Date(),
          answers: {},
          score: 0,
          isCompleted: false
        }
      })

      return APIResponse.success({
        attemptId: newAttempt.id,
        quiz: {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          instructions: quiz.instructions,
          timeLimit: quiz.timeLimit,
          questionCount: quiz._count.questions,
          questions: quiz.questions
        },
        startedAt: newAttempt.startedAt.toISOString(),
        answers: {}
      })

    } catch (error) {
      console.error('Error starting course quiz:', error)
      return APIResponse.error('Failed to start course quiz', 500)
    }
  }
)

// PUT /api/student/course-quizzes/[quizId] - Submit course quiz answers
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: submitQuizSchema
  },
  async (request: NextRequest, { validatedBody, user, params }) => {
    try {
      const resolvedParams = await params
      const quizId = resolvedParams?.quizId as string
      const { attemptId, answers, timeSpent } = validatedBody

      if (!quizId) {
        return APIResponse.error('Quiz ID is required', 400)
      }

      // Get attempt with quiz and questions
      const attempt = await prisma.courseQuizAttempt.findUnique({
        where: { 
          id: attemptId,
          userId: user.id,
          quizId: quizId
        },
        include: {
          quiz: {
            include: {
              questions: {
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!attempt) {
        return APIResponse.error('Quiz attempt not found', 404)
      }

      if (attempt.isCompleted) {
        return APIResponse.error('Quiz attempt already completed', 400)
      }

      // Calculate score
      let totalScore = 0
      let correctAnswers = 0
      const questionResults = []

      for (const question of attempt.quiz.questions) {
        const userAnswer = answers[question.id]
        const isCorrect = userAnswer === question.correctAnswer
        
        if (isCorrect) {
          totalScore += question.points || 1
          correctAnswers++
        }

        questionResults.push({
          questionId: question.id,
          userAnswer,
          correctAnswer: question.correctAnswer,
          isCorrect,
          points: isCorrect ? (question.points || 1) : 0
        })
      }

      const totalPossiblePoints = attempt.quiz.questions.reduce((sum, q) => sum + (q.points || 1), 0)
      const percentage = totalPossiblePoints > 0 ? Math.round((totalScore / totalPossiblePoints) * 100) : 0

      // Update attempt
      const updatedAttempt = await prisma.courseQuizAttempt.update({
        where: { id: attemptId },
        data: {
          answers,
          score: totalScore,
          percentage,
          timeSpent,
          completedAt: new Date(),
          isCompleted: true
        }
      })

      // Check if passed
      const passed = percentage >= attempt.quiz.passingScore

      return APIResponse.success({
        attemptId: updatedAttempt.id,
        score: totalScore,
        percentage,
        totalQuestions: attempt.quiz.questions.length,
        correctAnswers,
        passed,
        passingScore: attempt.quiz.passingScore,
        timeSpent,
        completedAt: updatedAttempt.completedAt?.toISOString(),
        questionResults
      })

    } catch (error) {
      console.error('Error submitting course quiz:', error)
      return APIResponse.error('Failed to submit course quiz', 500)
    }
  }
)
