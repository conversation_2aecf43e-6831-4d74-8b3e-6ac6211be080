'use client'

import React, { useState, useCallback } from 'react'
import { motion, AnimatePresence, Reorder } from 'framer-motion'
import { 
  Plus, 
  GripVertical, 
  ChevronDown, 
  ChevronRight,
  Edit3,
  Trash2,
  Video,
  FileText,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'
import { toast } from 'sonner'
import { VideoUpload } from './video-upload'

interface Course {
  id: string
  title: string
  sections: CourseSection[]
}

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  isPublished: boolean
  isFree: boolean
  topics: CourseTopic[]
}

interface CourseTopic {
  id: string
  title: string
  description?: string
  order: number
  type: 'VIDEO' | 'DOCUMENT' | 'QUIZ' | 'ASSIGNMENT'
  duration?: number
  isPublished: boolean
  isFree: boolean
  content: CourseContent[]
}

interface CourseContent {
  id: string
  type: 'VIDEO' | 'DOCUMENT' | 'QUIZ' | 'ASSIGNMENT' | 'RESOURCE'
  title: string
  description?: string
  videoUrl?: string
  thumbnailUrl?: string
  duration?: number
  fileUrl?: string
  fileName?: string
  order: number
}

interface CourseStructureEditorProps {
  course: Course
  onCourseUpdate: (course: Partial<Course>) => void
}

export function CourseStructureEditor({ course, onCourseUpdate }: CourseStructureEditorProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())
  const [editingSection, setEditingSection] = useState<string | null>(null)
  const [editingTopic, setEditingTopic] = useState<string | null>(null)
  const [showVideoUpload, setShowVideoUpload] = useState<{
    courseId: string
    sectionId: string
    topicId: string
  } | null>(null)

  // Toggle section expansion
  const toggleSection = useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }, [])

  // Add new section
  const addSection = async () => {
    try {
      const response = await fetch(`/api/instructor/courses/${course.id}/sections`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: 'New Section',
          description: '',
          isPublished: false,
          isFree: false
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create section')
      }

      const data = await response.json()
      const newSection = data.section

      // Update course with new section
      onCourseUpdate({
        sections: [...course.sections, newSection]
      })

      // Expand the new section and start editing
      setExpandedSections(prev => new Set([...prev, newSection.id]))
      setEditingSection(newSection.id)
      
      toast.success('Section added successfully!')
    } catch (error) {
      console.error('Error adding section:', error)
      toast.error('Failed to add section')
    }
  }

  // Update section
  const updateSection = async (sectionId: string, updates: Partial<CourseSection>) => {
    try {
      const response = await fetch(`/api/instructor/courses/${course.id}/sections/${sectionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error('Failed to update section')
      }

      const data = await response.json()
      const updatedSection = data.section

      // Update course sections
      const updatedSections = course.sections.map(section =>
        section.id === sectionId ? updatedSection : section
      )

      onCourseUpdate({ sections: updatedSections })
      toast.success('Section updated successfully!')
    } catch (error) {
      console.error('Error updating section:', error)
      toast.error('Failed to update section')
    }
  }

  // Delete section
  const deleteSection = async (sectionId: string) => {
    if (!confirm('Are you sure you want to delete this section? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/instructor/courses/${course.id}/sections/${sectionId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete section')
      }

      // Remove section from course
      const updatedSections = course.sections.filter(section => section.id !== sectionId)
      onCourseUpdate({ sections: updatedSections })
      
      toast.success('Section deleted successfully!')
    } catch (error) {
      console.error('Error deleting section:', error)
      toast.error('Failed to delete section')
    }
  }

  // Add new topic - placeholder for now since API doesn't exist yet
  const addTopic = async (sectionId: string) => {
    // Create a temporary topic for UI purposes
    const newTopic = {
      id: `temp-${Date.now()}`,
      title: 'New Lesson',
      description: '',
      order: 1,
      type: 'VIDEO' as const,
      isPublished: false,
      isFree: false,
      content: []
    }

    // Update section with new topic
    const updatedSections = course.sections.map(section => {
      if (section.id === sectionId) {
        return {
          ...section,
          topics: [...(section.topics || []), newTopic]
        }
      }
      return section
    })

    onCourseUpdate({ sections: updatedSections })
    setEditingTopic(newTopic.id)

    toast.success('Lesson added successfully!')
  }

  // Reorder sections
  const reorderSections = async (newOrder: CourseSection[]) => {
    try {
      const sectionsWithOrder = newOrder.map((section, index) => ({
        id: section.id,
        order: index + 1
      }))

      const response = await fetch(`/api/instructor/courses/${course.id}/sections`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sections: sectionsWithOrder
        })
      })

      if (!response.ok) {
        throw new Error('Failed to reorder sections')
      }

      onCourseUpdate({ sections: newOrder })
    } catch (error) {
      console.error('Error reordering sections:', error)
      toast.error('Failed to reorder sections')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Course Structure
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Organize your course content into sections and lessons
          </p>
        </div>

        <button
          onClick={addSection}
          className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
        >
          <Plus className="w-4 h-4" />
          <span>Add Section</span>
        </button>
      </div>

      {/* Course Structure */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700">
        {course.sections.length === 0 ? (
          <div className="p-12 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Start Building Your Course
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Create your first section to begin organizing your course content
            </p>
            <button
              onClick={addSection}
              className="px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
            >
              Create First Section
            </button>
          </div>
        ) : (
          <Reorder.Group
            axis="y"
            values={course.sections}
            onReorder={reorderSections}
            className="divide-y divide-gray-200 dark:divide-gray-700"
          >
            <AnimatePresence>
              {course.sections.map((section, sectionIndex) => (
                <Reorder.Item
                  key={section.id}
                  value={section}
                  className="p-6"
                >
                  <SectionEditor
                    section={section}
                    sectionIndex={sectionIndex}
                    courseId={course.id}
                    isExpanded={expandedSections.has(section.id)}
                    isEditing={editingSection === section.id}
                    onToggle={() => toggleSection(section.id)}
                    onEdit={() => setEditingSection(section.id)}
                    onSave={(updates: Partial<CourseSection>) => {
                      updateSection(section.id, updates)
                      setEditingSection(null)
                    }}
                    onCancel={() => setEditingSection(null)}
                    onDelete={() => deleteSection(section.id)}
                    onAddTopic={() => addTopic(section.id)}
                    onShowVideoUpload={(topicId: string) => setShowVideoUpload({
                      courseId: course.id,
                      sectionId: section.id,
                      topicId
                    })}
                  />
                </Reorder.Item>
              ))}
            </AnimatePresence>
          </Reorder.Group>
        )}
      </div>

      {/* Video Upload Modal */}
      <AnimatePresence>
        {showVideoUpload && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowVideoUpload(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  Upload Video Content
                </h3>
                <button
                  onClick={() => setShowVideoUpload(null)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  ×
                </button>
              </div>

              <VideoUpload
                courseId={showVideoUpload.courseId}
                sectionId={showVideoUpload.sectionId}
                topicId={showVideoUpload.topicId}
                onUploadComplete={(content) => {
                  toast.success('Video uploaded successfully!')
                  setShowVideoUpload(null)
                  // Refresh course data
                  window.location.reload()
                }}
                onUploadError={(error) => {
                  toast.error(error)
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Section Editor Component (to be implemented separately due to length)
function SectionEditor({ 
  section, 
  sectionIndex, 
  courseId,
  isExpanded, 
  isEditing, 
  onToggle, 
  onEdit, 
  onSave, 
  onCancel, 
  onDelete,
  onAddTopic,
  onShowVideoUpload
}: any) {
  const [title, setTitle] = useState(section.title)
  const [description, setDescription] = useState(section.description || '')

  const handleSave = () => {
    onSave({ title, description })
  }

  return (
    <div className="space-y-4">
      {/* Section Header */}
      <div className="flex items-center space-x-3">
        <GripVertical className="w-5 h-5 text-gray-400 cursor-grab" />
        
        <button
          onClick={onToggle}
          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
        >
          {isExpanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
        </button>

        <div className="flex-1">
          {isEditing ? (
            <div className="space-y-3">
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Section title..."
              />
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                placeholder="Section description..."
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  className="px-3 py-1 bg-violet-600 text-white rounded text-sm hover:bg-violet-700 transition-colors"
                >
                  Save
                </button>
                <button
                  onClick={onCancel}
                  className="px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Section {sectionIndex + 1}: {section.title}
              </h3>
              {section.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {section.description}
                </p>
              )}
            </div>
          )}
        </div>

        {!isEditing && (
          <div className="flex items-center space-x-2">
            <button
              onClick={onEdit}
              className="p-2 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
            >
              <Edit3 className="w-4 h-4" />
            </button>
            <button
              onClick={onDelete}
              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Section Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="ml-8 space-y-3"
          >
            {/* Topics */}
            {section.topics?.map((topic: CourseTopic, topicIndex: number) => (
              <div
                key={topic.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <GripVertical className="w-4 h-4 text-gray-400 cursor-grab" />
                
                {topic.type === 'VIDEO' ? (
                  <Video className="w-4 h-4 text-violet-500" />
                ) : (
                  <FileText className="w-4 h-4 text-blue-500" />
                )}

                <div className="flex-1">
                  <div className="font-medium text-gray-900 dark:text-white">
                    Lesson {topicIndex + 1}: {topic.title}
                  </div>
                  {topic.duration && (
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {Math.round(topic.duration / 60)} minutes
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  {topic.isPublished ? (
                    <Eye className="w-4 h-4 text-green-500" />
                  ) : (
                    <EyeOff className="w-4 h-4 text-gray-400" />
                  )}
                  
                  <button
                    onClick={() => onShowVideoUpload(topic.id)}
                    className="p-1 text-gray-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}

            {/* Add Topic Button */}
            <button
              onClick={onAddTopic}
              className="flex items-center space-x-2 p-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400 hover:border-violet-400 hover:text-violet-600 dark:hover:text-violet-400 transition-colors w-full"
            >
              <Plus className="w-4 h-4" />
              <span>Add Lesson</span>
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
