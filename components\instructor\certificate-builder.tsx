'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Award, 
  Plus, 
  Eye, 
  Edit3, 
  Trash2, 
  Save, 
  Copy,
  Download,
  Users,
  Settings,
  Palette,
  Type,
  Layout,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface CertificateTemplate {
  id: string
  name: string
  description?: string
  design: {
    backgroundColor: string
    primaryColor: string
    secondaryColor: string
    fontFamily: string
    layout: 'modern' | 'classic' | 'elegant' | 'minimal'
    showLogo: boolean
    showBorder: boolean
    showSignature: boolean
  }
  content: {
    title: string
    subtitle?: string
    bodyText: string
    footerText?: string
    instructorTitle: string
    organizationName?: string
  }
  requirements: {
    minCompletionPercentage: number
    requireAllQuizzesPassed: boolean
    minQuizScore: number
    requireAllAssignmentsCompleted: boolean
  }
  isActive: boolean
  createdAt: string
  updatedAt: string
  certificateCount: number
}

interface CertificateBuilderProps {
  courseId: string
  courseName: string
  instructorName: string
}

export function CertificateBuilder({ courseId, courseName, instructorName }: CertificateBuilderProps) {
  const [templates, setTemplates] = useState<CertificateTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [activeTab, setActiveTab] = useState<'design' | 'content' | 'requirements'>('design')

  // Fetch templates
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/courses/${courseId}/certificates`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch templates')
        }

        const data = await response.json()
        setTemplates(data.templates)
        
        // Select active template or first template
        const activeTemplate = data.templates.find((t: CertificateTemplate) => t.isActive)
        if (activeTemplate) {
          setSelectedTemplate(activeTemplate)
        } else if (data.templates.length > 0) {
          setSelectedTemplate(data.templates[0])
        }
      } catch (error) {
        console.error('Error fetching templates:', error)
        toast.error('Failed to load certificate templates')
      } finally {
        setIsLoading(false)
      }
    }

    if (courseId) {
      fetchTemplates()
    }
  }, [courseId])

  // Create new template
  const createTemplate = async () => {
    try {
      setIsCreating(true)
      const newTemplate = {
        name: 'New Certificate Template',
        description: 'Custom certificate template',
        design: {
          backgroundColor: '#ffffff',
          primaryColor: '#6366f1',
          secondaryColor: '#8b5cf6',
          fontFamily: 'Inter',
          layout: 'modern' as const,
          showLogo: true,
          showBorder: true,
          showSignature: true
        },
        content: {
          title: 'Certificate of Completion',
          subtitle: 'This certifies that',
          bodyText: 'This is to certify that {studentName} has successfully completed the course {courseName} on {completionDate}.',
          footerText: 'Congratulations on your achievement!',
          instructorTitle: 'Course Instructor',
          organizationName: 'Learning Platform'
        },
        requirements: {
          minCompletionPercentage: 80,
          requireAllQuizzesPassed: false,
          minQuizScore: 70,
          requireAllAssignmentsCompleted: false
        },
        isActive: templates.length === 0 // First template is active by default
      }

      const response = await fetch(`/api/courses/${courseId}/certificates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newTemplate)
      })

      if (!response.ok) {
        throw new Error('Failed to create template')
      }

      const data = await response.json()
      const createdTemplate = data.template

      setTemplates(prev => [createdTemplate, ...prev])
      setSelectedTemplate(createdTemplate)
      toast.success('Certificate template created successfully!')
    } catch (error) {
      console.error('Error creating template:', error)
      toast.error('Failed to create template')
    } finally {
      setIsCreating(false)
    }
  }

  // Update template
  const updateTemplate = async (templateId: string, updates: Partial<CertificateTemplate>) => {
    try {
      const response = await fetch(`/api/courses/${courseId}/certificates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      if (!response.ok) {
        throw new Error('Failed to update template')
      }

      const data = await response.json()
      const updatedTemplate = data.template

      setTemplates(prev => prev.map(t => t.id === templateId ? updatedTemplate : t))
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(updatedTemplate)
      }
      
      toast.success('Template updated successfully!')
    } catch (error) {
      console.error('Error updating template:', error)
      toast.error('Failed to update template')
    }
  }

  // Delete template
  const deleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/courses/${courseId}/certificates/${templateId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete template')
      }

      setTemplates(prev => prev.filter(t => t.id !== templateId))
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(templates.find(t => t.id !== templateId) || null)
      }
      
      toast.success('Template deleted successfully!')
    } catch (error) {
      console.error('Error deleting template:', error)
      toast.error('Failed to delete template')
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading certificate templates...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Certificate Builder
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Create and customize certificates for course completion
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {selectedTemplate && (
            <button
              onClick={() => setShowPreview(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <Eye className="w-4 h-4" />
              <span>Preview</span>
            </button>
          )}

          <button
            onClick={createTemplate}
            disabled={isCreating}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            <Plus className="w-4 h-4" />
            <span>{isCreating ? 'Creating...' : 'New Template'}</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Template List */}
        <div className="lg:col-span-1">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Templates ({templates.length})
              </h3>
            </div>

            <div className="p-4">
              {templates.length === 0 ? (
                <div className="text-center py-8">
                  <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    No Templates Yet
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Create your first certificate template
                  </p>
                  <button
                    onClick={createTemplate}
                    disabled={isCreating}
                    className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 disabled:opacity-50 transition-colors"
                  >
                    Create Template
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  {templates.map((template) => (
                    <motion.button
                      key={template.id}
                      onClick={() => setSelectedTemplate(template)}
                      className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                        selectedTemplate?.id === template.id
                          ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{template.name}</div>
                          <div className={`text-sm mt-1 flex items-center space-x-2 ${
                            selectedTemplate?.id === template.id 
                              ? 'text-white/80' 
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span>{template.certificateCount} issued</span>
                            {template.isActive && (
                              <CheckCircle className="w-3 h-3 text-green-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Template Editor */}
        <div className="lg:col-span-3">
          {selectedTemplate ? (
            <TemplateEditor
              template={selectedTemplate}
              courseId={courseId}
              courseName={courseName}
              instructorName={instructorName}
              onUpdate={(updates) => updateTemplate(selectedTemplate.id, updates)}
              onDelete={() => deleteTemplate(selectedTemplate.id)}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
            />
          ) : (
            <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
              <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Select a Template to Edit
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Choose a template from the list to start customizing
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Preview Modal */}
      <AnimatePresence>
        {showPreview && selectedTemplate && (
          <CertificatePreviewModal
            template={selectedTemplate}
            courseName={courseName}
            instructorName={instructorName}
            onClose={() => setShowPreview(false)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// Template Editor Component (placeholder)
interface TemplateEditorProps {
  template: CertificateTemplate
  courseId: string
  courseName: string
  instructorName: string
  onUpdate: (updates: Partial<CertificateTemplate>) => void
  onDelete: () => void
  activeTab: 'design' | 'content' | 'requirements'
  setActiveTab: (tab: 'design' | 'content' | 'requirements') => void
}

function TemplateEditor({ 
  template, 
  courseId,
  courseName,
  instructorName,
  onUpdate, 
  onDelete,
  activeTab,
  setActiveTab
}: TemplateEditorProps) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700">
      {/* Template Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              {template.name}
            </h3>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center space-x-1">
                <Award className="w-4 h-4" />
                <span>{template.certificateCount} certificates issued</span>
              </div>
              {template.isActive && (
                <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                  <CheckCircle className="w-4 h-4" />
                  <span>Active Template</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <button
              onClick={() => onUpdate({ isActive: !template.isActive })}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                template.isActive
                  ? 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
              }`}
            >
              {template.isActive ? 'Deactivate' : 'Activate'}
            </button>

            <button
              onClick={onDelete}
              className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mt-6 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {[
            { id: 'design', label: 'Design', icon: Palette },
            { id: 'content', label: 'Content', icon: Type },
            { id: 'requirements', label: 'Requirements', icon: CheckCircle }
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 flex-1 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          {activeTab === 'design' && (
            <motion.div
              key="design"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <DesignEditor template={template} onUpdate={onUpdate} />
            </motion.div>
          )}

          {activeTab === 'content' && (
            <motion.div
              key="content"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <ContentEditor template={template} onUpdate={onUpdate} />
            </motion.div>
          )}

          {activeTab === 'requirements' && (
            <motion.div
              key="requirements"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <RequirementsEditor template={template} onUpdate={onUpdate} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}

// Placeholder components (to be implemented)
function DesignEditor({ template, onUpdate }: any) {
  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-gray-900 dark:text-white">Design Settings</h4>
      <p className="text-gray-600 dark:text-gray-400">Customize the visual appearance of your certificate</p>
      {/* Design form to be implemented */}
    </div>
  )
}

function ContentEditor({ template, onUpdate }: any) {
  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-gray-900 dark:text-white">Content Settings</h4>
      <p className="text-gray-600 dark:text-gray-400">Edit the text content and layout of your certificate</p>
      {/* Content form to be implemented */}
    </div>
  )
}

function RequirementsEditor({ template, onUpdate }: any) {
  return (
    <div className="space-y-6">
      <h4 className="font-semibold text-gray-900 dark:text-white">Certificate Requirements</h4>
      <p className="text-gray-600 dark:text-gray-400">Set the criteria students must meet to earn this certificate</p>
      {/* Requirements form to be implemented */}
    </div>
  )
}

function CertificatePreviewModal({ template, courseName, instructorName, onClose }: any) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Certificate Preview
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
          >
            ×
          </button>
        </div>
        
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-8">
          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <h1 className="text-3xl font-bold text-violet-600 mb-4">
              {template.content.title}
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              This is to certify that <strong>John Doe</strong> has successfully completed the course <strong>{courseName}</strong>
            </p>
            <div className="mt-8 flex justify-between items-end">
              <div>
                <div className="w-32 h-0.5 bg-gray-400 mb-2"></div>
                <p className="text-sm text-gray-600">{instructorName}<br/>Course Instructor</p>
              </div>
              <div>
                <div className="w-32 h-0.5 bg-gray-400 mb-2"></div>
                <p className="text-sm text-gray-600">{new Date().toLocaleDateString()}<br/>Date of Completion</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
