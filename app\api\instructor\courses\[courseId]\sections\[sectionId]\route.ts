import { NextRequest } from 'next/server'
import { createAP<PERSON>Hand<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateSectionSchema = z.object({
  title: z.string().min(1).optional(),
  description: z.string().optional(),
  order: z.number().int().min(0).optional(),
  isPublished: z.boolean().optional(),
  isFree: z.boolean().optional()
})

// GET /api/instructor/courses/[courseId]/sections/[sectionId] - Get section details
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const sectionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get section with topics and content
      const section = await prisma.courseSection.findUnique({
        where: { 
          id: sectionId,
          courseId
        },
        include: {
          topics: {
            orderBy: { order: 'asc' },
            include: {
              content: {
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // Calculate section metrics
      const totalTopics = section.topics.length
      const totalDuration = section.topics.reduce(
        (sum, topic) => sum + topic.content.reduce(
          (contentSum, content) => contentSum + (content.duration || 0),
          0
        ),
        0
      )
      const totalContent = section.topics.reduce(
        (sum, topic) => sum + topic.content.length,
        0
      )

      return APIResponse.success({
        section: {
          ...section,
          totalTopics,
          totalDuration: Math.round(totalDuration / 60), // Convert to minutes
          totalContent
        }
      })

    } catch (error) {
      console.error('Error fetching section:', error)
      return APIResponse.error(
        'Failed to fetch section: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/instructor/courses/[courseId]/sections/[sectionId] - Update section
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR',
    validateBody: updateSectionSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const urlParts = request.url.split('/')
      const sectionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if section exists
      const existingSection = await prisma.courseSection.findUnique({
        where: { 
          id: sectionId,
          courseId
        }
      })

      if (!existingSection) {
        return APIResponse.error('Section not found', 404)
      }

      // Update section
      const updatedSection = await prisma.courseSection.update({
        where: { id: sectionId },
        data: validatedBody,
        include: {
          topics: {
            orderBy: { order: 'asc' },
            include: {
              content: {
                orderBy: { order: 'asc' }
              }
            }
          }
        }
      })

      return APIResponse.success({
        message: 'Section updated successfully',
        section: updatedSection
      })

    } catch (error) {
      console.error('Error updating section:', error)
      return APIResponse.error(
        'Failed to update section: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// DELETE /api/instructor/courses/[courseId]/sections/[sectionId] - Delete section
export const DELETE = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'INSTRUCTOR'
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const sectionId = urlParts.pop()
      const courseId = urlParts.slice(-3, -2)[0]

      if (!courseId || !sectionId) {
        return APIResponse.error('Course ID and Section ID are required', 400)
      }

      // Verify course ownership
      const course = await prisma.course.findUnique({
        where: { 
          id: courseId,
          instructorId: user.id
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Check if section exists
      const section = await prisma.courseSection.findUnique({
        where: { 
          id: sectionId,
          courseId
        },
        include: {
          topics: {
            include: {
              content: true
            }
          }
        }
      })

      if (!section) {
        return APIResponse.error('Section not found', 404)
      }

      // TODO: Delete associated files from Bunny CDN
      // This would involve iterating through all content and deleting video/document files

      // Delete section (cascade will handle topics and content)
      await prisma.courseSection.delete({
        where: { id: sectionId }
      })

      // Reorder remaining sections
      const remainingSections = await prisma.courseSection.findMany({
        where: { courseId },
        orderBy: { order: 'asc' }
      })

      await prisma.$transaction(
        remainingSections.map((section, index) =>
          prisma.courseSection.update({
            where: { id: section.id },
            data: { order: index + 1 }
          })
        )
      )

      return APIResponse.success({
        message: 'Section deleted successfully'
      })

    } catch (error) {
      console.error('Error deleting section:', error)
      return APIResponse.error(
        'Failed to delete section: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
