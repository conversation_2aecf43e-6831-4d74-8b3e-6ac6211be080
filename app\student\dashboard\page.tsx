'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { 
  BookOpen, 
  Trophy, 
  Clock, 
  Target, 
  TrendingUp,
  Calendar,
  MessageSquare,
  Award,
  Play,
  BarChart3,
  Users,
  Star,
  Zap,
  CheckCircle
} from 'lucide-react'
import { toast } from 'sonner'

// Import our enhanced components
import { EnhancedCourseDashboard } from '@/components/student/enhanced-course-dashboard'
import { CertificateGallery } from '@/components/student/certificate-gallery'

interface CourseProgress {
  courseId: string
  title: string
  description: string
  thumbnailImage?: string
  instructor: {
    id: string
    name: string
    image?: string
  }
  progress: {
    completedTopics: number
    totalTopics: number
    completionPercentage: number
    timeSpent: number
    lastAccessedAt: string
    currentTopic?: {
      id: string
      title: string
      sectionTitle: string
    }
  }
  stats: {
    totalQuizzes: number
    completedQuizzes: number
    averageQuizScore: number
    discussionPosts: number
    certificates: number
  }
  nextMilestone?: {
    type: 'quiz' | 'assignment' | 'certificate' | 'completion'
    title: string
    description: string
    progress: number
  }
}

interface DashboardStats {
  totalCourses: number
  completedCourses: number
  inProgressCourses: number
  totalCertificates: number
  totalTimeSpent: number
  averageScore: number
  streakDays: number
  weeklyGoalProgress: number
}

type DashboardView = 'overview' | 'courses' | 'certificates' | 'analytics'

export default function StudentDashboard() {
  const router = useRouter()
  const [activeView, setActiveView] = useState<DashboardView>('overview')
  const [courses, setCourses] = useState<CourseProgress[]>([])
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<any[]>([])

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true)
        
        // Fetch courses
        const coursesResponse = await fetch('/api/student/courses')
        if (coursesResponse.ok) {
          const coursesData = await coursesResponse.json()
          setCourses(coursesData.courses)
        }

        // Fetch dashboard stats
        const statsResponse = await fetch('/api/student/dashboard/stats')
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData.stats)
        }

        // Fetch recent activity
        const activityResponse = await fetch('/api/student/dashboard/activity')
        if (activityResponse.ok) {
          const activityData = await activityResponse.json()
          setRecentActivity(activityData.activities)
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        toast.error('Failed to load dashboard data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [])

  // Handle course selection
  const handleCourseSelect = (courseId: string) => {
    router.push(`/student/courses/${courseId}`)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-violet-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Student Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Welcome back! Continue your learning journey
              </p>
            </div>

            {/* Navigation Tabs */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              {[
                { id: 'overview', label: 'Overview', icon: BarChart3 },
                { id: 'courses', label: 'My Courses', icon: BookOpen },
                { id: 'certificates', label: 'Certificates', icon: Award },
                { id: 'analytics', label: 'Analytics', icon: TrendingUp }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveView(tab.id as DashboardView)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                      activeView === tab.id
                        ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {activeView === 'overview' && (
          <DashboardOverview
            courses={courses}
            stats={stats}
            recentActivity={recentActivity}
            onCourseSelect={handleCourseSelect}
          />
        )}

        {activeView === 'courses' && (
          <EnhancedCourseDashboard
            courses={courses}
            onCourseSelect={handleCourseSelect}
          />
        )}

        {activeView === 'certificates' && (
          <CertificateGallery />
        )}

        {activeView === 'analytics' && (
          <StudentAnalytics
            courses={courses}
            stats={stats}
          />
        )}
      </div>
    </div>
  )
}

// Dashboard Overview Component
interface DashboardOverviewProps {
  courses: CourseProgress[]
  stats: DashboardStats | null
  recentActivity: any[]
  onCourseSelect: (courseId: string) => void
}

function DashboardOverview({ courses, stats, recentActivity, onCourseSelect }: DashboardOverviewProps) {
  const inProgressCourses = courses.filter(c => c.progress.completionPercentage > 0 && c.progress.completionPercentage < 100)
  const recentCourses = courses
    .sort((a, b) => new Date(b.progress.lastAccessedAt).getTime() - new Date(a.progress.lastAccessedAt).getTime())
    .slice(0, 3)

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Welcome back!</h2>
            <p className="text-violet-100">
              You're making great progress. Keep up the excellent work!
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{stats?.streakDays || 0}</div>
            <div className="text-violet-100 text-sm">day streak</div>
          </div>
        </div>

        {/* Quick Stats */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <BookOpen className="w-5 h-5" />
                <span className="font-medium">Courses</span>
              </div>
              <div className="text-2xl font-bold">{stats.totalCourses}</div>
              <div className="text-violet-100 text-sm">
                {stats.completedCourses} completed
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="w-5 h-5" />
                <span className="font-medium">Time Spent</span>
              </div>
              <div className="text-2xl font-bold">{Math.round(stats.totalTimeSpent / 60)}h</div>
              <div className="text-violet-100 text-sm">this month</div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Star className="w-5 h-5" />
                <span className="font-medium">Avg Score</span>
              </div>
              <div className="text-2xl font-bold">{stats.averageScore}%</div>
              <div className="text-violet-100 text-sm">across all quizzes</div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Trophy className="w-5 h-5" />
                <span className="font-medium">Certificates</span>
              </div>
              <div className="text-2xl font-bold">{stats.totalCertificates}</div>
              <div className="text-violet-100 text-sm">earned</div>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Continue Learning */}
        <div className="lg:col-span-2">
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Continue Learning
              </h3>
              <button
                onClick={() => {/* Navigate to courses */}}
                className="text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 text-sm font-medium"
              >
                View All
              </button>
            </div>

            <div className="space-y-4">
              {recentCourses.map((course) => (
                <motion.div
                  key={course.courseId}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors cursor-pointer"
                  onClick={() => onCourseSelect(course.courseId)}
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                    <BookOpen className="w-6 h-6 text-white" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 dark:text-white truncate">
                      {course.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {course.instructor.name}
                    </p>
                    <div className="flex items-center space-x-2 mt-2">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full"
                          style={{ width: `${course.progress.completionPercentage}%` }}
                        />
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {Math.round(course.progress.completionPercentage)}%
                      </span>
                    </div>
                  </div>

                  <button className="flex items-center space-x-2 px-4 py-2 bg-violet-100 text-violet-700 hover:bg-violet-200 dark:bg-violet-900/20 dark:text-violet-300 dark:hover:bg-violet-900/30 rounded-lg transition-colors">
                    <Play className="w-4 h-4" />
                    <span>Continue</span>
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              Recent Activity
            </h3>

            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900 dark:text-white">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {activity.timestamp}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No recent activity
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Student Analytics Component (placeholder)
function StudentAnalytics({ courses, stats }: { courses: CourseProgress[]; stats: DashboardStats | null }) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Learning Analytics
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Detailed insights into your learning progress and performance
        </p>
      </div>

      {/* Analytics content would go here */}
      <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
        <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Analytics Dashboard
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Detailed analytics and insights coming soon
        </p>
      </div>
    </div>
  )
}
