'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Bell, 
  BellOff, 
  Mail, 
  MessageSquare, 
  BookOpen, 
  Award, 
  Calendar,
  Zap,
  Settings,
  Save,
  Volume2,
  VolumeX,
  Smartphone,
  Monitor,
  Clock
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

import { GlassmorphicCard } from '@/components/ui/glassmorphic-card'
import { GlassmorphicButton } from '@/components/ui/glassmorphic-button'
import { GlassmorphicSelect } from '@/components/ui/glassmorphic-forms'

interface NotificationPreferences {
  email: {
    courseUpdates: boolean
    assignments: boolean
    discussions: boolean
    achievements: boolean
    systemUpdates: boolean
    marketing: boolean
  }
  push: {
    courseUpdates: boolean
    assignments: boolean
    discussions: boolean
    achievements: boolean
    systemUpdates: boolean
    liveQuiz: boolean
  }
  inApp: {
    courseUpdates: boolean
    assignments: boolean
    discussions: boolean
    achievements: boolean
    systemUpdates: boolean
    liveQuiz: boolean
  }
  sound: {
    enabled: boolean
    volume: number
    highPriorityOnly: boolean
  }
  quietHours: {
    enabled: boolean
    startTime: string
    endTime: string
    timezone: string
  }
  frequency: {
    digest: 'never' | 'daily' | 'weekly'
    digestTime: string
  }
}

const defaultPreferences: NotificationPreferences = {
  email: {
    courseUpdates: true,
    assignments: true,
    discussions: false,
    achievements: true,
    systemUpdates: true,
    marketing: false
  },
  push: {
    courseUpdates: true,
    assignments: true,
    discussions: true,
    achievements: true,
    systemUpdates: false,
    liveQuiz: true
  },
  inApp: {
    courseUpdates: true,
    assignments: true,
    discussions: true,
    achievements: true,
    systemUpdates: true,
    liveQuiz: true
  },
  sound: {
    enabled: true,
    volume: 50,
    highPriorityOnly: false
  },
  quietHours: {
    enabled: false,
    startTime: '22:00',
    endTime: '08:00',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  },
  frequency: {
    digest: 'never',
    digestTime: '09:00'
  }
}

interface NotificationSettingsProps {
  className?: string
}

export function NotificationSettings({ className = '' }: NotificationSettingsProps) {
  const { data: session } = useSession()
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [pushSupported, setPushSupported] = useState(false)

  // Check if push notifications are supported
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window && 'serviceWorker' in navigator) {
      setPushSupported(true)
    }
  }, [])

  // Load user preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const response = await fetch('/api/user/notification-preferences')
        if (response.ok) {
          const data = await response.json()
          setPreferences({ ...defaultPreferences, ...data.preferences })
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error)
      } finally {
        setLoading(false)
      }
    }

    if (session?.user?.id) {
      loadPreferences()
    }
  }, [session?.user?.id])

  // Save preferences
  const savePreferences = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/user/notification-preferences', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ preferences })
      })

      if (response.ok) {
        toast.success('Notification preferences saved')
      } else {
        throw new Error('Failed to save preferences')
      }
    } catch (error) {
      console.error('Error saving preferences:', error)
      toast.error('Failed to save preferences')
    } finally {
      setSaving(false)
    }
  }

  // Request push notification permission
  const requestPushPermission = async () => {
    if (!pushSupported) {
      toast.error('Push notifications are not supported in this browser')
      return
    }

    try {
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        toast.success('Push notifications enabled')
        // Register service worker and subscribe to push notifications
        // This would typically involve registering with a push service
      } else {
        toast.error('Push notification permission denied')
      }
    } catch (error) {
      console.error('Error requesting push permission:', error)
      toast.error('Failed to enable push notifications')
    }
  }

  // Update preference
  const updatePreference = (category: keyof NotificationPreferences, key: string, value: any) => {
    setPreferences(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  if (loading) {
    return (
      <GlassmorphicCard className={`p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="w-6 h-6 border-2 border-violet-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </GlassmorphicCard>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-violet-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Notification Settings
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Customize how and when you receive notifications
              </p>
            </div>
          </div>

          <GlassmorphicButton
            variant="primary"
            onClick={savePreferences}
            loading={saving}
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </GlassmorphicButton>
        </div>
      </GlassmorphicCard>

      {/* Email Notifications */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Mail className="w-5 h-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Email Notifications
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(preferences.email).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                {getNotificationIcon(key)}
                <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => updatePreference('email', key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
              </label>
            </div>
          ))}
        </div>
      </GlassmorphicCard>

      {/* Push Notifications */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Smartphone className="w-5 h-5 text-green-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Push Notifications
            </h3>
          </div>

          {pushSupported && Notification.permission !== 'granted' && (
            <GlassmorphicButton
              variant="outline"
              size="sm"
              onClick={requestPushPermission}
            >
              Enable Push
            </GlassmorphicButton>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(preferences.push).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                {getNotificationIcon(key)}
                <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => updatePreference('push', key, e.target.checked)}
                  className="sr-only peer"
                  disabled={!pushSupported || Notification.permission !== 'granted'}
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600 disabled:opacity-50"></div>
              </label>
            </div>
          ))}
        </div>
      </GlassmorphicCard>

      {/* In-App Notifications */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Monitor className="w-5 h-5 text-purple-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            In-App Notifications
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(preferences.inApp).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <div className="flex items-center space-x-3">
                {getNotificationIcon(key)}
                <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => updatePreference('inApp', key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
              </label>
            </div>
          ))}
        </div>
      </GlassmorphicCard>

      {/* Sound Settings */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          {preferences.sound.enabled ? (
            <Volume2 className="w-5 h-5 text-orange-500" />
          ) : (
            <VolumeX className="w-5 h-5 text-gray-500" />
          )}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Sound Settings
          </h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Enable notification sounds
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.sound.enabled}
                onChange={(e) => updatePreference('sound', 'enabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
            </label>
          </div>

          {preferences.sound.enabled && (
            <>
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-900 dark:text-white">
                  Volume: {preferences.sound.volume}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={preferences.sound.volume}
                  onChange={(e) => updatePreference('sound', 'volume', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
                />
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Only for high priority notifications
                </span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.sound.highPriorityOnly}
                    onChange={(e) => updatePreference('sound', 'highPriorityOnly', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
                </label>
              </div>
            </>
          )}
        </div>
      </GlassmorphicCard>

      {/* Quiet Hours */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Clock className="w-5 h-5 text-indigo-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Quiet Hours
          </h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Enable quiet hours
            </span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.quietHours.enabled}
                onChange={(e) => updatePreference('quietHours', 'enabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 dark:peer-focus:ring-violet-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-violet-600"></div>
            </label>
          </div>

          {preferences.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={preferences.quietHours.startTime}
                  onChange={(e) => updatePreference('quietHours', 'startTime', e.target.value)}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                  End Time
                </label>
                <input
                  type="time"
                  value={preferences.quietHours.endTime}
                  onChange={(e) => updatePreference('quietHours', 'endTime', e.target.value)}
                  className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>
            </div>
          )}
        </div>
      </GlassmorphicCard>

      {/* Digest Settings */}
      <GlassmorphicCard className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Mail className="w-5 h-5 text-teal-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Email Digest
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <GlassmorphicSelect
            label="Digest Frequency"
            options={[
              { value: 'never', label: 'Never' },
              { value: 'daily', label: 'Daily' },
              { value: 'weekly', label: 'Weekly' }
            ]}
            value={preferences.frequency.digest}
            onChange={(value) => updatePreference('frequency', 'digest', value)}
          />

          {preferences.frequency.digest !== 'never' && (
            <div>
              <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
                Delivery Time
              </label>
              <input
                type="time"
                value={preferences.frequency.digestTime}
                onChange={(e) => updatePreference('frequency', 'digestTime', e.target.value)}
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-violet-500"
              />
            </div>
          )}
        </div>
      </GlassmorphicCard>
    </div>
  )
}

// Helper function to get notification icons
function getNotificationIcon(type: string) {
  switch (type) {
    case 'courseUpdates':
      return <BookOpen className="w-4 h-4 text-blue-500" />
    case 'assignments':
      return <Calendar className="w-4 h-4 text-orange-500" />
    case 'discussions':
      return <MessageSquare className="w-4 h-4 text-green-500" />
    case 'achievements':
      return <Award className="w-4 h-4 text-yellow-500" />
    case 'systemUpdates':
      return <Settings className="w-4 h-4 text-gray-500" />
    case 'liveQuiz':
      return <Zap className="w-4 h-4 text-purple-500" />
    case 'marketing':
      return <Mail className="w-4 h-4 text-pink-500" />
    default:
      return <Bell className="w-4 h-4 text-gray-500" />
  }
}
