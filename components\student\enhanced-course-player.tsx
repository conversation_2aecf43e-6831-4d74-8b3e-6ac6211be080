'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Play, 
  Pause, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Volume2, 
  <PERSON><PERSON><PERSON>, 
  Maximize, 
  Book<PERSON>pen, 
  MessageSquare, 
  Award, 
  CheckCircle, 
  Clock, 
  Users, 
  Star,
  Download,
  Share2,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  FileText,
  HelpCircle,
  Target,
  BarChart3
} from 'lucide-react'
import { toast } from 'sonner'

interface CourseSection {
  id: string
  title: string
  description?: string
  order: number
  topics: CourseTopic[]
}

interface CourseTopic {
  id: string
  title: string
  description?: string
  type: 'VIDEO' | 'DOCUMENT' | 'QUIZ' | 'ASSIGNMENT'
  duration?: number
  order: number
  isCompleted: boolean
  isCurrent: boolean
  content?: {
    videoUrl?: string
    documentUrl?: string
    quizId?: string
  }
}

interface Course {
  id: string
  title: string
  description: string
  instructor: {
    id: string
    name: string
    image?: string
    bio?: string
  }
  sections: CourseSection[]
  progress: {
    completedTopics: number
    totalTopics: number
    completionPercentage: number
    timeSpent: number
    currentTopicId?: string
  }
  stats: {
    totalQuizzes: number
    completedQuizzes: number
    averageQuizScore: number
    discussionPosts: number
    certificates: number
  }
}

interface EnhancedCoursePlayerProps {
  course: Course
  onTopicComplete: (topicId: string) => void
  onQuizStart: (quizId: string) => void
  onDiscussionOpen: () => void
  onCertificateView: () => void
  className?: string
}

export function EnhancedCoursePlayer({
  course,
  onTopicComplete,
  onQuizStart,
  onDiscussionOpen,
  onCertificateView,
  className = ''
}: EnhancedCoursePlayerProps) {
  const [currentTopic, setCurrentTopic] = useState<CourseTopic | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeTab, setActiveTab] = useState<'content' | 'discussions' | 'progress'>('content')
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  // Find current topic
  useEffect(() => {
    const allTopics = course.sections.flatMap(section => section.topics)
    const current = allTopics.find(topic => topic.isCurrent) || allTopics[0]
    setCurrentTopic(current)
  }, [course])

  // Navigate to topic
  const navigateToTopic = (topicId: string) => {
    const allTopics = course.sections.flatMap(section => section.topics)
    const topic = allTopics.find(t => t.id === topicId)
    if (topic) {
      setCurrentTopic(topic)
    }
  }

  // Get next/previous topics
  const allTopics = course.sections.flatMap(section => section.topics)
  const currentIndex = currentTopic ? allTopics.findIndex(t => t.id === currentTopic.id) : -1
  const previousTopic = currentIndex > 0 ? allTopics[currentIndex - 1] : null
  const nextTopic = currentIndex < allTopics.length - 1 ? allTopics[currentIndex + 1] : null

  // Handle topic completion
  const handleTopicComplete = () => {
    if (currentTopic && !currentTopic.isCompleted) {
      onTopicComplete(currentTopic.id)
      toast.success('Topic completed!')
      
      // Auto-navigate to next topic
      if (nextTopic) {
        setTimeout(() => {
          setCurrentTopic(nextTopic)
        }, 1000)
      }
    }
  }

  return (
    <div className={`flex h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      {/* Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ x: -320 }}
            animate={{ x: 0 }}
            exit={{ x: -320 }}
            className="w-80 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-r border-gray-200 dark:border-gray-700 flex flex-col"
          >
            {/* Sidebar Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h2 className="font-semibold text-gray-900 dark:text-white truncate">
                  {course.title}
                </h2>
                <button
                  onClick={() => setSidebarOpen(false)}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Progress Bar */}
              <div className="mt-3">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span>Course Progress</span>
                  <span>{Math.round(course.progress.completionPercentage)}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-gradient-to-r from-violet-500 to-purple-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${course.progress.completionPercentage}%` }}
                    transition={{ duration: 1 }}
                  />
                </div>
              </div>
            </div>

            {/* Sidebar Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              {[
                { id: 'content', label: 'Content', icon: BookOpen },
                { id: 'discussions', label: 'Discussions', icon: MessageSquare },
                { id: 'progress', label: 'Progress', icon: BarChart3 }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex-1 flex items-center justify-center space-x-2 px-3 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'text-violet-600 dark:text-violet-400 border-b-2 border-violet-600 dark:border-violet-400'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </button>
                )
              })}
            </div>

            {/* Sidebar Content */}
            <div className="flex-1 overflow-y-auto">
              <AnimatePresence mode="wait">
                {activeTab === 'content' && (
                  <motion.div
                    key="content"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-4"
                  >
                    <CourseContentSidebar
                      sections={course.sections}
                      currentTopicId={currentTopic?.id}
                      onTopicSelect={navigateToTopic}
                    />
                  </motion.div>
                )}

                {activeTab === 'discussions' && (
                  <motion.div
                    key="discussions"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-4"
                  >
                    <DiscussionsSidebar
                      courseId={course.id}
                      onOpenDiscussions={onDiscussionOpen}
                    />
                  </motion.div>
                )}

                {activeTab === 'progress' && (
                  <motion.div
                    key="progress"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-4"
                  >
                    <ProgressSidebar
                      course={course}
                      onCertificateView={onCertificateView}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {!sidebarOpen && (
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                >
                  <Menu className="w-5 h-5" />
                </button>
              )}

              <div>
                <h1 className="font-semibold text-gray-900 dark:text-white">
                  {currentTopic?.title || 'Select a topic'}
                </h1>
                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{currentTopic?.duration ? `${currentTopic.duration} min` : 'No duration'}</span>
                  </div>
                  {currentTopic?.isCompleted && (
                    <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                      <CheckCircle className="w-3 h-3" />
                      <span>Completed</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Navigation */}
              <button
                onClick={() => previousTopic && setCurrentTopic(previousTopic)}
                disabled={!previousTopic}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>

              <button
                onClick={() => nextTopic && setCurrentTopic(nextTopic)}
                disabled={!nextTopic}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <ChevronRight className="w-5 h-5" />
              </button>

              {/* Complete Topic Button */}
              {currentTopic && !currentTopic.isCompleted && (
                <button
                  onClick={handleTopicComplete}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Mark Complete</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {currentTopic ? (
            <TopicContent
              topic={currentTopic}
              onQuizStart={onQuizStart}
              isPlaying={isPlaying}
              setIsPlaying={setIsPlaying}
              currentTime={currentTime}
              setCurrentTime={setCurrentTime}
              duration={duration}
              setDuration={setDuration}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Welcome to the Course
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Select a topic from the sidebar to begin learning
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Course Content Sidebar Component
interface CourseContentSidebarProps {
  sections: CourseSection[]
  currentTopicId?: string
  onTopicSelect: (topicId: string) => void
}

function CourseContentSidebar({ sections, currentTopicId, onTopicSelect }: CourseContentSidebarProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const getTopicIcon = (type: string) => {
    switch (type) {
      case 'VIDEO':
        return <Play className="w-4 h-4" />
      case 'DOCUMENT':
        return <FileText className="w-4 h-4" />
      case 'QUIZ':
        return <HelpCircle className="w-4 h-4" />
      case 'ASSIGNMENT':
        return <Target className="w-4 h-4" />
      default:
        return <BookOpen className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-2">
      {sections.map((section) => (
        <div key={section.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <button
            onClick={() => toggleSection(section.id)}
            className="w-full p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {expandedSections.has(section.id) ? (
                  <ChevronRight className="w-4 h-4 text-gray-500 transform rotate-90 transition-transform" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500 transition-transform" />
                )}
                <span className="font-medium text-gray-900 dark:text-white">
                  {section.title}
                </span>
              </div>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {section.topics.length} topics
              </span>
            </div>
          </button>

          <AnimatePresence>
            {expandedSections.has(section.id) && (
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                className="overflow-hidden"
              >
                <div className="p-2 space-y-1">
                  {section.topics.map((topic) => (
                    <button
                      key={topic.id}
                      onClick={() => onTopicSelect(topic.id)}
                      className={`w-full p-3 text-left rounded-lg transition-colors ${
                        currentTopicId === topic.id
                          ? 'bg-violet-100 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300'
                          : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`flex-shrink-0 ${
                          topic.type === 'VIDEO' ? 'text-red-500' :
                          topic.type === 'DOCUMENT' ? 'text-blue-500' :
                          topic.type === 'QUIZ' ? 'text-yellow-500' :
                          'text-green-500'
                        }`}>
                          {getTopicIcon(topic.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{topic.title}</div>
                          {topic.duration && (
                            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {topic.duration} min
                            </div>
                          )}
                        </div>

                        {topic.isCompleted && (
                          <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  )
}

// Topic Content Component (placeholder)
function TopicContent({ topic, onQuizStart, isPlaying, setIsPlaying }: any) {
  return (
    <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl border border-gray-200 dark:border-gray-700 p-6">
      {topic.type === 'VIDEO' && (
        <div className="aspect-video bg-black rounded-lg mb-6 flex items-center justify-center">
          <div className="text-center text-white">
            <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <p className="text-lg">Video Player</p>
            <p className="text-sm opacity-75">Video content would be displayed here</p>
          </div>
        </div>
      )}

      {topic.type === 'QUIZ' && (
        <div className="text-center py-12">
          <HelpCircle className="w-16 h-16 text-violet-500 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Quiz: {topic.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test your knowledge with this interactive quiz
          </p>
          <button
            onClick={() => topic.content?.quizId && onQuizStart(topic.content.quizId)}
            className="px-6 py-3 bg-gradient-to-r from-violet-500 to-purple-600 text-white rounded-lg hover:from-violet-600 hover:to-purple-700 transition-all duration-200"
          >
            Start Quiz
          </button>
        </div>
      )}

      {topic.type === 'DOCUMENT' && (
        <div className="prose dark:prose-invert max-w-none">
          <h2>{topic.title}</h2>
          <p>Document content would be displayed here...</p>
        </div>
      )}

      {topic.description && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">Description</h4>
          <p className="text-gray-600 dark:text-gray-400">{topic.description}</p>
        </div>
      )}
    </div>
  )
}

// Placeholder components
function DiscussionsSidebar({ courseId, onOpenDiscussions }: any) {
  return (
    <div className="space-y-4">
      <button
        onClick={onOpenDiscussions}
        className="w-full p-4 bg-violet-100 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300 rounded-lg hover:bg-violet-200 dark:hover:bg-violet-900/30 transition-colors"
      >
        <div className="flex items-center space-x-3">
          <MessageSquare className="w-5 h-5" />
          <span>Join Course Discussions</span>
        </div>
      </button>
      <div className="text-sm text-gray-600 dark:text-gray-400">
        Connect with classmates and ask questions about the course content.
      </div>
    </div>
  )
}

function ProgressSidebar({ course, onCertificateView }: any) {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <div className="text-3xl font-bold text-violet-600 dark:text-violet-400">
          {Math.round(course.progress.completionPercentage)}%
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">Complete</div>
      </div>

      <div className="space-y-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Topics Completed</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {course.progress.completedTopics}/{course.progress.totalTopics}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Quizzes Passed</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {course.stats.completedQuizzes}/{course.stats.totalQuizzes}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Average Score</span>
          <span className="font-medium text-gray-900 dark:text-white">
            {course.stats.averageQuizScore}%
          </span>
        </div>
      </div>

      {course.stats.certificates > 0 && (
        <button
          onClick={onCertificateView}
          className="w-full p-4 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-900/30 transition-colors"
        >
          <div className="flex items-center space-x-3">
            <Award className="w-5 h-5" />
            <span>View Certificate</span>
          </div>
        </button>
      )}
    </div>
  )
}
