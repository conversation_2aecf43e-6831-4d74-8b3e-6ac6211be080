import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'

// POST /api/courses/[courseId]/discussions/[discussionId]/replies/[replyId]/like - Toggle reply like
export const POST = createAPIHandler(
  {
    requireAuth: true
  },
  async (request: NextRequest, { user }) => {
    try {
      const urlParts = request.url.split('/')
      const replyId = urlParts.slice(-2, -1)[0]
      const discussionId = urlParts.slice(-4, -3)[0]
      const courseId = urlParts.slice(-6, -5)[0]

      if (!courseId || !discussionId || !replyId) {
        return APIResponse.error('Course ID, Discussion ID, and Reply ID are required', 400)
      }

      // Check if user has access to course
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      const course = await prisma.course.findUnique({
        where: { id: courseId },
        select: { instructorId: true }
      })

      const isInstructor = course?.instructorId === user.id
      const hasAccess = isInstructor || (enrollment?.status === 'ACTIVE')

      if (!hasAccess) {
        return APIResponse.error('Access denied', 403)
      }

      // Verify reply exists
      const reply = await prisma.discussionReply.findUnique({
        where: { 
          id: replyId,
          discussionId,
          isDeleted: false
        }
      })

      if (!reply) {
        return APIResponse.error('Reply not found', 404)
      }

      // Check if user already liked this reply
      const existingLike = await prisma.discussionReplyLike.findUnique({
        where: {
          userId_replyId: {
            userId: user.id,
            replyId
          }
        }
      })

      let isLiked: boolean
      let likeCount: number

      if (existingLike) {
        // Unlike - remove the like
        await prisma.discussionReplyLike.delete({
          where: { id: existingLike.id }
        })
        isLiked = false
      } else {
        // Like - create new like
        await prisma.discussionReplyLike.create({
          data: {
            userId: user.id,
            replyId
          }
        })
        isLiked = true
      }

      // Get updated like count
      likeCount = await prisma.discussionReplyLike.count({
        where: { replyId }
      })

      return APIResponse.success({
        message: isLiked ? 'Reply liked' : 'Reply unliked',
        isLiked,
        likeCount
      })

    } catch (error) {
      console.error('Error toggling reply like:', error)
      return APIResponse.error(
        'Failed to toggle like: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
