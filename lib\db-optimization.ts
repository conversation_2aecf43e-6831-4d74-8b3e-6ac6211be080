import { prisma } from '@/lib/prisma'
import { cache, CACHE_TTL, cacheUtils } from '@/lib/cache'

// Database query optimization utilities
export class DatabaseOptimizer {
  // Optimized course queries with caching and efficient includes
  static async getCourseById(courseId: string, includeDetails = false) {
    const cacheKey = cacheUtils.courseKey(courseId)
    
    // Try cache first
    const cached = await cache.get(cacheKey)
    if (cached) return cached

    const course = await prisma.course.findUnique({
      where: { id: courseId },
      include: includeDetails ? {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true,
            bio: true,
            expertise: true,
            isVerified: true
          }
        },
        sections: {
          where: { isDeleted: false },
          orderBy: { order: 'asc' },
          include: {
            topics: {
              where: { isDeleted: false },
              orderBy: { order: 'asc' },
              select: {
                id: true,
                title: true,
                type: true,
                duration: true,
                order: true,
                isPreview: true
              }
            }
          }
        },
        _count: {
          select: {
            enrollments: {
              where: { status: 'ACTIVE' }
            },
            reviews: true
          }
        }
      } : {
        instructor: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        _count: {
          select: {
            enrollments: {
              where: { status: 'ACTIVE' }
            },
            reviews: true
          }
        }
      }
    })

    if (course) {
      await cache.set(cacheKey, course, {
        ttl: CACHE_TTL.MEDIUM,
        tags: [`course:${courseId}`, 'courses']
      })
    }

    return course
  }

  // Optimized course list with pagination and filtering
  static async getCourses(filters: {
    page?: number
    limit?: number
    category?: string
    level?: string
    instructor?: string
    search?: string
    sortBy?: string
    featured?: boolean
  } = {}) {
    const {
      page = 1,
      limit = 20,
      category,
      level,
      instructor,
      search,
      sortBy = 'popularity',
      featured
    } = filters

    const cacheKey = cacheUtils.courseListKey(filters)
    
    // Try cache first
    const cached = await cache.get(cacheKey)
    if (cached) return cached

    // Build where conditions efficiently
    const whereConditions: any = {
      isPublished: true,
      isDeleted: false
    }

    if (category) whereConditions.category = category
    if (level) whereConditions.level = level
    if (featured) whereConditions.isFeatured = true
    if (instructor) {
      whereConditions.instructor = {
        name: { contains: instructor, mode: 'insensitive' }
      }
    }
    if (search) {
      whereConditions.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { hasSome: search.split(' ') } }
      ]
    }

    // Optimize sort conditions
    let orderBy: any = {}
    switch (sortBy) {
      case 'popularity':
        orderBy = [
          { enrollmentCount: 'desc' },
          { averageRating: 'desc' }
        ]
        break
      case 'rating':
        orderBy = [
          { averageRating: 'desc' },
          { reviewCount: 'desc' }
        ]
        break
      case 'newest':
        orderBy = { createdAt: 'desc' }
        break
      case 'price_low':
        orderBy = { price: 'asc' }
        break
      case 'price_high':
        orderBy = { price: 'desc' }
        break
      default:
        orderBy = { createdAt: 'desc' }
    }

    // Use cursor-based pagination for better performance on large datasets
    const courses = await prisma.course.findMany({
      where: whereConditions,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        title: true,
        description: true,
        shortDescription: true,
        thumbnailImage: true,
        price: true,
        originalPrice: true,
        currency: true,
        level: true,
        category: true,
        tags: true,
        language: true,
        estimatedDuration: true,
        averageRating: true,
        reviewCount: true,
        enrollmentCount: true,
        isFeatured: true,
        createdAt: true,
        updatedAt: true,
        instructor: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    // Get total count efficiently (only when needed)
    const total = await prisma.course.count({ where: whereConditions })

    const result = {
      courses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      }
    }

    // Cache the result
    await cache.set(cacheKey, result, {
      ttl: CACHE_TTL.SHORT,
      tags: ['courses', 'course-list']
    })

    return result
  }

  // Optimized user enrollment check
  static async checkUserEnrollment(userId: string, courseId: string) {
    const cacheKey = `enrollment:${userId}:${courseId}`
    
    const cached = await cache.get(cacheKey)
    if (cached !== null) return cached

    const enrollment = await prisma.courseEnrollment.findFirst({
      where: {
        userId,
        courseId,
        status: 'ACTIVE'
      },
      select: {
        id: true,
        status: true,
        enrolledAt: true,
        progress: true
      }
    })

    const result = !!enrollment
    await cache.set(cacheKey, result, {
      ttl: CACHE_TTL.SHORT,
      tags: [`user:${userId}`, `course:${courseId}`]
    })

    return result
  }

  // Batch operations for better performance
  static async batchGetCourses(courseIds: string[]) {
    // Check cache for each course
    const cachedCourses: any[] = []
    const uncachedIds: string[] = []

    for (const id of courseIds) {
      const cached = await cache.get(cacheUtils.courseKey(id))
      if (cached) {
        cachedCourses.push(cached)
      } else {
        uncachedIds.push(id)
      }
    }

    // Fetch uncached courses in batch
    let uncachedCourses: any[] = []
    if (uncachedIds.length > 0) {
      uncachedCourses = await prisma.course.findMany({
        where: {
          id: { in: uncachedIds },
          isPublished: true,
          isDeleted: false
        },
        include: {
          instructor: {
            select: {
              id: true,
              name: true,
              image: true
            }
          },
          _count: {
            select: {
              enrollments: {
                where: { status: 'ACTIVE' }
              },
              reviews: true
            }
          }
        }
      })

      // Cache the fetched courses
      for (const course of uncachedCourses) {
        await cache.set(cacheUtils.courseKey(course.id), course, {
          ttl: CACHE_TTL.MEDIUM,
          tags: [`course:${course.id}`, 'courses']
        })
      }
    }

    return [...cachedCourses, ...uncachedCourses]
  }

  // Optimized search with full-text search capabilities
  static async searchCourses(query: string, filters: any = {}) {
    const cacheKey = cacheUtils.searchKey(query, filters)
    
    const cached = await cache.get(cacheKey)
    if (cached) return cached

    // Use database full-text search if available, otherwise fallback to LIKE
    const searchConditions = {
      isPublished: true,
      isDeleted: false,
      OR: [
        { title: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
        { shortDescription: { contains: query, mode: 'insensitive' } },
        { tags: { hasSome: query.split(' ') } },
        {
          instructor: {
            name: { contains: query, mode: 'insensitive' }
          }
        }
      ]
    }

    const courses = await prisma.course.findMany({
      where: searchConditions,
      orderBy: [
        { enrollmentCount: 'desc' },
        { averageRating: 'desc' }
      ],
      take: filters.limit || 20,
      select: {
        id: true,
        title: true,
        description: true,
        shortDescription: true,
        thumbnailImage: true,
        price: true,
        currency: true,
        level: true,
        category: true,
        averageRating: true,
        reviewCount: true,
        enrollmentCount: true,
        instructor: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    await cache.set(cacheKey, courses, {
      ttl: CACHE_TTL.SHORT,
      tags: ['search', 'courses']
    })

    return courses
  }

  // Database connection pooling optimization
  static async optimizeConnections() {
    // This would configure connection pooling
    // Prisma handles this automatically, but we can monitor it
    const metrics = await prisma.$metrics.json()
    return metrics
  }

  // Query performance monitoring
  static async analyzeSlowQueries() {
    // This would analyze slow queries and suggest optimizations
    // Implementation would depend on database type and monitoring tools
    console.log('Analyzing slow queries...')
    
    // Example: Log queries that take longer than 1 second
    const slowQueries: any[] = []
    
    return {
      slowQueries,
      recommendations: [
        'Add indexes on frequently queried columns',
        'Use select to limit returned fields',
        'Implement proper pagination',
        'Use caching for frequently accessed data'
      ]
    }
  }

  // Index recommendations
  static getIndexRecommendations() {
    return [
      {
        table: 'courses',
        columns: ['category', 'level', 'isPublished'],
        type: 'composite',
        reason: 'Frequently used in course filtering'
      },
      {
        table: 'courses',
        columns: ['title'],
        type: 'text',
        reason: 'Full-text search on course titles'
      },
      {
        table: 'course_enrollments',
        columns: ['userId', 'courseId', 'status'],
        type: 'composite',
        reason: 'User enrollment lookups'
      },
      {
        table: 'notifications',
        columns: ['userId', 'isRead', 'createdAt'],
        type: 'composite',
        reason: 'User notification queries'
      }
    ]
  }
}

// Query builder for complex queries
export class QueryBuilder {
  private query: any = {}
  private includes: any = {}
  private orderBy: any = {}
  private pagination: { skip?: number; take?: number } = {}

  where(conditions: any) {
    this.query = { ...this.query, ...conditions }
    return this
  }

  include(relations: any) {
    this.includes = { ...this.includes, ...relations }
    return this
  }

  orderByField(field: string, direction: 'asc' | 'desc' = 'asc') {
    this.orderBy = { [field]: direction }
    return this
  }

  paginate(page: number, limit: number) {
    this.pagination = {
      skip: (page - 1) * limit,
      take: limit
    }
    return this
  }

  build() {
    return {
      where: this.query,
      include: this.includes,
      orderBy: this.orderBy,
      ...this.pagination
    }
  }
}

// Performance monitoring utilities
export const performanceMonitor = {
  queryTimes: new Map<string, number[]>(),

  startTimer(queryName: string) {
    return {
      end: () => {
        const endTime = Date.now()
        // Implementation would track query execution time
      }
    }
  },

  getAverageQueryTime(queryName: string) {
    const times = this.queryTimes.get(queryName) || []
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0
  },

  getSlowestQueries(limit = 10) {
    const queries = Array.from(this.queryTimes.entries())
      .map(([name, times]) => ({
        name,
        averageTime: times.reduce((a, b) => a + b, 0) / times.length,
        callCount: times.length
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, limit)

    return queries
  }
}
