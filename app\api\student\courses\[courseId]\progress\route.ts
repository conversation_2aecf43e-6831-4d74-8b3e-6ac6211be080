import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateProgressSchema = z.object({
  topicId: z.string().min(1, 'Topic ID is required'),
  isCompleted: z.boolean().optional(),
  timeSpent: z.number().int().min(0).optional(), // seconds
  lastPosition: z.number().int().min(0).optional() // for video resume
})

const updateVideoProgressSchema = z.object({
  contentId: z.string().min(1, 'Content ID is required'),
  watchedDuration: z.number().int().min(0), // seconds watched
  totalDuration: z.number().int().min(0), // total video duration
  lastPosition: z.number().int().min(0), // current playback position
  isCompleted: z.boolean().optional()
})

// GET /api/student/courses/[courseId]/progress - Get course progress
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT'
  },
  async (request: NextRequest, { user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      // Check if user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('Not enrolled in this course', 403)
      }

      // Get course structure with progress
      const course = await prisma.course.findUnique({
        where: { id: courseId },
        include: {
          sections: {
            where: { isPublished: true },
            orderBy: { order: 'asc' },
            include: {
              topics: {
                where: { isPublished: true },
                orderBy: { order: 'asc' },
                include: {
                  content: {
                    orderBy: { order: 'asc' }
                  },
                  progress: {
                    where: { userId: user.id }
                  }
                }
              }
            }
          }
        }
      })

      if (!course) {
        return APIResponse.error('Course not found', 404)
      }

      // Get video progress
      const videoProgress = await prisma.videoProgress.findMany({
        where: {
          userId: user.id,
          content: {
            topic: {
              section: {
                courseId
              }
            }
          }
        }
      })

      const videoProgressMap = new Map(
        videoProgress.map(vp => [vp.contentId, vp])
      )

      // Calculate progress metrics
      let totalTopics = 0
      let completedTopics = 0
      let totalTimeSpent = 0

      const sectionsWithProgress = course.sections.map(section => {
        const topicsWithProgress = section.topics.map(topic => {
          totalTopics++
          const topicProgress = topic.progress[0] // Should be only one per user
          
          if (topicProgress?.isCompleted) {
            completedTopics++
          }
          
          totalTimeSpent += topicProgress?.timeSpent || 0

          // Add video progress to content
          const contentWithProgress = topic.content.map(content => ({
            ...content,
            videoProgress: videoProgressMap.get(content.id) || null
          }))

          return {
            ...topic,
            content: contentWithProgress,
            progress: topicProgress || null,
            isCompleted: topicProgress?.isCompleted || false,
            timeSpent: topicProgress?.timeSpent || 0,
            lastPosition: topicProgress?.lastPosition || 0
          }
        })

        const sectionCompletedTopics = topicsWithProgress.filter(t => t.isCompleted).length
        const sectionProgress = topicsWithProgress.length > 0 
          ? (sectionCompletedTopics / topicsWithProgress.length) * 100 
          : 0

        return {
          ...section,
          topics: topicsWithProgress,
          completedTopics: sectionCompletedTopics,
          totalTopics: topicsWithProgress.length,
          progress: Math.round(sectionProgress)
        }
      })

      const overallProgress = totalTopics > 0 ? (completedTopics / totalTopics) * 100 : 0

      // Update enrollment progress
      await prisma.courseEnrollment.update({
        where: { id: enrollment.id },
        data: {
          progress: overallProgress,
          completedTopics,
          totalTopics,
          lastAccessedAt: new Date(),
          ...(overallProgress === 100 && !enrollment.completedAt ? {
            completedAt: new Date(),
            status: 'COMPLETED'
          } : {})
        }
      })

      return APIResponse.success({
        courseId,
        enrollment: {
          id: enrollment.id,
          status: enrollment.status,
          enrolledAt: enrollment.enrolledAt,
          lastAccessedAt: new Date(),
          completedAt: enrollment.completedAt
        },
        progress: {
          overall: Math.round(overallProgress),
          completedTopics,
          totalTopics,
          totalTimeSpent: Math.round(totalTimeSpent / 60), // Convert to minutes
          sections: sectionsWithProgress
        }
      })

    } catch (error) {
      console.error('Error fetching course progress:', error)
      return APIResponse.error(
        'Failed to fetch progress: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// POST /api/student/courses/[courseId]/progress - Update topic progress
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: updateProgressSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      const { topicId, isCompleted, timeSpent, lastPosition } = validatedBody

      // Check if user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('Not enrolled in this course', 403)
      }

      // Verify topic belongs to course
      const topic = await prisma.courseTopic.findFirst({
        where: {
          id: topicId,
          section: {
            courseId
          }
        }
      })

      if (!topic) {
        return APIResponse.error('Topic not found in this course', 404)
      }

      // Update or create progress
      const progress = await prisma.courseProgress.upsert({
        where: {
          userId_topicId: {
            userId: user.id,
            topicId
          }
        },
        update: {
          ...(isCompleted !== undefined && { isCompleted }),
          ...(timeSpent !== undefined && { timeSpent: { increment: timeSpent } }),
          ...(lastPosition !== undefined && { lastPosition }),
          ...(isCompleted && { completedAt: new Date() })
        },
        create: {
          userId: user.id,
          topicId,
          enrollmentId: enrollment.id,
          isCompleted: isCompleted || false,
          timeSpent: timeSpent || 0,
          lastPosition: lastPosition || 0,
          ...(isCompleted && { completedAt: new Date() })
        }
      })

      return APIResponse.success({
        message: 'Progress updated successfully',
        progress
      })

    } catch (error) {
      console.error('Error updating progress:', error)
      return APIResponse.error(
        'Failed to update progress: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// PUT /api/student/courses/[courseId]/progress - Update video progress
export const PUT = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateBody: updateVideoProgressSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const courseId = request.url.split('/').slice(-2, -1)[0]

      if (!courseId) {
        return APIResponse.error('Course ID is required', 400)
      }

      const { contentId, watchedDuration, totalDuration, lastPosition, isCompleted } = validatedBody

      // Check if user is enrolled
      const enrollment = await prisma.courseEnrollment.findUnique({
        where: {
          userId_courseId: {
            userId: user.id,
            courseId
          }
        }
      })

      if (!enrollment) {
        return APIResponse.error('Not enrolled in this course', 403)
      }

      // Verify content belongs to course
      const content = await prisma.courseContent.findFirst({
        where: {
          id: contentId,
          topic: {
            section: {
              courseId
            }
          }
        }
      })

      if (!content) {
        return APIResponse.error('Content not found in this course', 404)
      }

      // Calculate completion based on watch percentage
      const watchPercentage = totalDuration > 0 ? (watchedDuration / totalDuration) * 100 : 0
      const autoCompleted = watchPercentage >= 90 // Consider 90%+ as completed

      // Update video progress
      const videoProgress = await prisma.videoProgress.upsert({
        where: {
          userId_contentId: {
            userId: user.id,
            contentId
          }
        },
        update: {
          watchedDuration,
          totalDuration,
          lastPosition,
          isCompleted: isCompleted !== undefined ? isCompleted : autoCompleted,
          ...(isCompleted || autoCompleted ? { completedAt: new Date() } : {})
        },
        create: {
          userId: user.id,
          contentId,
          watchedDuration,
          totalDuration,
          lastPosition,
          isCompleted: isCompleted !== undefined ? isCompleted : autoCompleted,
          ...(isCompleted || autoCompleted ? { completedAt: new Date() } : {})
        }
      })

      return APIResponse.success({
        message: 'Video progress updated successfully',
        videoProgress,
        watchPercentage: Math.round(watchPercentage)
      })

    } catch (error) {
      console.error('Error updating video progress:', error)
      return APIResponse.error(
        'Failed to update video progress: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)
