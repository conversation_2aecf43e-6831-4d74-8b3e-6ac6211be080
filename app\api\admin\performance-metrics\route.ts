import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON>and<PERSON>, APIResponse } from '@/lib/api-middleware'
import { prisma } from '@/lib/prisma'
import { cache } from '@/lib/cache'
import os from 'os'

// GET /api/admin/performance-metrics - Get system performance metrics
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: ['ADMIN']
  },
  async (request: NextRequest, { user }) => {
    try {
      // Server metrics
      const serverMetrics = await getServerMetrics()
      
      // Database metrics
      const databaseMetrics = await getDatabaseMetrics()
      
      // Application metrics
      const applicationMetrics = await getApplicationMetrics()
      
      // Cache metrics
      const cacheMetrics = await getCacheMetrics()
      
      // Performance alerts
      const alerts = await getPerformanceAlerts()

      const metrics = {
        server: serverMetrics,
        database: databaseMetrics,
        application: applicationMetrics,
        cache: cacheMetrics,
        timestamp: new Date().toISOString()
      }

      return APIResponse.success({
        metrics,
        alerts,
        summary: {
          overallHealth: calculateOverallHealth(metrics),
          criticalIssues: alerts.filter(a => a.type === 'error' && !a.resolved).length,
          warnings: alerts.filter(a => a.type === 'warning' && !a.resolved).length
        }
      })

    } catch (error) {
      console.error('Error fetching performance metrics:', error)
      return APIResponse.error(
        'Failed to fetch performance metrics: ' + (error instanceof Error ? error.message : 'Unknown error'),
        500
      )
    }
  }
)

// Get server metrics
async function getServerMetrics() {
  const uptime = process.uptime()
  const memoryUsage = process.memoryUsage()
  const cpuUsage = await getCPUUsage()
  
  // Calculate memory usage percentage
  const totalMemory = os.totalmem()
  const freeMemory = os.freemem()
  const usedMemory = totalMemory - freeMemory
  const memoryUsagePercent = Math.round((usedMemory / totalMemory) * 100)

  // Simulate disk usage (in production, you'd get this from the actual filesystem)
  const diskUsage = Math.round(Math.random() * 30 + 40) // 40-70%

  // Calculate average response time from recent requests
  const responseTime = await getAverageResponseTime()

  return {
    uptime: Math.round(uptime),
    cpuUsage: Math.round(cpuUsage),
    memoryUsage: memoryUsagePercent,
    diskUsage,
    responseTime
  }
}

// Get CPU usage
async function getCPUUsage(): Promise<number> {
  return new Promise((resolve) => {
    const startUsage = process.cpuUsage()
    const startTime = Date.now()

    setTimeout(() => {
      const endUsage = process.cpuUsage(startUsage)
      const endTime = Date.now()
      
      const userTime = endUsage.user / 1000 // Convert to milliseconds
      const systemTime = endUsage.system / 1000
      const totalTime = endTime - startTime
      
      const cpuPercent = ((userTime + systemTime) / totalTime) * 100
      resolve(Math.min(100, Math.max(0, cpuPercent)))
    }, 100)
  })
}

// Get average response time
async function getAverageResponseTime(): Promise<number> {
  // In production, this would come from monitoring tools or logs
  // For now, simulate based on system load
  const baseResponseTime = 150
  const variance = Math.random() * 100
  return Math.round(baseResponseTime + variance)
}

// Get database metrics
async function getDatabaseMetrics() {
  try {
    // Get database connection info
    const connectionCount = await getDatabaseConnectionCount()
    
    // Simulate query performance metrics
    const queryTime = Math.round(Math.random() * 50 + 20) // 20-70ms
    const slowQueries = Math.round(Math.random() * 5) // 0-5 slow queries
    
    // Calculate cache hit rate
    const cacheHitRate = Math.round(Math.random() * 20 + 75) // 75-95%

    return {
      connectionCount,
      queryTime,
      slowQueries,
      cacheHitRate
    }
  } catch (error) {
    console.error('Error getting database metrics:', error)
    return {
      connectionCount: 0,
      queryTime: 0,
      slowQueries: 0,
      cacheHitRate: 0
    }
  }
}

// Get database connection count
async function getDatabaseConnectionCount(): Promise<number> {
  try {
    // This would depend on your database setup
    // For Prisma with PostgreSQL, you might query pg_stat_activity
    // For now, simulate a reasonable number
    return Math.round(Math.random() * 20 + 5) // 5-25 connections
  } catch (error) {
    return 0
  }
}

// Get application metrics
async function getApplicationMetrics() {
  try {
    // Get active users (users with activity in last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    const activeUsers = await prisma.user.count({
      where: {
        lastActiveAt: {
          gte: fiveMinutesAgo
        }
      }
    })

    // Simulate requests per minute
    const requestsPerMinute = Math.round(Math.random() * 500 + 100) // 100-600 RPM

    // Simulate error rate
    const errorRate = Math.round((Math.random() * 2 + 0.1) * 100) / 100 // 0.1-2.1%

    // Simulate average load time
    const averageLoadTime = Math.round(Math.random() * 1000 + 500) // 500-1500ms

    return {
      activeUsers,
      requestsPerMinute,
      errorRate,
      averageLoadTime
    }
  } catch (error) {
    console.error('Error getting application metrics:', error)
    return {
      activeUsers: 0,
      requestsPerMinute: 0,
      errorRate: 0,
      averageLoadTime: 0
    }
  }
}

// Get cache metrics
async function getCacheMetrics() {
  try {
    const stats = await cache.getStats()
    
    const hitRate = Math.round(Math.random() * 20 + 75) // 75-95%
    const missRate = 100 - hitRate

    return {
      hitRate,
      missRate,
      keyCount: stats.keyCount,
      memoryUsage: stats.memoryUsage || 'Unknown',
      connected: stats.connected
    }
  } catch (error) {
    console.error('Error getting cache metrics:', error)
    return {
      hitRate: 0,
      missRate: 100,
      keyCount: 0,
      memoryUsage: 'Unknown',
      connected: false
    }
  }
}

// Get performance alerts
async function getPerformanceAlerts() {
  const alerts = []

  // Check for high CPU usage
  const serverMetrics = await getServerMetrics()
  if (serverMetrics.cpuUsage > 90) {
    alerts.push({
      id: 'cpu-high',
      type: 'error' as const,
      title: 'High CPU Usage',
      message: `CPU usage is at ${serverMetrics.cpuUsage}%`,
      timestamp: new Date(),
      resolved: false
    })
  } else if (serverMetrics.cpuUsage > 70) {
    alerts.push({
      id: 'cpu-warning',
      type: 'warning' as const,
      title: 'Elevated CPU Usage',
      message: `CPU usage is at ${serverMetrics.cpuUsage}%`,
      timestamp: new Date(),
      resolved: false
    })
  }

  // Check for high memory usage
  if (serverMetrics.memoryUsage > 95) {
    alerts.push({
      id: 'memory-high',
      type: 'error' as const,
      title: 'High Memory Usage',
      message: `Memory usage is at ${serverMetrics.memoryUsage}%`,
      timestamp: new Date(),
      resolved: false
    })
  } else if (serverMetrics.memoryUsage > 80) {
    alerts.push({
      id: 'memory-warning',
      type: 'warning' as const,
      title: 'Elevated Memory Usage',
      message: `Memory usage is at ${serverMetrics.memoryUsage}%`,
      timestamp: new Date(),
      resolved: false
    })
  }

  // Check for slow response times
  if (serverMetrics.responseTime > 1000) {
    alerts.push({
      id: 'response-slow',
      type: 'warning' as const,
      title: 'Slow Response Times',
      message: `Average response time is ${serverMetrics.responseTime}ms`,
      timestamp: new Date(),
      resolved: false
    })
  }

  // Check database performance
  const dbMetrics = await getDatabaseMetrics()
  if (dbMetrics.slowQueries > 10) {
    alerts.push({
      id: 'db-slow-queries',
      type: 'warning' as const,
      title: 'High Number of Slow Queries',
      message: `${dbMetrics.slowQueries} slow queries detected`,
      timestamp: new Date(),
      resolved: false
    })
  }

  // Check cache performance
  const cacheMetrics = await getCacheMetrics()
  if (cacheMetrics.hitRate < 60) {
    alerts.push({
      id: 'cache-low-hit-rate',
      type: 'warning' as const,
      title: 'Low Cache Hit Rate',
      message: `Cache hit rate is only ${cacheMetrics.hitRate}%`,
      timestamp: new Date(),
      resolved: false
    })
  }

  return alerts
}

// Calculate overall system health score
function calculateOverallHealth(metrics: any): number {
  let score = 100

  // CPU usage impact
  if (metrics.server.cpuUsage > 90) score -= 30
  else if (metrics.server.cpuUsage > 70) score -= 15

  // Memory usage impact
  if (metrics.server.memoryUsage > 95) score -= 25
  else if (metrics.server.memoryUsage > 80) score -= 10

  // Response time impact
  if (metrics.server.responseTime > 1000) score -= 20
  else if (metrics.server.responseTime > 500) score -= 10

  // Database performance impact
  if (metrics.database.queryTime > 500) score -= 15
  else if (metrics.database.queryTime > 100) score -= 5

  // Cache performance impact
  if (metrics.cache.hitRate < 60) score -= 10
  else if (metrics.cache.hitRate < 80) score -= 5

  // Application error rate impact
  if (metrics.application.errorRate > 5) score -= 20
  else if (metrics.application.errorRate > 1) score -= 10

  return Math.max(0, Math.min(100, score))
}
